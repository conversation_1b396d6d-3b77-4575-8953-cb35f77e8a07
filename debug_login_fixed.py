#!/usr/bin/env python3
"""
调试脚本：测试修复后的Kaipoke登录流程
验证错误页面处理和登录流程是否正常工作
"""

import asyncio
import os
import sys
from pathlib import Path
from dotenv import load_dotenv

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 加载环境变量
load_dotenv()

from logger_config import logger
from core.browser.browser_manager import browser_manager
from core.selector_executor import SelectorExecutor

async def test_fixed_login():
    """测试修复后的登录流程"""
    logger.info("🧪 开始测试修复后的Kaipoke登录流程...")
    
    # 获取登录信息
    corporation_id = os.getenv('KAIPOKE_CORPORATION_ID')
    member_login_id = os.getenv('KAIPOKE_MEMBER_LOGIN_ID')
    password = os.getenv('KAIPOKE_PASSWORD')
    
    if not all([corporation_id, member_login_id, password]):
        logger.error("❌ 登录信息不完整，请检查环境变量")
        return False
    
    logger.info(f"使用登录信息: 法人ID={corporation_id}, 成员ID={member_login_id}")
    
    try:
        # 启动浏览器
        await browser_manager.start_browser(headless=False)  # 使用可视化模式便于观察
        page = await browser_manager.get_page()
        
        # 创建选择器执行器
        selector_executor = SelectorExecutor(page)
        
        # 访问登录页面（错误页面URL）
        login_url = "https://r.kaipoke.biz/kaipokebiz//nonmember/error.html"
        logger.info(f"访问登录页面: {login_url}")
        await page.goto(login_url, wait_until='networkidle', timeout=60000)
        
        # 等待页面加载
        await page.wait_for_timeout(2000)
        
        # 保存错误页面截图
        try:
            await page.screenshot(path='/tmp/kaipoke_error_page.png', full_page=True)
            logger.info("📸 错误页面截图已保存: /tmp/kaipoke_error_page.png")
        except:
            pass
        
        # 执行修复后的登录流程
        logger.info("🔐 开始执行修复后的登录流程...")
        login_success = await selector_executor.execute_kaipoke_login(
            corporation_id, member_login_id, password
        )
        
        if login_success:
            logger.info("✅ 登录测试成功！")
            
            # 保存登录后页面截图
            try:
                await page.screenshot(path='/tmp/kaipoke_after_login.png', full_page=True)
                logger.info("📸 登录后页面截图已保存: /tmp/kaipoke_after_login.png")
            except:
                pass
            
            # 等待一下，让用户看到结果
            await page.wait_for_timeout(5000)
            
            # 测试是否能找到レセプト菜单
            try:
                receipt_menu_exists = await page.locator('.mainCtg li:nth-of-type(1) a').count()
                if receipt_menu_exists > 0:
                    logger.info("✅ 登录成功确认：レセプト菜单已出现")
                    
                    # 尝试点击レセプト菜单
                    await page.click('.mainCtg li:nth-of-type(1) a', timeout=5000)
                    await page.wait_for_timeout(2000)
                    logger.info("✅ レセプト菜单点击成功")
                    
                    # 保存点击后页面截图
                    try:
                        await page.screenshot(path='/tmp/kaipoke_receipt_page.png', full_page=True)
                        logger.info("📸 レセプト页面截图已保存: /tmp/kaipoke_receipt_page.png")
                    except:
                        pass
                    
                else:
                    logger.warning("⚠️ レセプト菜单未出现，但登录可能仍然成功")
            except Exception as e:
                logger.warning(f"检查レセプト菜单时出错: {e}")
            
            return True
        else:
            logger.error("❌ 登录测试失败")
            
            # 保存失败页面截图
            try:
                await page.screenshot(path='/tmp/kaipoke_login_failed.png', full_page=True)
                logger.info("📸 登录失败页面截图已保存: /tmp/kaipoke_login_failed.png")
            except:
                pass
            
            return False
        
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}", exc_info=True)
        return False
    finally:
        try:
            # 等待一下再关闭浏览器，便于观察结果
            await asyncio.sleep(3)
            await browser_manager.close_browser()
        except:
            pass

async def main():
    """主函数"""
    success = await test_fixed_login()
    if success:
        logger.info("🎉 修复后的登录测试完成！")
    else:
        logger.error("❌ 修复后的登录测试失败")
    
    return success

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
