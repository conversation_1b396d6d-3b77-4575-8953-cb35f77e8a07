#!/usr/bin/env python3
"""
测试脚本：验证 kaipoke_form_download 工作流的修复
主要测试：
1. 同一据点不同任务的选择器差异修复
2. A模板执行后的URL返回问题修复
"""

import asyncio
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from workflows.kaipoke_form_download import template_a_flow, template_c_flow
from configs.workflows import load_workflows

def test_url_correction():
    """测试URL修正逻辑"""
    print("🧪 测试URL修正逻辑...")
    
    # 测试A模板的URL修正
    test_cases = [
        {
            'element_text': '訪問看護/4660190861',
            'original_url': 'https://r.kaipoke.biz/kaipokebiz/common/MEM090002.do?conversationContext=4',
            'expected_url': 'https://r.kaipoke.biz/kaipokebiz/common/MEM090002.do?conversationContext=9',
            'description': '訪問看護据点A模板URL修正'
        },
        {
            'element_text': '訪問介護/4670105586',
            'original_url': 'https://r.kaipoke.biz/kaipokebiz/common/MEM090002.do?conversationContext=4',
            'expected_url': 'https://r.kaipoke.biz/kaipokebiz/common/MEM090002.do?conversationContext=4',
            'description': '非訪問看護据点不应修正URL'
        }
    ]
    
    for case in test_cases:
        # 模拟URL修正逻辑
        element_text = case['element_text']
        task_list_url = case['original_url']
        
        if "訪問看護" in element_text and task_list_url:
            corrected_url = task_list_url.replace("conversationContext=4", "conversationContext=9")
        else:
            corrected_url = task_list_url
            
        if corrected_url == case['expected_url']:
            print(f"✅ {case['description']}: 通过")
        else:
            print(f"❌ {case['description']}: 失败")
            print(f"   期望: {case['expected_url']}")
            print(f"   实际: {corrected_url}")

def test_flow_transition_logic():
    """测试流程转换逻辑"""
    print("\n🧪 测试流程转换逻辑...")
    
    test_cases = [
        {
            'element_text': '訪問看護/4660190861',
            'current_flow_type': 'A',
            'new_flow_type': 'B',
            'current_url': 'https://r.kaipoke.biz/kaipokebiz/common/MEM090002.do?conversationContext=9',
            'should_reset': False,
            'description': '訪問看護 A->B 转换应保持URL'
        },
        {
            'element_text': '訪問看護/4060391200',
            'current_flow_type': 'C',
            'new_flow_type': 'B+C',
            'current_url': 'https://r.kaipoke.biz/kaipokebiz/common/MEM090002.do?conversationContext=9',
            'should_reset': False,
            'description': '訪問看護 C->B+C 转换应保持URL'
        },
        {
            'element_text': '訪問介護/4670105586',
            'current_flow_type': 'A',
            'new_flow_type': 'B',
            'current_url': 'https://r.kaipoke.biz/kaipokebiz/common/MEM090002.do?conversationContext=4',
            'should_reset': True,
            'description': '非訪問看護据点应重置导航'
        }
    ]
    
    for case in test_cases:
        # 模拟流程转换逻辑
        element_text = case['element_text']
        current_flow_type = case['current_flow_type']
        new_flow_type = case['new_flow_type']
        current_task_list_url = case['current_url']
        
        should_reset = not ("訪問看護" in element_text and 
                           current_flow_type in ['A', 'C'] and 
                           new_flow_type in ['B', 'B+C'] and 
                           current_task_list_url and "conversationContext=9" in current_task_list_url)
        
        if should_reset == case['should_reset']:
            print(f"✅ {case['description']}: 通过")
        else:
            print(f"❌ {case['description']}: 失败")
            print(f"   期望重置: {case['should_reset']}")
            print(f"   实际重置: {should_reset}")

def test_task_configuration():
    """测试任务配置"""
    print("\n🧪 测试任务配置...")
    
    try:
        workflows = load_workflows()
        kaipoke_config = workflows.get('kaipoke_form_download', {})
        tasks = kaipoke_config.get('tasks', [])
        
        # 查找訪問看護相关任务
        visiting_nurse_tasks = [task for task in tasks if "訪問看護" in task.get('element_text', '')]
        
        print(f"找到 {len(visiting_nurse_tasks)} 个訪問看護任务")
        
        # 按据点分组
        facilities = {}
        for task in visiting_nurse_tasks:
            element_text = task.get('element_text', '')
            if element_text not in facilities:
                facilities[element_text] = []
            facilities[element_text].append(task)
        
        for facility, facility_tasks in facilities.items():
            print(f"\n📍 据点: {facility}")
            for task in facility_tasks:
                task_id = task.get('task_id', 'N/A')
                flow_type = task.get('flow_type', 'N/A')
                filename = task.get('output_filename', 'N/A')
                print(f"  - Task {task_id}: {flow_type} -> {filename}")
        
        print("\n✅ 任务配置加载成功")
        
    except Exception as e:
        print(f"❌ 任务配置测试失败: {e}")

def main():
    """主测试函数"""
    print("🚀 开始测试 kaipoke_form_download 工作流修复...")
    
    test_url_correction()
    test_flow_transition_logic()
    test_task_configuration()
    
    print("\n🎉 测试完成！")

if __name__ == "__main__":
    main()
