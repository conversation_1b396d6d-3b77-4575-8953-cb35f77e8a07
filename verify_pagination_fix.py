#!/usr/bin/env python3
"""
验证分页修复的简单脚本（不依赖外部库）
"""

import os
import re

def check_selectors_config():
    """检查选择器配置文件的修改"""
    
    print("🔍 检查选择器配置文件...")
    
    config_file = 'configs/selectors.yaml'
    
    if not os.path.exists(config_file):
        print(f"❌ 配置文件不存在: {config_file}")
        return False
    
    with open(config_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 查找next_page_button配置
    next_button_section = re.search(
        r'next_page_button:\s*\n((?:\s*-.*\n)*)', 
        content, 
        re.MULTILINE
    )
    
    if next_button_section:
        selectors = next_button_section.group(1)
        print("📄 下一页按钮选择器:")
        
        # 提取选择器
        selector_lines = re.findall(r'-\s*"([^"]+)"', selectors)
        if not selector_lines:
            selector_lines = re.findall(r'-\s*([^\n]+)', selectors)
        
        improvements = []
        
        for i, selector in enumerate(selector_lines, 1):
            selector = selector.strip()
            print(f"   {i}. {selector}")
            
            # 分析改进
            if '.pager-btm' in selector:
                improvements.append("✅ 限定在分页区域")
            if ':not(' in selector and 'month' in selector:
                improvements.append("✅ 排除月份链接")
            if '.next02' in selector or '.next' in selector:
                improvements.append("✅ 包含next关键词")
        
        print("\n🎯 检测到的改进:")
        for improvement in set(improvements):
            print(f"   {improvement}")
    
    else:
        print("❌ 未找到next_page_button配置")
        return False
    
    return True

def check_workflow_modifications():
    """检查工作流文件的修改"""
    
    print("\n🔍 检查工作流文件修改...")
    
    workflow_file = 'workflows/kaipoke_performance_report.py'
    
    if not os.path.exists(workflow_file):
        print(f"❌ 工作流文件不存在: {workflow_file}")
        return False
    
    with open(workflow_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查关键修改
    checks = [
        ('verify_user_list_page_state', '页面状态验证函数'),
        ('防止月份更改', '修复注释'),
        ('严格验证：确保在用户列表页面', '页面验证逻辑'),
        ('检查分页区域是否存在', '分页区域检查'),
        ('更精确地检查下一页按钮', '精确按钮检查'),
        ('验证按钮是否可点击', '按钮状态验证'),
        ('意外跳转到月份', '月份跳转检查'),
        ('分页前最终安全检查', '安全检查'),
    ]
    
    found_modifications = []
    
    for keyword, description in checks:
        if keyword in content:
            found_modifications.append(description)
            print(f"   ✅ {description}: 已添加")
        else:
            print(f"   ⚠️ {description}: 未找到")
    
    # 检查函数定义
    functions = [
        'verify_user_list_page_state',
        'navigate_to_next_page',
    ]
    
    print("\n🔍 检查函数定义:")
    for func in functions:
        if f"async def {func}" in content:
            print(f"   ✅ 函数 {func}: 已定义")
        else:
            print(f"   ❌ 函数 {func}: 未找到")
    
    return len(found_modifications) > 0

def analyze_fix_effectiveness():
    """分析修复效果"""
    
    print("\n📊 分析修复效果...")
    
    # 读取选择器配置
    try:
        with open('configs/selectors.yaml', 'r', encoding='utf-8') as f:
            config_content = f.read()
    except:
        print("❌ 无法读取选择器配置")
        return False
    
    # 分析选择器精确性
    precision_score = 0
    max_score = 5
    
    if '.pager-btm' in config_content:
        precision_score += 1
        print("   ✅ 分页区域限定 (+1)")
    
    if ':not(' in config_content and 'month' in config_content:
        precision_score += 1
        print("   ✅ 月份链接排除 (+1)")
    
    if '.next02 a' in config_content:
        precision_score += 1
        print("   ✅ 精确next02选择器 (+1)")
    
    if 'contains(' in config_content:
        precision_score += 1
        print("   ✅ 文本内容匹配 (+1)")
    
    # 检查工作流修改
    try:
        with open('workflows/kaipoke_performance_report.py', 'r', encoding='utf-8') as f:
            workflow_content = f.read()
        
        if 'verify_user_list_page_state' in workflow_content:
            precision_score += 1
            print("   ✅ 页面状态验证 (+1)")
    except:
        print("   ⚠️ 无法检查工作流修改")
    
    print(f"\n🎯 修复精确性评分: {precision_score}/{max_score}")
    
    if precision_score >= 4:
        print("✅ 修复质量: 优秀")
    elif precision_score >= 3:
        print("✅ 修复质量: 良好")
    elif precision_score >= 2:
        print("⚠️ 修复质量: 一般")
    else:
        print("❌ 修复质量: 需要改进")
    
    return precision_score >= 3

def main():
    """主函数"""
    
    print("🚀 开始验证分页修复...")
    print("=" * 50)
    
    # 检查各个组件
    config_ok = check_selectors_config()
    workflow_ok = check_workflow_modifications()
    effectiveness_ok = analyze_fix_effectiveness()
    
    print("\n" + "=" * 50)
    print("📋 修复验证总结:")
    
    if config_ok:
        print("✅ 选择器配置: 已修复")
    else:
        print("❌ 选择器配置: 需要检查")
    
    if workflow_ok:
        print("✅ 工作流逻辑: 已增强")
    else:
        print("❌ 工作流逻辑: 需要检查")
    
    if effectiveness_ok:
        print("✅ 修复效果: 预期良好")
    else:
        print("⚠️ 修复效果: 可能需要进一步调整")
    
    print("\n🎯 关键修复点:")
    print("1. 下一页选择器限定在 .pager-btm 区域内")
    print("2. 排除包含 'month' 的链接")
    print("3. 增加页面状态验证函数")
    print("4. 添加分页前安全检查")
    print("5. 验证分页后没有意外跳转")
    
    print("\n📝 测试建议:")
    print("1. 运行修复后的工作流")
    print("2. 观察日志中的选择器使用情况")
    print("3. 确认不再出现月份更改问题")
    print("4. 验证分页功能正常工作")
    
    overall_success = config_ok and workflow_ok and effectiveness_ok
    
    if overall_success:
        print("\n🎉 修复验证通过！")
    else:
        print("\n⚠️ 修复验证发现问题，请检查相关文件")
    
    return overall_success

if __name__ == "__main__":
    main()
