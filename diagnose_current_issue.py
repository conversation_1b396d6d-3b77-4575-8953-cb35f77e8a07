#!/usr/bin/env python3
"""
诊断当前kaipoke_tennki登录问题
检查会话状态、弹窗、表单状态等
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from logger_config import logger
from core.browser.browser_manager import BrowserManager
from core.rpa_tools.kaipoke_login_service import kaipoke_login_with_env

async def diagnose_current_issue():
    """诊断当前问题"""
    logger.info("🔍 开始诊断当前kaipoke_tennki登录问题...")
    
    browser_manager = BrowserManager()
    
    try:
        # 1. 启动浏览器
        logger.info("🌐 启动浏览器...")
        page = await browser_manager.start_browser(headless=False)
        
        # 2. 尝试登录Kaipoke
        logger.info("🔑 尝试登录Kaipoke...")
        login_success = await kaipoke_login_with_env(
            page,
            'KAIPOKE_CORPORATION_ID',
            'KAIPOKE_MEMBER_LOGIN_ID',
            'KAIPOKE_PASSWORD',
            'https://r.kaipoke.biz/kaipokebiz/login/COM020102.do'
        )
        
        if not login_success:
            logger.error("❌ Kaipoke登录失败 - 这可能是问题的根源")
            return
        
        logger.info("✅ Kaipoke登录成功")
        
        # 3. 检查页面状态
        logger.info("🔍 检查页面状态...")
        page_info = await page.evaluate("""
            () => {
                return {
                    url: window.location.href,
                    title: document.title,
                    readyState: document.readyState,
                    hasModals: document.querySelectorAll('.modal').length,
                    hasPopups: document.querySelectorAll('[id*="karte"], [class*="popup"], [class*="notification"]').length,
                    hasRegistModal: document.querySelector('#registModal') !== null,
                    registModalVisible: document.querySelector('#registModal') ? 
                        window.getComputedStyle(document.querySelector('#registModal')).display !== 'none' : false
                }
            }
        """)
        
        logger.info(f"📊 页面状态:")
        logger.info(f"   URL: {page_info['url']}")
        logger.info(f"   标题: {page_info['title']}")
        logger.info(f"   就绪状态: {page_info['readyState']}")
        logger.info(f"   模态框数量: {page_info['hasModals']}")
        logger.info(f"   弹窗数量: {page_info['hasPopups']}")
        logger.info(f"   注册模态框存在: {page_info['hasRegistModal']}")
        logger.info(f"   注册模态框可见: {page_info['registModalVisible']}")
        
        # 4. 导航到訪問看護页面
        logger.info("🏥 导航到訪問看護页面...")
        try:
            # 点击レセプト菜单
            await page.click('.mainCtg li:nth-of-type(1) a')
            await page.wait_for_load_state("load")
            
            # 选择据点（使用第一个可用的）
            await page.click('ul:nth-of-type(2) > :nth-child(1) li:nth-of-type(1) a')
            await page.wait_for_load_state("load")
            
            # 导航到訪問看護
            await page.evaluate("""
                () => {
                    const menuItem = document.querySelector('.dropdown:nth-child(3) li:nth-of-type(2) a');
                    if (menuItem) {
                        menuItem.click();
                        return true;
                    }
                    return false;
                }
            """)
            await page.wait_for_load_state("load")
            
            logger.info("✅ 成功导航到訪問看護页面")
            
        except Exception as e:
            logger.error(f"❌ 导航失败: {e}")
            return
        
        # 5. 检查是否有弹窗干扰
        logger.info("🔍 检查弹窗干扰...")
        popup_info = await page.evaluate("""
            () => {
                const popups = [];
                
                // 检查Karte弹窗
                const karteElements = document.querySelectorAll('[id*="karte"]');
                karteElements.forEach(el => {
                    if (el.offsetParent !== null) {
                        popups.push({
                            type: 'karte',
                            id: el.id,
                            visible: true,
                            zIndex: window.getComputedStyle(el).zIndex
                        });
                    }
                });
                
                // 检查通知弹窗
                const notifications = document.querySelectorAll('.modal:not(#registModal), [class*="notification"], [class*="alert"]');
                notifications.forEach(el => {
                    if (el.offsetParent !== null) {
                        popups.push({
                            type: 'notification',
                            className: el.className,
                            visible: true,
                            zIndex: window.getComputedStyle(el).zIndex
                        });
                    }
                });
                
                return popups;
            }
        """)
        
        if popup_info:
            logger.warning(f"⚠️ 发现活跃弹窗: {len(popup_info)} 个")
            for popup in popup_info:
                logger.warning(f"   - {popup['type']}: {popup.get('id', popup.get('className', 'unknown'))}")
        else:
            logger.info("✅ 未发现活跃弹窗")
        
        # 6. 尝试打开数据登录表单
        logger.info("📝 尝试打开数据登录表单...")
        try:
            # 点击新規追加按钮
            add_button = await page.query_selector('#btn_area .cf:nth-child(1) :nth-child(1)')
            if add_button:
                await add_button.click()
                await page.wait_for_timeout(2000)
                
                # 检查表单是否打开
                form_visible = await page.locator('#registModal').is_visible()
                if form_visible:
                    logger.info("✅ 数据登录表单成功打开")
                    
                    # 检查登录按钮状态
                    button_status = await page.evaluate("""
                        () => {
                            const button = document.querySelector('#btnRegisPop');
                            if (!button) return { exists: false };
                            
                            return {
                                exists: true,
                                disabled: button.disabled,
                                visible: button.offsetParent !== null,
                                style: button.getAttribute('style'),
                                className: button.className
                            };
                        }
                    """)
                    
                    logger.info(f"🔘 登录按钮状态:")
                    logger.info(f"   存在: {button_status.get('exists')}")
                    logger.info(f"   禁用: {button_status.get('disabled')}")
                    logger.info(f"   可见: {button_status.get('visible')}")
                    logger.info(f"   样式: {button_status.get('style')}")
                    logger.info(f"   类名: {button_status.get('className')}")
                    
                    if button_status.get('disabled'):
                        logger.warning("❌ 登录按钮被禁用 - 这是当前问题的症状")
                    else:
                        logger.info("✅ 登录按钮正常")
                        
                else:
                    logger.error("❌ 数据登录表单未能打开")
            else:
                logger.error("❌ 未找到新規追加按钮")
                
        except Exception as e:
            logger.error(f"❌ 表单测试失败: {e}")
        
        # 7. 保持浏览器打开以便观察
        logger.info("🔍 浏览器将保持打开30秒以便观察...")
        await asyncio.sleep(30)
        
    except Exception as e:
        logger.error(f"❌ 诊断过程中出错: {e}", exc_info=True)
    finally:
        await browser_manager.close_browser()

if __name__ == "__main__":
    asyncio.run(diagnose_current_issue())
