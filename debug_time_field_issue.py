#!/usr/bin/env python3
"""
调试kaipoke_tennki时间字段问题
分析数据映射和格式问题
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from logger_config import logger

async def debug_time_field_mapping():
    """调试时间字段映射问题"""
    logger.info("🔍 开始调试时间字段映射问题...")
    
    # 模拟您提供的表格数据
    # 根据您的表格：H=start_time, I=start_time时间, J=time分(1桁目), K=time分(2桁目), L=end_time, M=end_time时间, N=time分(1桁目), O=time分(2桁目)
    sample_row = [
        '',      # A (0)
        '',      # B (1) 
        '',      # C (2)
        '',      # D (3)
        '',      # E (4)
        '',      # F (5)
        '',      # G (6)
        '9:00',  # H (7) - start_time
        '09',    # I (8) - start_time时间 (正确的两位数格式)
        '0',     # J (9) - time分(1桁目)
        '0',     # K (10) - time分(2桁目)
        '9:30',  # L (11) - end_time
        '09',    # M (12) - end_time时间 (正确的两位数格式)
        '3',     # N (13) - time分(1桁目)
        '0',     # O (14) - time分(2桁目)
        '',      # P (15)
        '',      # Q (16)
        '',      # R (17)
        '',      # S (18)
        '',      # T (19)
        '',      # U (20)
        '',      # V (21)
        '',      # W (22)
        '',      # X (23)
        '照屋晏至', # Y (24) - 职员姓名
        '',      # Z (25)
        '',      # AA (26)
        '正看護師'  # AB (27) - 职员资格
    ]
    
    logger.info("📊 当前数据映射分析:")
    logger.info(f"   H列 (索引7) start_time: '{sample_row[7]}'")
    logger.info(f"   I列 (索引8) start_time时间: '{sample_row[8]}' ✅ 正确格式")
    logger.info(f"   J列 (索引9) time分(1桁目): '{sample_row[9]}'")
    logger.info(f"   K列 (索引10) time分(2桁目): '{sample_row[10]}'")
    logger.info(f"   L列 (索引11) end_time: '{sample_row[11]}'")
    logger.info(f"   M列 (索引12) end_time时间: '{sample_row[12]}' ✅ 正确格式")
    logger.info(f"   N列 (索引13) time分(1桁目): '{sample_row[13]}'")
    logger.info(f"   O列 (索引14) time分(2桁目): '{sample_row[14]}'")
    
    # 当前代码的时间字段映射
    current_mapping = [
        ('#inPopupStartHour', sample_row[8] if len(sample_row) > 8 else ''),      # I列 - 正确
        ('#inPopupStartMinute1', sample_row[9] if len(sample_row) > 9 else ''),   # J列 - 正确
        ('#inPopupStartMinute2', sample_row[10] if len(sample_row) > 10 else ''), # K列 - 正确
        ('#inPopupEndHour', sample_row[12] if len(sample_row) > 12 else ''),      # M列 - 正确
        ('#inPopupEndMinute1', sample_row[13] if len(sample_row) > 13 else ''),   # N列 - 正确
        ('#inPopupEndMinute2', sample_row[14] if len(sample_row) > 14 else '')    # O列 - 正确
    ]
    
    logger.info("\n🕐 当前时间字段映射:")
    for selector, value in current_mapping:
        logger.info(f"   {selector}: '{value}' (类型: {type(value)})")
    
    # 检查是否有问题
    logger.info("\n🔍 问题分析:")
    
    # 检查小时字段
    start_hour = sample_row[8]  # I列
    end_hour = sample_row[12]   # M列
    
    if start_hour == '09':
        logger.info("✅ 开始小时格式正确: '09'")
    else:
        logger.warning(f"❌ 开始小时格式问题: '{start_hour}' (期望: '09')")
    
    if end_hour == '09':
        logger.info("✅ 结束小时格式正确: '09'")
    else:
        logger.warning(f"❌ 结束小时格式问题: '{end_hour}' (期望: '09')")
    
    # 检查分钟字段组合
    start_minute = f"{sample_row[9]}{sample_row[10]}"  # J+K列
    end_minute = f"{sample_row[13]}{sample_row[14]}"   # N+O列
    
    logger.info(f"🕐 组合分钟检查:")
    logger.info(f"   开始分钟: J('{sample_row[9]}') + K('{sample_row[10]}') = '{start_minute}'")
    logger.info(f"   结束分钟: N('{sample_row[13]}') + O('{sample_row[14]}') = '{end_minute}'")
    
    # 可能的问题点
    logger.info("\n❓ 可能的问题点:")
    logger.info("1. 数据读取范围是否正确 (A2:AK)")
    logger.info("2. 表单字段选择器是否正确")
    logger.info("3. 事件拦截器是否干扰了字段填写")
    logger.info("4. 表单验证逻辑是否有其他要求")
    
    return current_mapping

if __name__ == "__main__":
    asyncio.run(debug_time_field_mapping())
