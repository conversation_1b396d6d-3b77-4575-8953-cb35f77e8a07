import os
import asyncio

print("[DEBUG] kanamic_download.py imported")

try:
    from dotenv import load_dotenv
    from crewai import Task, Crew
    from logger_config import logger
    from core.browser.browser_manager import browser_manager
    from agents.web_operator_agent import website_operator
    from agents.tools.browser_tools import SmartBrowserTools, do_download_task
    from agents.selector_finder_agent import selector_finder_agent
    from agents.tools.data_tools import process_downloaded_csv
    from core.gsuite.drive_client import DriveClient
    from core.gsuite.sheets_client import SheetsClient
except Exception as import_exc:
    print(f"[IMPORT ERROR] {import_exc}")
    raise

# .envファイルから環境変数を読み込む
from dotenv import load_dotenv
try:
    load_dotenv()
    logger.debug("[DEBUG] .env 已加载")
except Exception as e:
    logger.warning(f"[DEBUG] .env 加载失败: {e}")

# 调试输出所有环境变量（仅本地调试用，注意安全）
import os
print("[DEBUG] 当前环境变量 KANAMIC_PASSWORD:", os.getenv("KANAMIC_PASSWORD"))

def run(config: dict):
    asyncio.run(main_async(config))

async def main_async(config: dict):
    logger.info("カナミックダウンロードワークフローを開始します。")
    workflow_config = config.get('config', {})
    tasks = config.get("tasks", [])

    for task in tasks:
        params = task.get("params", {})
        login_url = params.get("login_url") or workflow_config.get("login_url")
        account = params.get("account") or workflow_config.get("account")
        password_env = params.get("password_env") or workflow_config.get("password_env")
        logger.debug(f"[DEBUG] password_env: {password_env}")
        logger.debug(f"[DEBUG] os.environ.get(password_env): {os.environ.get(password_env) if password_env else None}")
        password = params.get("password") or (os.environ.get(password_env) if password_env else None)
        logger.debug(f"[DEBUG] 最终用于登录的 password: {password}")
        if not (login_url and account and password):
            logger.error("ログイン情報（URL, アカウント, パスワード）が不完全です。YAML設定と.envファイルを確認してください。")
            continue
        logger.info(f"[DEBUG] 実際に使用されるログインアカウント: {account}")
        logger.info(f"[DEBUG] 実際に使用されるログインパスワード: {password}")
        download_path = workflow_config.get('download_path', '/tmp/downloads')

        if not all([login_url, account, password]):
            logger.error("ログイン情報（URL, アカウント, パスワード）が不完全です。YAML設定と.envファイルを確認してください。")
            return

        smart_tools = SmartBrowserTools()
        drive_client = DriveClient()
        sheets_client = SheetsClient()

        try:
            logger.info("ブラウザを起動し、ログイン処理を開始します...")
            await browser_manager.start_browser(headless=False)
            await browser_manager.login(login_url, account, password)
            logger.info("ログインに成功しました。各タスクの処理を開始します。")
            page = await browser_manager.get_page()

            for task_config in config.get('tasks', []):
                task_id = task_config.get('task_id')
                target_description = task_config.get('target')
                params = task_config.get('params', {})

                if not all([task_id, target_description, params]):
                    logger.warning(f"タスク設定が不完全なためスキップします: {task_config}")
                    continue

                logger.info(f"--- タスク [{task_id}] を開始します --- ")
                try:
                    # 每个任务前先回到首页，确保页面状态正确
                    await page.goto(workflow_config["login_url"].rsplit("/", 1)[0] + "/")
                    # 修复 crewai 自动注入 security_context 导致的参数校验失败
                    params.pop("security_context", None)
                    result = await do_download_task(page, params, download_path, smart_tools, selector_finder_agent, task_id)
                    logger.info(f"--- タスク [{task_id}] Playwright自動化結果: {result}")
                except Exception as e:
                    logger.error(f"タスク [{task_id}] Playwright自動化中にエラー: {e}", exc_info=True)
                    continue

                # === 文件重命名、Drive上传、Sheet粘贴 ===
                try:
                    # 1. 文件重命名与内容获取
                    file_name_template = params.get('file_name_template')
                    process_result = process_downloaded_csv(download_path, file_name_template)
                    if process_result.startswith('エラー'):
                        logger.error(f"CSVファイル処理失敗: {process_result}")
                        continue
                    new_filepath, csv_content = process_result.split('|', 1)
                    logger.info(f"リネーム後ファイル: {new_filepath}")

                    # 2. Google Drive 上传
                    drive_folder_id = params.get('target_drive_folder_id')
                    if drive_folder_id:
                        file_id = drive_client.upload_file(new_filepath, drive_folder_id)
                        if not file_id:
                            logger.error(f"Google Driveへのアップロード失敗: {new_filepath}")
                            continue
                        logger.info(f"Google Driveにアップロード完了: file_id={file_id}")
                    else:
                        logger.warning("DriveフォルダID未設定、スキップ")

                    # 3. Google Sheet 粘贴 (新逻辑)
                    data_processing_rules = params.get('data_processing_rules')
                    if data_processing_rules:
                        sheets_client.process_and_write_data(csv_content, data_processing_rules)
                    else:
                        logger.warning("data_processing_rulesが見つかりません。シートへの書き込みをスキップします。")

                except Exception as e:
                    logger.error(f"Drive/Sheet自動化処理中にエラー: {e}", exc_info=True)
                    continue

                # 3. 各タスクに独立したCrewを編成して実行（如有需要可保留）
                try:
                    task_description = create_task_description(task_id, target_description, params, download_path)
                    kanamic_task = Task(
                        description=task_description,
                        agent=website_operator,
                        expected_output=f"タスク '{task_id}' が正常に完了し、処理済みのファイルが指定のGoogle DriveとSheetsに保存されていること。"
                    )
                    task_crew = Crew(
                        agents=[website_operator],
                        tasks=[kanamic_task],
                        verbose=True
                    )
                    result = task_crew.kickoff()
                    logger.info(f"--- タスク [{task_id}] が正常に完了しました --- ")
                    logger.debug(f"タスク [{task_id}] の実行結果: {result}")
                except Exception as e:
                    logger.error(f"タスク [{task_id}] の実行中にエラーが発生しました: {e}", exc_info=True)
                    continue

        except Exception as e:
            logger.error(f"ワークフロー全体の実行中に致命的なエラーが発生しました: {e}", exc_info=True)
        finally:
            logger.info("すべてのタスクが完了したため、ブラウザをシャットダウンします。")
            await browser_manager.close_browser()
            logger.info("ワークフローが正常に終了しました。")

def create_task_description(task_id, target_description, params, download_path):
    """Agentへの指示書（description）を生成します。"""
    return f"""
    タスクID '{task_id}' を実行せよ。
    全体目標: {target_description}

    実行手順とパラメータ:
    1. **ページの状態確認**: 現在のページがメニュー選択可能な状態であることを確認せよ。
    2. **メインメニュー操作**: '{params.get("main_menu_target")}' というテキストを持つ要素をインテリジェントに探し、クリックせよ。
    3. **レポートメニュー操作**: '{params.get("report_menu_target")}' というテキストを持つ要素をインテリジェントに探し、クリックせよ。
    4. **日付選択**: 方法 '{params.get("date_handling_method")}' を使用して、前月の日付を選択せよ。
    5. **レポートタイプ選択**: 
       - レポートタイプ: '{params.get("report_type_target")}'
       - 'null' の場合はこのステップをスキップせよ。
       - それ以外の場合は、指定されたレポートタイプを選択せよ。
    6. **チェックボックス操作**: '{params.get("checkbox_target")}' を探し、チェックを入れよ。'null' の場合はスキップせよ。
    7. **ダウンロード**: '{params.get("download_button_target")}' を探し、クリックしてCSVファイルをダウンロードせよ。
       ダウンロード先のローカルパスは '{download_path}' を使用せよ。
    8. **ファイル処理**: 
       - ダウンロードした最新のCSVファイルを処理せよ。
       - ファイル名テンプレート: '{params.get("file_name_template")}'
       - 文字コードをShift_JISからUTF-8に変換せよ。
       - テンプレートに基づいて新しいファイル名を生成し、ファイルの内容を読み取れ。
    9. **Google Driveへのアップロード**: 処理済みのファイルをGoogle DriveのフォルダID '{params.get("target_drive_folder_id")}' にアップロードせよ。
    10. **Google Sheetsへの更新**: 処理済みのファイルの内容をGoogle SheetsのスプレッドシートID '{params.get("target_sheet_id")}' に書き込め。
    """
