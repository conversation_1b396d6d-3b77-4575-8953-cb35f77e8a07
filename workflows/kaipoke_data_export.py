"""
Kaipoke Data Export Workflow
基于RPA代码重构的卡イポケ数据导出工作流

功能概述：
1. 登录卡イポケ系统
2. 为19个据点注册提供票実績数据导出任务
3. 等待文件生成完成
4. 下载成功的文件并保存到Google Drive

技术特点：
- 复用现有的kaipoke登录服务
- 使用MCP三层备份机制
- 支持多据点配置化处理
- 集成Google Drive文件管理

使用方法：
python main.py kaipoke_data_export
"""

import asyncio
import sys
import os
import yaml
from typing import Dict, List, Any

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from logger_config import logger
from core.browser.browser_manager import BrowserManager
from core.selector_executor import SelectorExecutor
from core.rpa_tools.kaipoke_login_service import kaipoke_login_with_env
from core.rpa_tools.kaipoke_data_export_engine import KaipokeDataExportEngine

# 全局浏览器管理器
browser_manager = BrowserManager()


def load_workflow_config(workflow_id: str) -> Dict[str, Any]:
    """
    加载工作流配置

    Args:
        workflow_id: 工作流ID

    Returns:
        Dict[str, Any]: 工作流配置
    """
    try:
        config_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'configs', 'workflows.yaml')

        with open(config_path, 'r', encoding='utf-8') as f:
            all_configs = yaml.safe_load(f)

        config = all_configs.get(workflow_id)
        if not config:
            logger.error(f"❌ 未找到工作流配置: {workflow_id}")
            return {}

        logger.info(f"✅ 成功加载工作流配置: {workflow_id}")
        return config

    except Exception as e:
        logger.error(f"❌ 加载工作流配置失败: {e}")
        return {}


async def execute_kaipoke_data_export():
    """
    执行卡イポケ数据导出工作流
    
    工作流程：
    1. 加载配置和初始化组件
    2. 登录卡イポケ系统
    3. 为所有据点注册导出任务
    4. 下载生成的文件
    5. 上传到Google Drive
    """
    try:
        logger.info("🚀 开始执行卡イポケ数据导出工作流...")
        
        # 1. 加载配置
        config = load_workflow_config('kaipoke_data_export')
        if not config:
            logger.error("❌ 无法加载工作流配置")
            return
        
        common_config = config.get('config', {})
        facilities = config.get('facilities', [])
        
        if not facilities:
            logger.error("❌ 未找到据点配置")
            return
        
        logger.info(f"📋 加载了 {len(facilities)} 个据点配置")
        
        # 2. 初始化浏览器和组件
        await browser_manager.start_browser(headless=False)
        page = await browser_manager.get_page()
        selector_executor = SelectorExecutor(page)
        
        # 初始化MCP备份工具
        await selector_executor.initialize_mcp_fallback()
        
        # 3. 登录卡イポケ
        login_success = await kaipoke_login_with_env(
            page,
            common_config.get('corporation_id_env', 'KAIPOKE_CORPORATION_ID'),
            common_config.get('member_login_id_env', 'KAIPOKE_MEMBER_LOGIN_ID'),
            common_config.get('password_env', 'KAIPOKE_PASSWORD'),
            common_config.get('login_url')
        )
        
        if not login_success:
            logger.error("❌ 卡イポケ登录失败")
            return
        
        logger.info("✅ 卡イポケ登录成功")
        
        # 4. 初始化数据导出引擎
        export_engine = KaipokeDataExportEngine(page, selector_executor, common_config)
        
        # 5. 执行数据导出注册（所有据点）
        logger.info("📤 开始为所有据点注册数据导出任务...")
        export_results = await execute_export_registration(export_engine, facilities)
        
        # 统计注册结果
        successful_exports = sum(1 for result in export_results.values() if result)
        logger.info(f"📊 导出注册完成: {successful_exports}/{len(facilities)} 个据点成功")
        
        # 6. 等待文件生成（可选的等待时间）
        wait_time = common_config.get('download_management', {}).get('check_interval', 30)
        logger.info(f"⏳ 等待文件生成... ({wait_time}秒)")
        await asyncio.sleep(wait_time)
        
        # 7. 下载生成的文件
        logger.info("📥 开始下载生成的文件...")
        download_results = await export_engine.download_exported_files(facilities)
        
        # 统计下载结果
        successful_downloads = sum(1 for result in download_results.values() if result)
        logger.info(f"📊 文件下载完成: {successful_downloads}/{len(facilities)} 个据点成功")
        
        # 8. 输出最终报告
        generate_final_report(export_results, download_results, facilities)
        
        logger.info("✅ 卡イポケ数据导出工作流执行完成")
        
    except Exception as e:
        logger.error(f"❌ 工作流执行失败: {e}", exc_info=True)
    finally:
        # 确保浏览器关闭
        await browser_manager.close_browser()


async def execute_export_registration(export_engine: KaipokeDataExportEngine, 
                                    facilities: List[Dict[str, Any]]) -> Dict[str, bool]:
    """
    执行导出注册（所有据点）
    
    Args:
        export_engine: 数据导出引擎
        facilities: 据点配置列表
        
    Returns:
        Dict[str, bool]: 各据点的注册结果
    """
    results = {}
    
    for i, facility in enumerate(facilities, 1):
        facility_name = facility.get('facility_name')
        logger.info(f"🏢 处理据点 {i}/{len(facilities)}: {facility_name}")
        
        try:
            # 执行单个据点的导出注册
            success = await export_engine.export_facility_data(facility)
            results[facility_name] = success
            
            if success:
                logger.info(f"✅ 据点导出注册成功: {facility_name}")
            else:
                logger.error(f"❌ 据点导出注册失败: {facility_name}")
            
            # 据点间的间隔等待
            if i < len(facilities):
                await asyncio.sleep(2)
                
        except Exception as e:
            logger.error(f"❌ 据点处理异常: {facility_name}, 错误: {e}")
            results[facility_name] = False
    
    return results


def generate_final_report(export_results: Dict[str, bool], 
                         download_results: Dict[str, bool],
                         facilities: List[Dict[str, Any]]):
    """
    生成最终执行报告
    
    Args:
        export_results: 导出注册结果
        download_results: 文件下载结果
        facilities: 据点配置列表
    """
    logger.info("📋 ===== 卡イポケ数据导出工作流执行报告 =====")
    
    total_facilities = len(facilities)
    successful_exports = sum(1 for result in export_results.values() if result)
    successful_downloads = sum(1 for result in download_results.values() if result)
    
    logger.info(f"📊 总据点数: {total_facilities}")
    logger.info(f"📤 导出注册成功: {successful_exports}/{total_facilities}")
    logger.info(f"📥 文件下载成功: {successful_downloads}/{total_facilities}")
    
    # 详细结果
    logger.info("📋 详细结果:")
    for facility in facilities:
        facility_name = facility.get('facility_name')
        export_status = "✅" if export_results.get(facility_name, False) else "❌"
        download_status = "✅" if download_results.get(facility_name, False) else "❌"
        logger.info(f"  {facility_name}: 导出{export_status} 下载{download_status}")
    
    # 失败据点列表
    failed_exports = [name for name, result in export_results.items() if not result]
    failed_downloads = [name for name, result in download_results.items() if not result]
    
    if failed_exports:
        logger.warning(f"⚠️ 导出注册失败的据点: {', '.join(failed_exports)}")
    
    if failed_downloads:
        logger.warning(f"⚠️ 文件下载失败的据点: {', '.join(failed_downloads)}")
    
    # 成功率计算
    export_success_rate = (successful_exports / total_facilities) * 100 if total_facilities > 0 else 0
    download_success_rate = (successful_downloads / total_facilities) * 100 if total_facilities > 0 else 0
    
    logger.info(f"📈 导出注册成功率: {export_success_rate:.1f}%")
    logger.info(f"📈 文件下载成功率: {download_success_rate:.1f}%")
    
    logger.info("📋 ============================================")


async def execute_single_facility_export(facility_name: str):
    """
    执行单个据点的数据导出（用于测试）
    
    Args:
        facility_name: 据点名称
    """
    try:
        logger.info(f"🧪 开始单据点测试: {facility_name}")
        
        # 加载配置
        config = load_workflow_config('kaipoke_data_export')
        if not config:
            logger.error("❌ 无法加载工作流配置")
            return
        
        common_config = config.get('config', {})
        facilities = config.get('facilities', [])
        
        # 查找目标据点
        target_facility = None
        for facility in facilities:
            if facility.get('facility_name') == facility_name:
                target_facility = facility
                break
        
        if not target_facility:
            logger.error(f"❌ 未找到据点配置: {facility_name}")
            return
        
        # 初始化组件
        await browser_manager.start_browser(headless=False)
        page = await browser_manager.get_page()
        selector_executor = SelectorExecutor(page)
        await selector_executor.initialize_mcp_fallback()
        
        # 登录
        login_success = await kaipoke_login_with_env(
            page,
            common_config.get('corporation_id_env', 'KAIPOKE_CORPORATION_ID'),
            common_config.get('member_login_id_env', 'KAIPOKE_MEMBER_LOGIN_ID'),
            common_config.get('password_env', 'KAIPOKE_PASSWORD'),
            common_config.get('login_url')
        )
        
        if not login_success:
            logger.error("❌ 登录失败")
            return
        
        # 执行导出
        export_engine = KaipokeDataExportEngine(page, selector_executor, common_config)
        success = await export_engine.export_facility_data(target_facility)
        
        if success:
            logger.info(f"✅ 单据点测试成功: {facility_name}")
        else:
            logger.error(f"❌ 单据点测试失败: {facility_name}")
        
    except Exception as e:
        logger.error(f"❌ 单据点测试异常: {e}", exc_info=True)
    finally:
        await browser_manager.close_browser()


def run(config: Dict[str, Any]):
    """
    主入口函数，由main.py调用

    Args:
        config: 从workflows.yaml加载的配置
    """
    try:
        logger.info("🚀 启动卡イポケ数据导出工作流...")
        asyncio.run(execute_kaipoke_data_export())
    except Exception as e:
        logger.error(f"❌ 工作流执行失败: {e}", exc_info=True)


if __name__ == "__main__":
    # 支持命令行参数
    if len(sys.argv) > 1:
        if sys.argv[1] == "test" and len(sys.argv) > 2:
            # 单据点测试模式
            facility_name = sys.argv[2]
            asyncio.run(execute_single_facility_export(facility_name))
        else:
            logger.error("❌ 无效的命令行参数")
    else:
        # 正常执行模式
        asyncio.run(execute_kaipoke_data_export())
