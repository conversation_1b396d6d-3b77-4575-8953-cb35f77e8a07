"""
Kaipoke Form Download Workflow (V6 - Final Corrected Version)
意图：一次性登录カイポケ系统，基于三种标准操作模板（A、B、C）完成37个账单预览文件的下载。

核心流程 (V6 - 最终版):
1.  登录 -> 进入服务总览页。
2.  按据点分组任务。
3.  循环处理每个据点:
    a. 导航到据点页面。
    b. 跟踪任务的`flow_type`。如果`flow_type`改变，则重置导航，从据点页开始执行新的悬停/点击。
    c. 如果`flow_type`不变，则精确返回到之前记住的“様式出力”后页面。
    d. 在选择年月后，动态等待目标按钮出现再点击。
    e. 点击预览后，按要求等待2秒即点击下载。
    f. 完成一个据点的所有任务后，返回服务总览页。
"""

import asyncio
import os
from logger_config import logger
from core.browser.browser_manager import BrowserManager
from core.selector_executor import SelectorExecutor
from core.rpa_tools.kaipoke_login_service import kaipoke_login_direct
from core.rpa_tools.kaipoke_common import get_previous_month_wareki
from core.popup_handler.kaipoke_popup_handler import handle_kaipoke_login_popups
from core.gsuite.drive_client import DriveClient

async def template_a_flow(selector_executor: SelectorExecutor, task_config: dict, download_path: str, task_list_url: str = None):
    page = selector_executor.page
    task_id = task_config.get('task_id', 'N/A')
    template_param = task_config.get('template_param', '')
    final_filename = task_config.get('output_filename', f"download_{task_id}.pdf")
    element_text = task_config.get('element_text', '')

    logger.info(f"🅰️ Task {task_id}: Executing Template A flow")

    if not task_list_url:
        logger.info(f"🎯 Task {task_id}: First task in flow group. Performing initial navigation.")
        await selector_executor.smart_hover("kaipoke_form_download", "template_a", "hover_selector")
        await page.wait_for_timeout(1000)
        await selector_executor.smart_click("kaipoke_form_download", "template_a", "click_selector_1")
        await page.wait_for_load_state('networkidle', timeout=60000)

        # 确保我们在正确的表单页面，而不是选择页面
        current_url = page.url
        if "MEM090002.do" in current_url:
            logger.warning(f"⚠️ Task {task_id}: Still on selection page, need to navigate to form page")
            # 如果还在选择页面，需要再次点击进入表单页面
            try:
                await selector_executor.smart_hover("kaipoke_form_download", "template_a", "hover_selector")
                await page.wait_for_timeout(1000)
                await selector_executor.smart_click("kaipoke_form_download", "template_a", "click_selector_1")
                await page.wait_for_load_state('networkidle', timeout=60000)
                current_url = page.url
            except Exception as nav_error:
                logger.error(f"❌ Task {task_id}: Failed to navigate to form page: {nav_error}")

        task_list_url = current_url
        logger.info(f"📖 Stored task list URL: {task_list_url}")
    else:
        logger.info(f"📖 Returning to stored task list URL: {task_list_url}")
        # 检查URL是否指向正确的页面类型
        if "MEM090002.do" in task_list_url:
            logger.warning(f"⚠️ Task {task_id}: Stored URL is selection page, navigating to form page instead")
            # 如果存储的URL是选择页面，需要重新导航
            await selector_executor.smart_hover("kaipoke_form_download", "template_a", "hover_selector")
            await page.wait_for_timeout(1000)
            await selector_executor.smart_click("kaipoke_form_download", "template_a", "click_selector_1")
            await page.wait_for_load_state('networkidle', timeout=60000)
            task_list_url = page.url
            logger.info(f"📖 Updated task list URL: {task_list_url}")
        else:
            await page.goto(task_list_url, wait_until='networkidle', timeout=60000)

    await handle_kaipoke_login_popups(page, "form_download_template_based")

    await selector_executor.smart_select_option("kaipoke_form_download", "template_a", "month_selector", text=get_previous_month_wareki())

    logger.info(f"⏳ Waiting for preview button '{template_param}' to be visible after date change...")
    await page.wait_for_selector(template_param, state='visible', timeout=30000)

    await selector_executor.smart_click("kaipoke_form_download", "template_a", "click_selector_2", dynamic_params={'selector': template_param})

    logger.info("⏳ Waiting 2 seconds for download page to settle as requested...")
    await page.wait_for_timeout(2000)

    # 等待下载按钮可见，增加更详细的错误处理
    download_button_selector = '#form\\:btnExport'
    try:
        logger.info(f"🔍 Waiting for download button '{download_button_selector}' to be visible...")
        await page.wait_for_selector(download_button_selector, state='visible', timeout=60000)
        logger.info("✅ Download button is visible, proceeding with download...")

        async with page.expect_download(timeout=120000) as download_info:
            await page.click(download_button_selector, no_wait_after=True, timeout=30000)
            download = await download_info.value
            download_target_path = os.path.join(download_path, final_filename)
            await download.save_as(download_target_path)
            logger.info(f"✅ File downloaded to: {download_target_path}")
    except Exception as e:
        logger.error(f"❌ Download button error: {e}")
        # 尝试查找页面上的其他可能的下载按钮
        alternative_selectors = [
            'input[type="image"][alt*="印刷"]',
            'input[type="image"][alt*="プレビュー"]',
            'button:contains("印刷プレビューする")',
            'a:contains("印刷プレビューする")'
        ]

        for alt_selector in alternative_selectors:
            try:
                logger.info(f"🔍 Trying alternative download selector: {alt_selector}")
                await page.wait_for_selector(alt_selector, state='visible', timeout=10000)
                async with page.expect_download(timeout=120000) as download_info:
                    await page.click(alt_selector, no_wait_after=True, timeout=30000)
                    download = await download_info.value
                    download_target_path = os.path.join(download_path, final_filename)
                    await download.save_as(download_target_path)
                    logger.info(f"✅ File downloaded using alternative selector: {download_target_path}")
                    break
            except Exception as alt_e:
                logger.debug(f"Alternative selector {alt_selector} failed: {alt_e}")
                continue
        else:
            # 如果所有选择器都失败，抛出原始错误
            raise e

    # 特殊处理：对于訪問看護据点的A模板任务，需要返回到正确的"样式出力"页面URL
    # 以便后续B模板任务能够正确继续执行
    if "訪問看護" in element_text and task_list_url:
        # 修正URL中的conversationContext参数，从4改为9
        corrected_url = task_list_url.replace("conversationContext=4", "conversationContext=9")
        if corrected_url != task_list_url:
            logger.info(f"🔧 Correcting task list URL for 訪問看護 facility: {corrected_url}")
            task_list_url = corrected_url

    return download_target_path, task_list_url

async def template_b_flow(selector_executor: SelectorExecutor, task_config: dict, download_path: str, task_list_url: str = None):
    page = selector_executor.page
    task_id = task_config.get('task_id', 'N/A')
    template_param = task_config.get('template_param', '')
    final_filename = task_config.get('output_filename', f"download_{task_id}.pdf")

    logger.info(f"🅱️ Task {task_id}: Executing Template B flow")

    if not task_list_url:
        logger.info(f"🎯 Task {task_id}: First task in flow group. Performing initial navigation.")
        await selector_executor.smart_hover("kaipoke_form_download", "template_b", "hover_selector")
        await page.wait_for_timeout(1000)
        await selector_executor.smart_click("kaipoke_form_download", "template_b", "click_selector_1")
        await page.wait_for_load_state('networkidle', timeout=60000)
        task_list_url = page.url
        logger.info(f"📖 Stored task list URL: {task_list_url}")
    else:
        logger.info(f"📖 Returning to stored task list URL: {task_list_url}")
        await page.goto(task_list_url, wait_until='networkidle', timeout=60000)

    await handle_kaipoke_login_popups(page, "form_download_template_based")

    # 先点击"出力设定"按钮（对于任务11和27）
    try:
        await selector_executor.smart_click("kaipoke_form_download", "template_b", "preparation_button")
        await page.wait_for_load_state('networkidle', timeout=60000)
        logger.info(f"✅ Task {task_id}: Clicked preparation button (出力設定)")
    except Exception as e:
        logger.debug(f"⚠️ Task {task_id}: Preparation button not found or not needed: {e}")

    await selector_executor.smart_select_option("kaipoke_form_download", "template_b", "month_selector", text=get_previous_month_wareki())

    logger.info(f"⏳ Waiting for preview button '{template_param}' to be visible after date change...")
    await page.wait_for_selector(template_param, state='visible', timeout=30000)

    await selector_executor.smart_click("kaipoke_form_download", "template_b", "click_selector_2", dynamic_params={'selector': template_param})
    await page.wait_for_load_state('networkidle', timeout=60000)

    try:
        await selector_executor.smart_click("kaipoke_form_download", "template_b", "checkbox_already_output")
    except Exception: pass
    try:
        await selector_executor.smart_click("kaipoke_form_download", "template_b", "checkbox_select_all")
    except Exception: pass

    async with page.expect_download(timeout=120000) as download_info:
        await page.click('#previewLink', no_wait_after=True, timeout=30000)
        download = await download_info.value
        download_target_path = os.path.join(download_path, final_filename)
        await download.save_as(download_target_path)
        logger.info(f"✅ File downloaded to: {download_target_path}")

    return download_target_path, task_list_url

async def template_c_flow(selector_executor: SelectorExecutor, task_config: dict, download_path: str, task_list_url: str = None):
    page = selector_executor.page
    task_id = task_config.get('task_id', 'N/A')
    template_param = task_config.get('template_param', '.box-refine :nth-child(1)')
    final_filename = task_config.get('output_filename', f"download_{task_id}.pdf")
    element_text = task_config.get('element_text', '')

    logger.info(f"🅲 Task {task_id}: Executing Template C flow")

    if not task_list_url:
        logger.info(f"🎯 Task {task_id}: First task in flow group. Performing initial navigation.")
        await selector_executor.smart_hover("kaipoke_form_download", "template_c", "hover_selector")
        await page.wait_for_timeout(1000)
        await selector_executor.smart_click("kaipoke_form_download", "template_c", "click_selector_1")
        await page.wait_for_load_state('networkidle', timeout=60000)
        task_list_url = page.url
        logger.info(f"📖 Stored task list URL: {task_list_url}")
    else:
        logger.info(f"📖 Returning to stored task list URL: {task_list_url}")
        await page.goto(task_list_url, wait_until='networkidle', timeout=60000)

    await handle_kaipoke_login_popups(page, "form_download_template_based")

    await selector_executor.smart_click("kaipoke_form_download", "template_c", "preparation_button")
    await page.wait_for_load_state('networkidle', timeout=60000)

    await selector_executor.smart_select_option("kaipoke_form_download", "template_c", "month_selector", text=get_previous_month_wareki())

    logger.info(f"⏳ Waiting for preview button '{template_param}' to be visible after date change...")
    await page.wait_for_selector(template_param, state='visible', timeout=30000)

    await selector_executor.smart_click("kaipoke_form_download", "template_c", "click_selector_2", dynamic_params={'selector': template_param})

    logger.info("⏳ Waiting 2 seconds for download page to settle as requested...")
    await page.wait_for_timeout(2000)

    # 对于任务17，需要点击"印刷"按钮
    if task_id == "17":
        logger.info(f"🖨️ Task {task_id}: Clicking print button for task 17")
        print_button_selector = '.box-refine :nth-child(1)'
        try:
            await page.wait_for_selector(print_button_selector, state='visible', timeout=30000)
            await page.click(print_button_selector, timeout=30000)
            logger.info(f"✅ Task {task_id}: Print button clicked successfully")
            await page.wait_for_timeout(2000)
        except Exception as e:
            logger.error(f"❌ Task {task_id}: Failed to click print button: {e}")

    final_download_selector = '#form\\:btnExport'
    async with page.expect_download(timeout=120000) as download_info:
        await page.click(final_download_selector, no_wait_after=True, timeout=30000)
        download = await download_info.value
        download_target_path = os.path.join(download_path, final_filename)
        await download.save_as(download_target_path)
        logger.info(f"✅ File downloaded to: {download_target_path}")

    # 特殊处理：对于訪問看護据点的C模板任务，需要返回到正确的"样式出力"页面URL
    # 以便后续B模板任务能够正确继续执行
    if "訪問看護" in element_text and task_list_url:
        # 修正URL中的conversationContext参数，从4改为9
        corrected_url = task_list_url.replace("conversationContext=4", "conversationContext=9")
        if corrected_url != task_list_url:
            logger.info(f"🔧 Correcting task list URL for 訪問看護 facility: {corrected_url}")
            task_list_url = corrected_url

    return download_target_path, task_list_url

async def template_bc_combined_flow(selector_executor: SelectorExecutor, task_config: dict, download_path: str, task_list_url: str = None):
    page = selector_executor.page
    task_id = task_config.get('task_id', 'N/A')
    template_param = task_config.get('template_param', '')
    final_filename = task_config.get('output_filename', f"download_{task_id}.pdf")

    logger.info(f"🅱️🅲 Task {task_id}: Executing Template B+C combined flow")

    if not task_list_url:
        logger.info(f"🎯 Task {task_id}: First task in flow group. Performing initial navigation.")
        await selector_executor.smart_hover("kaipoke_form_download", "template_b", "hover_selector")
        await page.wait_for_timeout(1000)
        await selector_executor.smart_click("kaipoke_form_download", "template_b", "click_selector_1")
        await page.wait_for_load_state('networkidle', timeout=60000)
        task_list_url = page.url
        logger.info(f"📖 Stored task list URL: {task_list_url}")
    else:
        logger.info(f"📖 Returning to stored task list URL: {task_list_url}")
        await page.goto(task_list_url, wait_until='networkidle', timeout=60000)

    await handle_kaipoke_login_popups(page, "form_download_template_based")

    # 先点击"出力设定"按钮（对于任务11和13）
    try:
        await selector_executor.smart_click("kaipoke_form_download", "template_c", "preparation_button")
        await page.wait_for_load_state('networkidle', timeout=60000)
        logger.info(f"✅ Task {task_id}: Clicked preparation button (出力設定)")
    except Exception as e:
        logger.debug(f"⚠️ Task {task_id}: Preparation button not found or not needed: {e}")

    await selector_executor.smart_select_option("kaipoke_form_download", "template_b", "month_selector", text=get_previous_month_wareki())

    logger.info(f"⏳ Waiting for preview button '{template_param}' to be visible after date change...")
    await page.wait_for_selector(template_param, state='visible', timeout=30000)

    await selector_executor.smart_click("kaipoke_form_download", "template_b", "click_selector_2", dynamic_params={'selector': template_param})
    await page.wait_for_load_state('networkidle', timeout=60000)

    try:
        await selector_executor.smart_click("kaipoke_form_download", "template_b", "checkbox_already_output")
        await selector_executor.smart_click("kaipoke_form_download", "template_b", "checkbox_select_all")
    except Exception: pass

    async with page.expect_download(timeout=120000) as download_info:
        await page.click('#previewLink', no_wait_after=True, timeout=30000)
        download = await download_info.value
        download_target_path = os.path.join(download_path, final_filename)
        await download.save_as(download_target_path)
        logger.info(f"✅ File downloaded to: {download_target_path}")

    return download_target_path, task_list_url

async def execute_task_with_template(selector_executor: SelectorExecutor, task_config: dict, download_path: str, task_list_url: str = None):
    flow_type = task_config.get('flow_type', 'A')
    task_id = task_config.get('task_id', 'N/A')
    logger.info(f"🎯 Task {task_id}: Flow type '{flow_type}' - Selecting template")
    try:
        if flow_type == 'A':
            return await template_a_flow(selector_executor, task_config, download_path, task_list_url)
        elif flow_type == 'B':
            return await template_b_flow(selector_executor, task_config, download_path, task_list_url)
        elif flow_type == 'C':
            return await template_c_flow(selector_executor, task_config, download_path, task_list_url)
        elif flow_type == 'B+C':
            return await template_bc_combined_flow(selector_executor, task_config, download_path, task_list_url)
        else:
            logger.error(f"❌ Task {task_id}: Unknown flow type '{flow_type}'")
            return None, None
    except Exception as e:
        logger.error(f"❌ Task {task_id}: Template execution failed: {e}", exc_info=True)
        return None, None

async def navigate_to_service_overview(page):
    logger.info("🏠 Navigating to service overview page (レセプト)")
    try:
        receipt_menu_selector = ".mainCtg li:nth-of-type(1) a"
        await page.wait_for_selector(receipt_menu_selector, timeout=30000)
        await page.click(receipt_menu_selector)
        await page.wait_for_load_state('networkidle', timeout=60000)
        await handle_kaipoke_login_popups(page, "form_download_template_based")
        logger.info("✅ Successfully navigated to service overview page")
    except Exception as e:
        logger.error(f"❌ Failed to navigate to service overview: {e}")
        raise

async def navigate_to_facility(page, element_text: str):
    logger.info(f"🏢 Navigating to facility: {element_text}")
    try:
        # It's safer to be on the overview page before trying to find a facility
        await return_to_service_overview(page)
        facility_xpath = f'//a[contains(text(), "{element_text}")]'
        await page.wait_for_selector(facility_xpath, timeout=30000)
        await page.click(facility_xpath, timeout=20000)
        await page.wait_for_load_state('networkidle', timeout=60000)
        await handle_kaipoke_login_popups(page, "form_download_template_based")
        logger.info(f"✅ Successfully navigated to facility: {element_text}")
        return True
    except Exception as e:
        logger.error(f"❌ Failed to navigate to facility {element_text}: {e}")
        return False

async def return_to_service_overview(page):
    logger.info("🔙 Returning to service overview page")
    try:
        overview_url = "https://r.kaipoke.biz/kaipokebiz/common/COM020101.do?fromBP=true"
        if page.url != overview_url:
            await page.goto(overview_url, wait_until='networkidle', timeout=60000)
            await handle_kaipoke_login_popups(page, "form_download_template_based")
            logger.info("✅ Successfully returned to service overview page")
        else:
            logger.info("✅ Already on service overview page.")
    except Exception as e:
        logger.error(f"❌ Failed to return to service overview: {e}")

def group_tasks_by_facility(tasks: list):
    facility_groups = {}
    for task in tasks:
        element_text = task.get('element_text', '')
        if element_text not in facility_groups:
            facility_groups[element_text] = []
        facility_groups[element_text].append(task)
    logger.info(f"📊 Tasks grouped into {len(facility_groups)} facilities.")
    return facility_groups

async def process_facility_group(selector_executor: SelectorExecutor, element_text: str,
                               facility_tasks: list, common_config: dict, drive_client: DriveClient):
    page = selector_executor.page
    logger.info(f"🏢 Processing facility: {element_text} ({len(facility_tasks)} tasks)")

    if not await navigate_to_facility(page, element_text):
        logger.error(f"❌ Skipping facility {element_text} due to navigation failure.")
        return 0

    successful_tasks = 0
    current_task_list_url = None
    current_flow_type = None
    current_account = None

    for i, task_config in enumerate(facility_tasks):
        task_id = task_config.get('task_id', 'N/A')
        new_flow_type = task_config.get('flow_type', 'A')
        logger.info(f"📋 Starting task {i+1}/{len(facility_tasks)}: {task_id} (Flow: {new_flow_type})")

        # 检查是否需要切换账号（任务34、35、36）
        task_account = None
        if task_config.get('corporation_id_env') or task_config.get('member_login_id_env') or task_config.get('password_env'):
            task_account = {
                'corporation_id_env': task_config.get('corporation_id_env', common_config.get('corporation_id_env')),
                'member_login_id_env': task_config.get('member_login_id_env', common_config.get('member_login_id_env')),
                'password_env': task_config.get('password_env', common_config.get('password_env'))
            }

        # 如果需要切换账号，重新登录
        if task_account and task_account != current_account:
            logger.info(f"🔄 Task {task_id}: Account switch required, re-logging in...")
            try:
                login_success = await kaipoke_login_direct(
                    page,
                    os.getenv(task_account['corporation_id_env']),
                    os.getenv(task_account['member_login_id_env']),
                    os.getenv(task_account['password_env']),
                    common_config.get('login_url')
                )
                if not login_success:
                    logger.error(f"❌ Task {task_id}: Account switch login failed. Skipping task.")
                    continue
                await handle_kaipoke_login_popups(page, "form_download_template_based")
                await navigate_to_service_overview(page)
                await navigate_to_facility(page, element_text)
                current_account = task_account
                current_task_list_url = None  # Reset navigation after account switch
                logger.info(f"✅ Task {task_id}: Account switch successful")
            except Exception as login_error:
                logger.error(f"❌ Task {task_id}: Account switch failed: {login_error}")
                continue

        try:
            if new_flow_type != current_flow_type:
                logger.info(f"🔄 Flow type changed from '{current_flow_type}' to '{new_flow_type}'.")

                # 特殊处理：对于訪問看護据点，从A或C模板切换到B或B+C模板时，
                # 不重置导航，而是使用A/C模板返回的修正后的URL
                if ("訪問看護" in element_text and
                    current_flow_type in ['A', 'C'] and new_flow_type in ['B', 'B+C'] and
                    current_task_list_url and "conversationContext=9" in current_task_list_url):
                    logger.info(f"🎯 Special handling for 訪問看護 {current_flow_type}->{new_flow_type} transition. Keeping corrected URL: {current_task_list_url}")
                else:
                    logger.info(f"🔄 Resetting navigation for flow type change.")
                    current_task_list_url = None
                    # Navigate back to facility page to ensure a clean start for the new flow type
                    await navigate_to_facility(page, element_text)

                current_flow_type = new_flow_type

            downloaded_file_path, returned_url = await execute_task_with_template(
                selector_executor, task_config, common_config['download_path'], current_task_list_url
            )

            if returned_url:
                current_task_list_url = returned_url

            if downloaded_file_path:
                if common_config.get('gdrive_folder_id'):
                    try:
                        drive_client.upload_file(downloaded_file_path, common_config['gdrive_folder_id'])
                        logger.info(f"✅ GDrive Upload Success for Task {task_id}")
                    except Exception as upload_error:
                        logger.error(f"❌ GDrive Upload Failed for Task {task_id}: {upload_error}")
                successful_tasks += 1
                logger.info(f"✅ Task {task_id} completed successfully.")
            else:
                logger.error(f"❌ Task {task_id} failed to produce a download.")

        except Exception as task_error:
            logger.error(f"❌ Task {task_id} failed with critical error: {task_error}", exc_info=True)
            current_task_list_url = None
            current_flow_type = None

    logger.info(f"✅ Facility {element_text} processing complete. Success: {successful_tasks}/{len(facility_tasks)}.")
    return successful_tasks

async def async_run(config: dict):
    logger.info("🚀 Starting Kaipoke Form Download Workflow (V6 - Final Corrected Version)")
    common_config = config.get('config', {})
    tasks = config.get('tasks', [])
    if not tasks:
        logger.error("❌ No tasks defined. Aborting.")
        return

    browser_manager = BrowserManager()
    drive_client = DriveClient()

    try:
        await browser_manager.start_browser(headless=common_config.get("headless", False))
        page = await browser_manager.get_page()
        selector_executor = SelectorExecutor(page)

        login_success = await kaipoke_login_direct(
            page,
            os.getenv(common_config.get('corporation_id_env', 'KAIPOKE_CORPORATION_ID')),
            os.getenv(common_config.get('member_login_id_env', 'KAIPOKE_MEMBER_LOGIN_ID')),
            os.getenv(common_config.get('password_env', 'KAIPOKE_PASSWORD')),
            common_config.get('login_url')
        )
        if not login_success:
            logger.error("❌ Login failed. Aborting workflow.")
            return
        await handle_kaipoke_login_popups(page, "form_download_template_based")

        await navigate_to_service_overview(page)
        facility_groups = group_tasks_by_facility(tasks)
        total_successful = 0

        for element_text, facility_tasks in facility_groups.items():
            try:
                successful_count = await process_facility_group(
                    selector_executor, element_text, facility_tasks, common_config, drive_client
                )
                total_successful += successful_count
            except Exception as group_error:
                logger.error(f"❌ Unhandled error in facility group {element_text}: {group_error}", exc_info=True)

        logger.info(f"🎉 Workflow finished. Total successful tasks: {total_successful}/{len(tasks)}")

    except Exception as e:
        logger.error(f"❌ A critical error occurred in the main workflow: {e}", exc_info=True)
    finally:
        await browser_manager.close_browser()

def run(config: dict):
    """工作流同步入口函数"""
    asyncio.run(async_run(config))
