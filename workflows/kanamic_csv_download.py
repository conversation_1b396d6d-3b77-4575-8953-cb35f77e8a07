import os
import asyncio
import zipfile
import tempfile
from pathlib import Path
from dotenv import load_dotenv
from logger_config import logger
from core.browser.browser_manager import browser_manager
from core.gsuite.drive_client import DriveClient
from agents.web_operator_agent import website_operator
from crewai import Task, Crew
from core.selector_executor import SelectorExecutor

# .envファイルから環境変数を読み込む
load_dotenv()

def run(config: dict):
    """カナミックCSVダウンロードワークフローを実行します。"""
    asyncio.run(main_async(config))

async def main_async(config: dict):
    """メインの非同期実行関数"""
    logger.info("カナミックCSVダウンロードワークフローを開始します。")
    
    workflow_config = config.get('config', {})
    login_url = workflow_config.get('login_url', 'https://bi.kanamic.net/josso/signon/login.do?josso_back_to=https://portal.kanamic.net/tritrus/josso_security_check')
    username_env = workflow_config.get('username_env', 'KANAMIC_USERNAME')
    password_env = workflow_config.get('password_env', 'KANAMIC_PASSWORD')
    gdrive_folder_id = workflow_config.get('gdrive_folder_id')
    download_path = workflow_config.get('download_path', '/tmp/kanamic_csv_downloads')
    
    # 環境変数から認証情報を取得
    username = os.getenv(username_env)
    password = os.getenv(password_env)
    
    if not all([username, password, gdrive_folder_id]):
        logger.error("必要な設定が不足しています。ユーザー名、パスワード、Google DriveフォルダIDを確認してください。")
        return
    
    logger.info(f"ログイン情報: {username}")
    logger.info(f"Google DriveフォルダID: {gdrive_folder_id}")
    
    # Google Drive クライアントを初期化
    try:
        drive_client = DriveClient()
        logger.info("Google Drive クライアントが正常に初期化されました。")
    except Exception as e:
        logger.error(f"Google Drive クライアントの初期化に失敗しました: {e}")
        return
    
    # ダウンロードディレクトリを作成
    os.makedirs(download_path, exist_ok=True)
    
    try:
        # ブラウザを起動
        await browser_manager.start_browser(headless=False)
        logger.info("ブラウザを起動しました。")

        # ページを取得してログイン
        page = await browser_manager.get_page()
        await page.goto(login_url, wait_until='networkidle', timeout=60000)
        logger.info(f"ページにアクセスしました: {login_url}")

        # セレクタ実行器を初期化
        selector_executor = SelectorExecutor(page)
        
        # 🆕 MCPバックアップツールを初期化
        await selector_executor.initialize_mcp_fallback()

        # ログイン処理（セレクタ優先 + MCPバックアップ）
        login_success = await login_to_kanamic_with_enhanced_selectors(selector_executor, username, password)
        
        if login_success:
            # CSVダウンロード処理を実行（セレクタ優先 + MCPバックアップ）
            download_success = await download_csv_data_with_enhanced_selectors(selector_executor, download_path, drive_client, gdrive_folder_id)
            
            if not download_success:
                logger.warning("⚠️ セレクタとMCPでのCSVダウンロードに失敗、Agentフォールバックを実行")
                await call_agent_for_csv_download(page, drive_client, gdrive_folder_id)
        else:
            logger.warning("⚠️ セレクタとMCPでのログインに失敗、Agentフォールバックを実行")
            await call_agent_for_login(page, username, password)

    except Exception as e:
        logger.error(f"ワークフローの実行中にエラーが発生しました: {e}", exc_info=True)
    finally:
        # ブラウザを終了
        await browser_manager.close_browser()
        logger.info("ワークフローが終了しました。")

async def login_to_kanamic_with_enhanced_selectors(selector_executor, username, password):
    """カナミックにログインする（セレクタ優先 + MCPバックアップ版本）"""
    try:
        logger.info("カナミックにログインします（MCP強化版）...")
        
        # 強化されたセレクタ実行器を使用してログイン（MCPバックアップ含む）
        login_success = await selector_executor.execute_kanamic_login(username, password)
        
        if login_success:
            logger.info("✅ カナミックへのログインが完了しました（MCP強化版）。")
            return True
        else:
            logger.warning("⚠️ セレクタとMCPバックアップでのログインに失敗")
            return False
        
    except Exception as e:
        logger.error(f"MCP強化ログイン処理中にエラーが発生しました: {e}")
        return False



async def call_agent_for_login(page, username, password):
    """セレクタ失効時にAgentを呼び出してログイン処理を行う"""
    logger.info("セレクタが失効したため、Agentを呼び出します。")
    
    task = Task(
        description=f"カナミックのログインページで、ユーザー名 '{username}' とパスワードを使用してログインしてください。ログインフォームを見つけて入力し、ログインボタンをクリックしてください。",
        agent=website_operator,
        expected_output="ログインが成功し、ダッシュボードまたはメインページに遷移していること。"
    )
    
    crew = Crew(
        agents=[website_operator],
        tasks=[task],
        verbose=True
    )
    
    try:
        result = crew.kickoff()
        logger.info(f"Agentによるログイン処理が完了しました: {result}")
    except Exception as e:
        logger.error(f"Agentによるログイン処理でエラーが発生しました: {e}")

async def download_csv_data_with_enhanced_selectors(selector_executor, download_path, drive_client, gdrive_folder_id):
    """CSVデータをダウンロードしてGoogle Driveにアップロードする（セレクタ優先 + MCPバックアップ版本）"""
    try:
        logger.info("CSVダウンロード処理を開始します（MCP強化版）...")
        
        # セレクタ実行器を使用してCSVダウンロード処理を実行（MCP強化版）
        # ファイル処理も含めて一括実行
        download_success = await selector_executor.execute_kanamic_csv_download(
            download_path=download_path, 
            drive_client=drive_client, 
            gdrive_folder_id=gdrive_folder_id
        )
        
        if download_success:
            logger.info("✅ CSVダウンロード処理が完了しました（MCP強化版）。")
            return True
        else:
            logger.warning("⚠️ セレクタとMCPバックアップでのCSVダウンロードに失敗")
            return False
        
    except Exception as e:
        logger.error(f"MCP強化CSVダウンロード処理でエラーが発生しました: {e}")
        return False


# 注意：重複していた関数は削除されました
# download_csv_data, navigate_to_csv_download_page, select_current_month_and_download, process_downloaded_files
# これらの機能は現在 selector_executor.py の execute_kanamic_csv_download() に統合されています

async def call_agent_for_csv_download(page, drive_client, gdrive_folder_id):
    """セレクタ失効時にAgentを呼び出してCSVダウンロード処理を行う"""
    logger.info("セレクタが失効したため、AgentでCSVダウンロード処理を実行します。")
    
    task = Task(
        description="""
        カナミックのシステムで以下の手順でCSVデータをダウンロードしてください：
        1. 適切なメニューに移動して、地域密着型介護福祉施設入所者生活介護のデータダウンロードページを見つける
        2. サービス種別で「地域密着型介護福祉施設入所者生活介護」を選択
        3. 対象月として現在の月を選択
        4. 検索を実行してデータを表示
        5. CSVファイルをダウンロード
        ダウンロードしたファイルは自動的にGoogle Driveにアップロードされます。
        """,
        agent=website_operator,
        expected_output="CSVファイルが正常にダウンロードされ、Google Driveの指定フォルダにアップロードされていること。"
    )
    
    crew = Crew(
        agents=[website_operator],
        tasks=[task],
        verbose=True
    )
    
    try:
        result = crew.kickoff()
        logger.info(f"AgentによるCSVダウンロード処理が完了しました: {result}")
    except Exception as e:
        logger.error(f"AgentによるCSVダウンロード処理でエラーが発生しました: {e}")
