"""
Kaipoke Billing Workflow (MCP強化版)
基于RPA代码的核心流程，复用现有kaipoke模块

核心流程：
1. 登录Kaipoke系统
2. 批量处理20个据点的账单数据下载
3. CSV编码转换和数据分类处理
4. Google Sheets多Sheet创建和数据写入
5. Google Drive文件夹管理

🆕 优化内容：
- 复用kaipoke_login_service和据点导航逻辑
- 增强批量写入风险控制（分批处理、重试机制）
- 智能数据分类（当月请求、当月服务提供、前月以前请求）
- MCP备份机制确保选择器稳定性

🔧 修复记录 (2025-07-21):
- 修复smart_hover方法调用兼容性问题
- 增强错误处理和日志记录
- 清理Python缓存文件避免版本冲突
- 验证所有核心功能正常运行
"""

import asyncio
import os
import pandas as pd
import math
import re
import sys
from datetime import datetime, timedelta
from pathlib import Path
from logger_config import logger
from core.browser.browser_manager import BrowserManager
from core.selector_executor import SelectorExecutor
from core.rpa_tools.kaipoke_login_service import kaipoke_login_with_env, kaipoke_login_direct
from core.gsuite.sheets_client import SheetsClient
from core.gsuite.drive_client import DriveClient
from core.rpa_tools.data_processor import DataProcessor
from core.rpa_tools.csv_data_classifier import CSVDataClassifier
from core.gsuite.multi_sheet_manager import MultiSheetManager
from openpyxl import Workbook, load_workbook
from openpyxl.utils import get_column_letter
import numpy as np


async def group_tasks_by_account(tasks: list, common_config: dict):
    """
    按账号分组任务
    🆕 基于kaipoke_monthly_report的实现
    """
    import os

    tasks_by_account = {}

    # 默认账号信息
    default_corp_id_env = common_config.get('corporation_id_env', 'KAIPOKE_CORPORATION_ID')
    default_login_id_env = common_config.get('login_id_env', 'KAIPOKE_MEMBER_LOGIN_ID')
    default_password_env = common_config.get('password_env', 'KAIPOKE_PASSWORD')

    default_corporation_id = os.getenv(default_corp_id_env)
    default_member_login_id = os.getenv(default_login_id_env)
    default_password = os.getenv(default_password_env)

    default_account_key = f"{default_corporation_id}_{default_member_login_id}"

    for task_config in tasks:
        # 检查是否有特殊登录信息
        task_corporation_id_env = task_config.get('corporation_id_env')
        task_member_login_id_env = task_config.get('member_login_id_env')
        task_password_env = task_config.get('password_env')

        if task_corporation_id_env and task_member_login_id_env and task_password_env:
            # 使用特殊账号
            task_corporation_id = os.getenv(task_corporation_id_env)
            task_member_login_id = os.getenv(task_member_login_id_env)
            task_password = os.getenv(task_password_env)

            if task_corporation_id and task_member_login_id and task_password:
                account_key = f"{task_corporation_id}_{task_member_login_id}"
                if account_key not in tasks_by_account:
                    tasks_by_account[account_key] = {
                        'corporation_id': task_corporation_id,
                        'member_login_id': task_member_login_id,
                        'password': task_password,
                        'tasks': []
                    }
                tasks_by_account[account_key]['tasks'].append(task_config)
                logger.info(f"🔑 据点 {task_config.get('facility_name')} 使用账号: {account_key}")
            else:
                # 环境变量未设置，使用默认账号
                if default_account_key not in tasks_by_account:
                    tasks_by_account[default_account_key] = {
                        'corporation_id': default_corporation_id,
                        'member_login_id': default_member_login_id,
                        'password': default_password,
                        'tasks': []
                    }
                tasks_by_account[default_account_key]['tasks'].append(task_config)
        else:
            # 使用默认账号
            if default_account_key not in tasks_by_account:
                tasks_by_account[default_account_key] = {
                    'corporation_id': default_corporation_id,
                    'member_login_id': default_member_login_id,
                    'password': default_password,
                    'tasks': []
                }
            tasks_by_account[default_account_key]['tasks'].append(task_config)

    logger.info(f"📊 任务分组完成: {len(tasks_by_account)} 个账号，总计 {len(tasks)} 个据点")
    for account_key, account_info in tasks_by_account.items():
        corp_id = account_info['corporation_id']
        member_id = account_info['member_login_id']
        task_count = len(account_info['tasks'])
        logger.info(f"   账号 {corp_id}_{member_id}: {task_count} 个据点")

    return tasks_by_account


async def process_account_facilities(tasks: list, common_config: dict,
                                   sheets_client: SheetsClient, drive_client: DriveClient,
                                   selector_executor: SelectorExecutor):
    """
    处理单个账号的所有据点
    🆕 基于原有的批量处理逻辑
    """
    total_facilities = len(tasks)
    batch_size = common_config.get('batch_size', 5)

    # 分批处理据点
    for batch_start in range(0, total_facilities, batch_size):
        batch_end = min(batch_start + batch_size, total_facilities)
        batch_tasks = tasks[batch_start:batch_end]

        logger.info(f"📦 处理批次: {batch_start + 1}-{batch_end}/{total_facilities}")

        # 处理当前批次的据点
        for i, task_config in enumerate(batch_tasks):
            facility_index = batch_start + i + 1
            logger.info(f"🏢 开始处理据点 {facility_index}/{total_facilities}: {task_config.get('facility_name')}")

            try:
                await process_single_facility(task_config, common_config, sheets_client, drive_client, selector_executor)
                logger.info(f"✅ 据点 {facility_index} 处理完成")
            except Exception as e:
                logger.error(f"❌ 据点 {facility_index} 处理失败: {e}", exc_info=True)
                # 继续处理下一个据点，不中断整个流程

            # 据点间短暂休息，避免系统负载过高
            if i < len(batch_tasks) - 1:
                await asyncio.sleep(2)

        # 批次间休息，进一步降低风险
        if batch_end < total_facilities:
            logger.info(f"⏸️ 批次完成，休息10秒后继续...")
            await asyncio.sleep(10)


async def async_run(config: dict):
    """
    Kaipoke Billing Workflow主入口
    批量处理20个据点的账单数据下载和处理
    🆕 支持多账号切换（基于kaipoke_monthly_report的实现）
    """
    logger.info("🚀 开始执行Kaipoke Billing Workflow（MCP強化版）")

    # 从配置获取基本信息
    common_config = config.get('config', {})
    tasks = config.get('tasks', [])

    if not tasks:
        logger.error("❌ 配置文件中没有定义任务")
        return

    # 🆕 按账号分组任务（基于kaipoke_monthly_report的实现）
    tasks_by_account = await group_tasks_by_account(tasks, common_config)

    # 初始化客户端（注意：不设置默认spreadsheet_id，每个据点单独处理）
    sheets_client = SheetsClient()
    drive_client = DriveClient()
    browser_manager = BrowserManager()

    try:
        # 一次性浏览器启动和登录
        await browser_manager.start_browser(headless=False)
        page = await browser_manager.get_page()
        selector_executor = SelectorExecutor(page)

        # 🆕 MCP备份工具初始化
        await selector_executor.initialize_mcp_fallback()

        # 🆕 按账号处理任务
        for account_key, account_info in tasks_by_account.items():
            logger.info(f"🔑 切换到账号: {account_key}")

            # 登录当前账号
            login_url = common_config.get('login_url')
            login_success = await kaipoke_login_direct(
                page,
                account_info['corporation_id'],
                account_info['member_login_id'],
                account_info['password'],
                login_url
            )

            if not login_success:
                logger.error(f"❌ 账号 {account_key} 登录失败，跳过该账号的所有任务")
                continue

            logger.info(f"✅ 账号 {account_key} 登录成功，开始处理 {len(account_info['tasks'])} 个据点")

            # 🆕 登录后处理弹窗
            await handle_post_login_popups(page, f"billing_{account_key}")

            # 处理当前账号的所有据点
            await process_account_facilities(
                account_info['tasks'], common_config,
                sheets_client, drive_client, selector_executor
            )
        
        logger.info("✅ Kaipoke Billing Workflow全部完成")
        
    except Exception as e:
        logger.error(f"❌ 工作流执行异常: {e}", exc_info=True)
    finally:
        await browser_manager.close_browser()


async def process_single_facility(task_config: dict, common_config: dict, 
                                sheets_client: SheetsClient, drive_client: DriveClient,
                                selector_executor: SelectorExecutor):
    """
    处理单个据点的账单数据
    基于RPA代码的完整流程
    """
    facility_name = task_config.get('facility_name')
    element_text = task_config.get('element_text')
    service_type = task_config.get('service_type')

    # 🆕 正确提取facility_id：从element_text中提取数字部分
    facility_id = ''
    if element_text and '/' in element_text:
        facility_id = element_text.split('/')[-1]  # 提取最后的数字部分

    # 传递facility_id到selector_executor
    setattr(selector_executor, 'facility_id', facility_id)
    logger.info(f"🔍 据点ID: {facility_id}")

    logger.info(f"🏢 开始处理据点: {facility_name} ({service_type})")

    try:
        # 0. 据点处理前重置选择器状态，确保每个据点都有干净的开始
        await reset_selector_state_for_facility(selector_executor)

        # 1. 导航到据点选择页面
        await navigate_to_facility_selection(selector_executor)

        # 2. 选择目标据点（必须先点击进入目标据点页面！）
        await select_target_facility(selector_executor, element_text, facility_name)
        # 2.1 进入目标据点后，确认页面已切换
        page = selector_executor.page
        await page.wait_for_load_state('networkidle', timeout=20000)
        await page.wait_for_timeout(2000)
        logger.info(f"✅ 已进入据点页面: {facility_name}")

        # 3. 导航到各种情报输出页面（此时才可用专用选择器）
        await navigate_to_billing_export(selector_executor, task_config)

        # 4. 下载CSV文件
        import os
        download_path = common_config.get('download_path', '/tmp/kaipoke_billing_downloads')
        os.makedirs(download_path, exist_ok=True)
        csv_file_path = await download_billing_csv(selector_executor, download_path, facility_name, service_type)
        if not csv_file_path:
            raise Exception("CSV文件下载失败")

        # 5. 处理CSV文件（编码转换）
        processed_file_path = await process_csv_file(csv_file_path, facility_name, service_type)

        # 6. 创建包含多个工作表的XLSX文件（保持原有数据分类功能）
        import_table_name = task_config.get('import_table_name', f'{facility_name}_{service_type}').strip()
        xlsx_file_path, classified_data = await create_xlsx_with_multiple_sheets(
            processed_file_path, import_table_name, facility_name, service_type
        )

        # 7. 🆕 使用kanamic成功模式上传XLSX文件到Google Drive
        gdrive_folder_id = common_config.get('gdrive_folder_id')
        if gdrive_folder_id:
            logger.info(f"📤 上传XLSX文件到Google Drive: {xlsx_file_path}")
            try:
                # 🆕 参考kanamic成功模式：确保文件存在后再上传
                import os
                if os.path.exists(xlsx_file_path):
                    drive_file_id = drive_client.upload_file(xlsx_file_path, gdrive_folder_id)
                    if drive_file_id:
                        logger.info(f"✅ XLSX文件上传成功，File ID: {drive_file_id}")

                        # 🆕 清理临时文件（参考kanamic模式）
                        try:
                            os.remove(processed_file_path)  # 删除处理后的CSV文件
                            logger.info(f"🗑️ 清理临时CSV文件: {processed_file_path}")
                        except Exception as cleanup_error:
                            logger.warning(f"⚠️ 临时文件清理失败: {cleanup_error}")
                    else:
                        logger.warning("⚠️ XLSX文件上传失败，但继续处理后续步骤")
                else:
                    logger.error(f"❌ XLSX文件不存在: {xlsx_file_path}")
            except Exception as drive_error:
                logger.warning(f"⚠️ Google Drive上传失败: {drive_error}")
                logger.info("📁 XLSX文件已保存在本地，继续处理后续步骤")
                logger.info(f"📂 本地文件路径: {xlsx_file_path}")
        else:
            logger.info("⚠️ 未配置gdrive_folder_id，跳过Google Drive上传")

        # 8. 🆕 增强Google Sheets写入逻辑，添加详细的错误处理
        target_spreadsheet_id = task_config.get('sheet_id', '').strip()  # 目标表格ID
        target_sheet_name = task_config.get('target_sheet_name', 'インポート')  # 目标工作表名

        logger.info(f"📊 准备写入Google Sheets:")
        logger.info(f"  - 目标表格ID: {target_spreadsheet_id}")
        logger.info(f"  - 目标工作表: {target_sheet_name}")

        import pandas as pd
        current_service_data = classified_data.get('current_month_service', [])
        current_service_df = pd.DataFrame(current_service_data)

        if not current_service_df.empty:
            logger.info(f"📋 当月サービス提供数据: {len(current_service_df)} 行")

            try:
                # 🆕 验证目标表格是否可访问
                logger.info("🔍 验证目标表格访问权限...")
                spreadsheet_info = sheets_client.service.spreadsheets().get(
                    spreadsheetId=target_spreadsheet_id
                ).execute()
                logger.info(f"✅ 目标表格可访问: {spreadsheet_info.get('properties', {}).get('title', 'Unknown')}")

                # 🆕 使用增强的批量写入策略
                logger.info(f"📊 准备写入Google Sheets:")
                logger.info(f"  - 目标表格ID: {target_spreadsheet_id}")
                logger.info(f"  - 目标工作表: {target_sheet_name}")
                logger.info(f"  - 📋 当月サービス提供数据: {len(current_service_df)} 行")

                # 使用增强的写入策略，包含重试和分批机制
                success = await write_to_sheets_with_enhanced_retry(
                    sheets_client, target_spreadsheet_id, target_sheet_name,
                    current_service_data, len(current_service_df)
                )

                if success:
                    logger.info(f"✅ 成功导入 {len(current_service_df)} 条当月サービス提供数据")
                else:
                    logger.error(f"❌ Google Sheets写入失败，但XLSX文件已保存")
                    # 不抛出异常，允许工作流继续处理其他据点

            except Exception as sheets_error:
                logger.error(f"❌ Google Sheets写入失败: {sheets_error}")
                logger.info("📁 数据已保存在本地XLSX文件中，可手动导入")
                # 不抛出异常，继续处理后续步骤
        else:
            logger.info("⚠️ 没有当月サービス提供数据需要导入")

        # 9. 处理完成后，返回据点选择页面（为下一个据点做准备）
        await navigate_back_to_facility_selection(selector_executor)
        logger.info(f"✅ 据点 {facility_name} 处理完成")

    except Exception as e:
        logger.error(f"❌ 据点 {facility_name} 处理失败: {e}", exc_info=True)
        # 即使出错也尝试回到据点选择页面
        try:
            await navigate_back_to_facility_selection(selector_executor)
        except:
            pass
        raise


async def reset_selector_state_for_facility(selector_executor: SelectorExecutor):
    """
    重置选择器状态，确保每个据点处理都有干净的开始
    🆕 解决据点间选择器失效问题 - 简化版本
    """
    try:
        logger.info("🔄 重置选择器状态，为新据点处理做准备")

        # 简单等待，确保页面状态稳定
        page = selector_executor.page
        await page.wait_for_timeout(3000)

        # 确保页面完全加载
        await page.wait_for_load_state('networkidle', timeout=10000)

        logger.info("✅ 选择器状态重置完成")

    except Exception as e:
        logger.warning(f"⚠️ 选择器状态重置失败，继续处理: {e}")


async def navigate_to_facility_selection(selector_executor: SelectorExecutor):
    """
    导航到据点选择页面
    🆕 修复第二个据点的导航逻辑
    """
    page = selector_executor.page

    try:
        current_url = page.url
        logger.debug(f"📄 当前页面URL: {current_url}")

        # 🆕 简化逻辑：如果已经在据点选择页面，直接返回
        if "COM020101.do" in current_url:
            logger.info("✅ 已在据点选择页面，无需导航")
            return

        # 🆕 对于其他所有情况，直接导航到据点选择页面
        logger.info("🔄 导航到据点选择页面...")
        facility_selection_url = "https://r.kaipoke.biz/kaipokebiz/common/COM020101.do?fromBP=true"
        await page.goto(facility_selection_url, wait_until='networkidle', timeout=45000)
        await page.wait_for_timeout(3000)

        # 验证是否成功到达据点选择页面
        current_url = page.url
        if "COM020101.do" in current_url:
            logger.info("✅ 成功导航到据点选择页面")
        else:
            logger.warning(f"⚠️ 可能未正确到达据点选择页面，当前URL: {current_url}")

    except Exception as e:
        logger.error(f"❌ 导航到据点选择页面失败: {e}")
        raise


async def select_target_facility(selector_executor: SelectorExecutor, element_text: str, facility_name: str):
    """🆕 选择目标据点（增强稳定性和调试信息）"""
    page = selector_executor.page

    # 使用XPath选择器查找据点
    facility_xpath = f'//a[contains(text(), "{element_text}")]'

    try:
        logger.info(f"🔍 查找据点: {facility_name} ({element_text})")

        # 🆕 添加页面状态检查
        current_url = page.url
        logger.info(f"📄 当前页面URL: {current_url}")

        # 确保在据点选择页面
        if "COM020101.do" not in current_url:
            logger.warning(f"⚠️ 不在据点选择页面，当前URL: {current_url}")

        # 等待页面完全加载
        await page.wait_for_load_state('networkidle', timeout=15000)
        await page.wait_for_timeout(1000)

        # 首先等待元素出现
        logger.info(f"⏳ 等待据点元素出现: {element_text}")
        await page.wait_for_selector(f'xpath={facility_xpath}', timeout=15000)
        logger.info(f"✅ 找到据点元素: {element_text}")

        # 立即点击据点
        logger.info(f"🖱️ 立即点击据点: {element_text}")
        await page.click(f'xpath={facility_xpath}', timeout=10000)

        # 等待页面跳转
        await page.wait_for_timeout(3000)
        await page.wait_for_load_state('networkidle', timeout=20000)

        # 验证是否成功进入据点页面
        new_url = page.url
        logger.info(f"📄 跳转后页面URL: {new_url}")
        logger.info(f"✅ {facility_name}のページに移動しました")

    except Exception as e:
        logger.error(f"❌ 据点选择失败: {element_text}, {e}")
        # 添加更多调试信息
        try:
            current_url = page.url
            logger.error(f"❌ 失败时页面URL: {current_url}")
        except:
            pass
        raise Exception(f"选择据点失败: {facility_name}")


async def navigate_to_billing_export(selector_executor: SelectorExecutor, task_config: dict):
    """
    导航到各种情报输出页面
    基于RPA代码的导航流程
    🆕 增强容错机制，解决据点间选择器失效问题
    """
    page = selector_executor.page
    # 新增：尝试获取facility_id
    facility_id = getattr(selector_executor, 'facility_id', None)
    try:
        # 🆕 等待页面完全加载
        await page.wait_for_load_state('networkidle', timeout=10000)
        await page.wait_for_timeout(2000)

        # 🆕 根据据点类型选择正确的各種情報出力选择器
        logger.info("📊 悬停并点击各种情报输出菜单")

        # 🆕 根据服务类型确定正确的选择器位置
        service_type = task_config.get('service_type', '')

        # 根据服务类型选择对应的菜单悬停选择器
        if service_type == '訪問介護':
            hover_selector = '#jsddm > li:nth-child(11) img'
            click_selector = '#jsddm > li:nth-child(11) a'
            logger.info(f"🔵 訪問介護据点使用第11个菜单位置")
        elif service_type == '障害者総合支援':
            hover_selector = '#jsddm > :nth-child(8) img'
            click_selector = '#jsddm > :nth-child(8) a'
            logger.info(f"🟦 障害者総合支援据点使用第8个菜单位置")
        elif service_type == '訪問看護':
            hover_selector = '.dropdown:nth-child(8) .dropdown-toggle'
            click_selector = '.dropdown:nth-child(8) li a'
            logger.info(f"🟦 訪問看護据点使用dropdown第8个菜单位置")
        elif service_type == '通所介護':
            hover_selector = '#jsddm > li:nth-of-type(7) img'
            click_selector = '#jsddm > li:nth-of-type(7) a'
            logger.info(f"🟡 通所介護据点使用第7个菜单位置")
        elif service_type == '福祉用具貸与':
            hover_selector = '#jsddm > li:nth-of-type(7) img'
            click_selector = '#jsddm > li:nth-of-type(7) a'
            logger.info(f"🟡 福祉用具貸与据点使用第7个菜单位置")
        else:
            # 默认使用第11个位置
            hover_selector = '#jsddm > li:nth-child(11) img'
            click_selector = '#jsddm > li:nth-child(11) a'
            logger.info(f"🔵 {service_type}据点使用默认第11个菜单位置")

        # 1. 悬停在各种情报输出菜单
        hover_success = False
        try:
            await page.hover(hover_selector, timeout=5000)
            hover_success = True
            logger.info(f"✅ 悬停成功: {hover_selector}")
        except Exception as e:
            logger.warning(f"⚠️ 悬停失败 {hover_selector}: {e}")

            # 备用策略：尝试smart_hover
            try:
                logger.info("🔄 尝试smart_hover备用策略")
                hover_success = await selector_executor.smart_hover(
                    workflow="kaipoke_billing", category="navigation", element="info_output_menu"
                )
                if hover_success:
                    logger.info("✅ smart_hover备用策略成功")
                else:
                    logger.warning("❌ smart_hover备用策略失败")
            except Exception as smart_hover_error:
                logger.error(f"❌ smart_hover备用策略异常: {smart_hover_error}")
                hover_success = False

        if not hover_success:
            raise Exception("所有悬停策略都失败")

        await page.wait_for_timeout(500)  # 缩短等待时间

        # 2. 点击出力対象選択
        logger.info("📋 点击出力対象選択")
        click_success = await selector_executor.smart_click(
            workflow="kaipoke_billing", category="navigation", element="output_target_selection",
            target_text="出力対象選択"
        )

        if not click_success:
            # 备用点击策略：使用对应的直接选择器
            try:
                await page.click(click_selector, timeout=5000)
                logger.info(f"✅ 备用点击成功: {click_selector}")
            except Exception as e:
                logger.error(f"❌ 所有点击策略都失败: {e}")
                raise Exception("点击出力対象選択失败")
        
        await page.wait_for_timeout(2000)
        logger.info("✅ 成功导航到各种情报输出页面")
        
    except Exception as e:
        logger.error(f"❌ 导航到各种情报输出页面失败: {e}")
        raise


async def download_billing_csv(selector_executor: SelectorExecutor, download_path: str,
                              facility_name: str, service_type: str):
    """
    下载账单CSV文件
    🆕 根据服务类型使用不同的选择器
    """
    page = selector_executor.page
    # 新增：尝试获取facility_id
    facility_id = getattr(selector_executor, 'facility_id', None)
    try:
        # 1. 点击利用者请求选项 - 根据服务类型选择正确的选择器
        logger.info(f"👤 点击利用者請求 (服务类型: {service_type})")

        # 🆕 等待页面元素加载
        await page.wait_for_timeout(2000)

        # 🆕 根据服务类型选择正确的利用者請求选择器
        logger.info(f"🔍 服务类型: '{service_type}' - 选择对应的利用者請求选择器")

        # 根据服务类型选择对应的利用者請求选择器
        if service_type == '訪問介護':
            user_billing_selector = 'div:nth-of-type(6) tr:nth-of-type(2) span'
            logger.info(f"🔵 訪問介護据点使用选择器: {user_billing_selector}")
        elif service_type == '障害者総合支援':
            user_billing_selector = 'div:nth-of-type(5) tr:nth-of-type(2) span'
            logger.info(f"🟦 障害者総合支援据点使用选择器: {user_billing_selector}")
        elif service_type == '訪問看護':
            user_billing_selector = '#billingInfo_tooltip'
            logger.info(f"🟦 訪問看護据点使用专用选择器: {user_billing_selector}")
        elif service_type == '通所介護':
            user_billing_selector = 'div:nth-of-type(8) tr:nth-of-type(2) span'
            logger.info(f"🟡 通所介護据点使用选择器: {user_billing_selector}")
        elif service_type == '福祉用具貸与':
            user_billing_selector = 'div:nth-of-type(6) tr:nth-of-type(2) span'
            logger.info(f"🟡 福祉用具貸与据点使用选择器: {user_billing_selector}")
        else:
            # 默认使用通用选择器
            user_billing_selector = 'text=利用者請求'
            logger.info(f"🔵 {service_type}据点使用默认选择器: {user_billing_selector}")

        # 点击利用者請求
        try:
            logger.info(f"🔄 等待并点击利用者請求选择器: {user_billing_selector}")
            await page.wait_for_selector(user_billing_selector, timeout=10000)
            await page.click(user_billing_selector, timeout=10000)
            logger.info(f"✅ {service_type}据点利用者請求选择器点击成功")
        except Exception as e:
            logger.error(f"❌ {service_type}据点利用者請求选择器失败: {e}")
            # 备用：使用smart_click
            try:
                logger.info("🔄 尝试备用smart_click方法")
                click_success = await selector_executor.smart_click(
                    workflow="kaipoke_billing", category="billing", element="user_billing",
                    target_text="利用者請求"
                )
                if click_success:
                    logger.info("✅ smart_click利用者請求成功")
                else:
                    logger.error("❌ smart_click利用者請求失败")
                    raise Exception(f"{service_type}据点所有利用者請求选择器都失败")
            except Exception as smart_click_error:
                logger.error(f"❌ smart_click异常: {smart_click_error}")
                raise Exception(f"{service_type}据点所有利用者請求选择器都失败")

        await page.wait_for_timeout(1000)

        # 2. 点击CSV输出按钮
        logger.info("📄 点击CSV出力按钮")

        # 设置下载监听
        async with page.expect_download(timeout=30000) as download_info:
            try:
                logger.info("🔄 尝试smart_click点击CSV出力")
                csv_click_success = await selector_executor.smart_click(
                    workflow="kaipoke_billing", category="billing", element="csv_export",
                    target_text="CSV出力"
                )
                if csv_click_success:
                    logger.info("✅ smart_click CSV出力成功")
                else:
                    logger.warning("❌ smart_click CSV出力失败，尝试备用选择器")
                    raise Exception("smart_click失败，使用备用策略")
            except Exception as smart_click_error:
                logger.warning(f"⚠️ smart_click异常: {smart_click_error}")
                # 备用选择器（基于RPA代码）
                try:
                    logger.info("🔄 使用备用选择器点击CSV出力")
                    await page.click('#form\\:export img')
                    logger.info("✅ 使用备用选择器点击CSV出力成功")
                except Exception as e:
                    logger.error(f"❌ 所有CSV出力策略都失败: {e}")
                    raise

        download = await download_info.value

        # 3. 生成文件名（基于RPA代码的命名模式）
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        csv_filename = f"利用者請求_{facility_name}_{service_type}_{timestamp}.csv"
        csv_path = os.path.join(download_path, csv_filename)

        await download.save_as(csv_path)
        logger.info(f"✅ CSV文件下载成功: {csv_path}")

        # 等待文件完全写入
        await page.wait_for_timeout(2000)

        return csv_path

    except Exception as e:
        logger.error(f"❌ CSV文件下载失败: {e}")
        return None


async def process_csv_file(csv_file_path: str, facility_name: str, service_type: str):
    """
    处理CSV文件：编码转换
    基于RPA代码的文件处理流程
    🆕 增强编码处理，支持多种日文编码
    """
    try:
        logger.info(f"🔄 开始处理CSV文件: {csv_file_path}")

        # 1. 🆕 尝试多种编码读取CSV文件
        df = None
        encodings_to_try = [
            'shift_jis',      # 标准Shift_JIS
            'cp932',          # Windows日文编码（更常用）
            'euc-jp',         # EUC-JP编码
            'iso-2022-jp',    # ISO-2022-JP编码
            'utf-8',          # UTF-8编码（备用）
        ]

        successful_encoding = None

        for encoding in encodings_to_try:
            try:
                logger.info(f"🔍 尝试编码: {encoding}")
                df = pd.read_csv(csv_file_path, encoding=encoding)
                successful_encoding = encoding
                logger.info(f"✅ 编码 {encoding} 读取成功，共{len(df)}行数据")
                break
            except UnicodeDecodeError as e:
                logger.debug(f"❌ 编码 {encoding} 失败: {e}")
                continue
            except Exception as e:
                logger.debug(f"❌ 编码 {encoding} 其他错误: {e}")
                continue

        if df is None:
            # 最后尝试：使用文件级别的错误处理策略
            logger.warning("⚠️ 所有标准编码都失败，尝试文件级别错误处理")
            try:
                # 先读取文件内容，手动处理编码错误
                with open(csv_file_path, 'rb') as f:
                    raw_content = f.read()

                # 尝试解码并忽略错误
                try:
                    decoded_content = raw_content.decode('shift_jis', errors='ignore')
                except:
                    decoded_content = raw_content.decode('cp932', errors='ignore')

                # 将处理后的内容写入临时文件
                import tempfile
                with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8') as temp_file:
                    temp_file.write(decoded_content)
                    temp_file_path = temp_file.name

                # 读取临时文件
                df = pd.read_csv(temp_file_path, encoding='utf-8')
                successful_encoding = 'manual error handling'
                logger.info(f"✅ 使用错误处理策略读取成功，共{len(df)}行数据")

                # 清理临时文件
                os.unlink(temp_file_path)

            except Exception as e:
                logger.error(f"❌ 所有编码尝试都失败: {e}")
                raise Exception(f"无法读取CSV文件，所有编码都失败: {csv_file_path}")

        logger.info(f"📊 最终使用编码: {successful_encoding}")
        logger.info(f"📋 列名: {list(df.columns)}")

        # 2. 生成新文件名（基于RPA代码的命名模式）
        current_month = datetime.now().strftime('%Y年%m月')
        new_filename = f"{current_month}ご利用分_利用者請求_{facility_name}_{service_type}_コード変換.csv"
        new_file_path = os.path.join(os.path.dirname(csv_file_path), new_filename)

        # 3. 保存为UTF-8编码
        df.to_csv(new_file_path, encoding='utf-8', index=False)
        logger.info(f"✅ 编码转换完成: {new_file_path}")

        # 4. 删除原始文件
        os.remove(csv_file_path)
        logger.info(f"🗑️ 删除原始文件: {csv_file_path}")

        return new_file_path

    except Exception as e:
        logger.error(f"❌ CSV文件处理失败: {e}")
        raise


async def create_xlsx_with_multiple_sheets(csv_file_path: str, import_table_name: str, facility_name: str, service_type: str):
    """
    🆕 创建包含多个工作表的XLSX文件
    1. 读取CSV数据并转换为XLSX
    2. 重命名默认工作表为"当月請求"
    3. 创建"当月サービス提供"和"前月以前請求"工作表
    4. 根据数据分类逻辑填充各工作表
    """
    try:
        # 1. 读取CSV数据
        df = pd.read_csv(csv_file_path, encoding='utf-8')
        logger.info(f"📊 读取CSV数据成功，共 {len(df)} 行")

        # 2. 生成XLSX文件路径
        download_dir = os.path.dirname(csv_file_path)
        xlsx_filename = f"{import_table_name}.xlsx"
        xlsx_path = os.path.join(download_dir, xlsx_filename)

        # 3. 创建工作簿并设置工作表
        wb = Workbook()

        # 重命名默认工作表为"当月請求"
        ws_current_billing = wb.active
        ws_current_billing.title = "当月請求"

        # 创建其他工作表
        ws_current_service = wb.create_sheet("当月サービス提供")
        ws_previous_billing = wb.create_sheet("前月以前請求")

        # 4. 数据分类处理
        classifier = CSVDataClassifier()
        classified_data = classifier.classify_billing_data(df)

        # 验证数据完整性
        classifier.validate_data_integrity(df, classified_data)

        # 5. 🆕 修复数据填充逻辑，使用正确的键名并添加表头

        # 当月請求（原始数据）- 修复键名
        original_data_df = classified_data.get('original_data', pd.DataFrame())
        if not original_data_df.empty:
            # 写入表头
            for c_idx, header in enumerate(original_data_df.columns, 1):
                ws_current_billing.cell(row=1, column=c_idx, value=header)
            # 写入数据
            for r_idx, row in enumerate(original_data_df.values, 2):  # 从第2行开始写数据
                for c_idx, value in enumerate(row, 1):
                    ws_current_billing.cell(row=r_idx, column=c_idx, value=value)
            logger.info(f"✅ 当月請求工作表: {len(original_data_df)} 行数据 + 表头")

        # 当月サービス提供 - 添加表头
        current_service_df = classified_data.get('current_month_service', pd.DataFrame())
        if not current_service_df.empty:
            # 写入表头
            for c_idx, header in enumerate(current_service_df.columns, 1):
                ws_current_service.cell(row=1, column=c_idx, value=header)
            # 写入数据
            for r_idx, row in enumerate(current_service_df.values, 2):  # 从第2行开始写数据
                for c_idx, value in enumerate(row, 1):
                    ws_current_service.cell(row=r_idx, column=c_idx, value=value)
            logger.info(f"✅ 当月サービス提供工作表: {len(current_service_df)} 行数据 + 表头")

        # 前月以前請求 - 添加表头
        previous_billing_df = classified_data.get('previous_month_billing', pd.DataFrame())
        if not previous_billing_df.empty:
            # 写入表头
            for c_idx, header in enumerate(previous_billing_df.columns, 1):
                ws_previous_billing.cell(row=1, column=c_idx, value=header)
            # 写入数据
            for r_idx, row in enumerate(previous_billing_df.values, 2):  # 从第2行开始写数据
                for c_idx, value in enumerate(row, 1):
                    ws_previous_billing.cell(row=r_idx, column=c_idx, value=value)
            logger.info(f"✅ 前月以前請求工作表: {len(previous_billing_df)} 行数据 + 表头")

        # 6. 🆕 调整列宽以适应内容
        logger.info("🔧 调整XLSX文件列宽...")
        adjust_column_widths(ws_current_billing, "当月請求")
        adjust_column_widths(ws_current_service, "当月サービス提供")
        adjust_column_widths(ws_previous_billing, "前月以前請求")

        # 7. 保存XLSX文件
        wb.save(xlsx_path)
        logger.info(f"✅ XLSX文件创建成功: {xlsx_path}")
        logger.info(f"📊 工作表数据统计:")
        logger.info(f"  - 当月請求（原始数据）: {len(original_data_df)} 行")
        logger.info(f"  - 当月サービス提供: {len(current_service_df)} 行")
        logger.info(f"  - 前月以前請求: {len(previous_billing_df)} 行")

        return xlsx_path, classified_data

    except Exception as e:
        logger.error(f"❌ XLSX文件创建失败: {e}")
        raise


def get_column_letter(col_num: int) -> str:
    """
    将列数转换为Excel列字母（A, B, C, ..., Z, AA, AB, ...）
    🆕 通用列范围计算函数，避免代码重复
    """
    result = ""
    while col_num > 0:
        col_num -= 1
        result = chr(col_num % 26 + ord('A')) + result
        col_num //= 26
    return result


def calculate_dynamic_range(data: list, sheet_name: str, start_row: int, end_row: int) -> str:
    """
    根据数据动态计算Google Sheets范围
    🆕 增强Sheet名称处理，避免URL编码问题，解决列数不匹配问题
    """
    try:
        # 🆕 确保sheet_name不包含特殊字符，避免URL编码问题
        safe_sheet_name = sheet_name.strip() if sheet_name else "Sheet1"

        # 🆕 记录原始sheet名称用于调试
        if sheet_name != safe_sheet_name:
            logger.debug(f"📝 Sheet名称处理: '{sheet_name}' -> '{safe_sheet_name}'")

        if data and len(data) > 0:
            # 计算最大列数
            max_cols = max(len(row) for row in data if hasattr(row, '__len__'))
            if max_cols > 0:
                end_col = get_column_letter(max_cols)
                # 🆕 直接使用safe_sheet_name，避免额外编码
                range_str = f"{safe_sheet_name}!A{start_row}:{end_col}{end_row}"
                logger.debug(f"📊 动态范围计算: {max_cols} 列 -> {range_str}")
                return range_str

        # 默认范围（如果无法计算）
        default_range = f"{safe_sheet_name}!A{start_row}:T{end_row}"
        logger.debug(f"📊 使用默认范围: {default_range}")
        return default_range

    except Exception as e:
        logger.warning(f"⚠️ 动态范围计算失败: {e}")
        # 🆕 安全备用方案
        safe_fallback = "Sheet1" if not sheet_name else sheet_name.strip()
        return f"{safe_fallback}!A{start_row}:T{end_row}"


def clean_data_for_sheets(data) -> list:
    """
    清理数据以适应Google Sheets API
    🆕 处理DataFrame、NaN、特殊字符、编码问题
    """

    # 🆕 首先处理DataFrame类型
    if hasattr(data, 'values'):  # 检查是否是DataFrame
        logger.info("🔄 检测到DataFrame，转换为list格式")
        try:
            # 将DataFrame转换为list，包括处理NaN值
            import pandas as pd
            import numpy as np

            # 替换所有NaN和inf值
            data_cleaned = data.replace([np.nan, np.inf, -np.inf], '')

            # 转换为纯Python list
            data = data_cleaned.values.tolist()

            # 🆕 确保所有元素都是基本Python类型
            converted_data = []
            for row in data:
                converted_row = []
                for cell in row:
                    if hasattr(cell, 'item'):  # pandas scalar
                        try:
                            converted_row.append(cell.item())
                        except Exception:
                            converted_row.append(str(cell))
                    elif isinstance(cell, (np.integer, np.floating)):
                        converted_row.append(cell.item())
                    else:
                        converted_row.append(cell)
                converted_data.append(converted_row)
            data = converted_data

            logger.info(f"✅ DataFrame转换完成: {len(data)} 行")

        except Exception as e:
            logger.warning(f"⚠️ DataFrame转换失败: {e}")
            # 备用转换方法
            try:
                data = [[str(cell) if cell is not None else '' for cell in row] for row in data.values.tolist()]
            except Exception as backup_error:
                logger.error(f"❌ 备用转换也失败: {backup_error}")
                data = []

    # 确保data是list类型
    if not isinstance(data, list):
        logger.warning(f"⚠️ 数据类型不支持: {type(data)}, 尝试转换为list")
        try:
            data = list(data)
        except Exception as e:
            logger.error(f"❌ 数据转换失败: {e}")
            return []

    cleaned_data = []

    for row_idx, row in enumerate(data):
        # 确保row是可迭代的
        if not hasattr(row, '__iter__') or isinstance(row, str):
            logger.warning(f"⚠️ 行 {row_idx} 不是可迭代对象: {type(row)}")
            row = [row]  # 将单个值包装成列表

        cleaned_row = []
        for cell_idx, cell in enumerate(row):
            try:
                # 🆕 处理DataFrame、Series等pandas对象
                if hasattr(cell, 'item'):  # pandas scalar
                    try:
                        cell = cell.item()
                    except Exception:
                        cell = str(cell)

                # 处理None和NaN值
                if cell is None:
                    cleaned_cell = ""
                elif isinstance(cell, float) and (math.isnan(cell) or math.isinf(cell)):
                    cleaned_cell = ""
                # 处理数字类型
                elif isinstance(cell, (int, float)):
                    cleaned_cell = str(cell)
                # 处理字符串类型
                elif isinstance(cell, str):
                    # 移除或替换问题字符
                    cleaned_cell = cell.strip()

                    # 🆕 增强Unicode字符处理
                    try:
                        # 确保字符串是有效的UTF-8编码
                        cleaned_cell = cleaned_cell.encode('utf-8', errors='ignore').decode('utf-8')
                    except Exception:
                        cleaned_cell = ""

                    # 替换全角字符为半角字符
                    cleaned_cell = cleaned_cell.replace('－', '-')  # 全角连字符
                    cleaned_cell = cleaned_cell.replace('　', ' ')   # 全角空格
                    cleaned_cell = cleaned_cell.replace('ー', '-')  # 长音符

                    # 🆕 处理特殊的Unicode字符（基于错误信息）
                    cleaned_cell = cleaned_cell.replace('\uff0d', '-')  # 全角连字符的Unicode
                    cleaned_cell = cleaned_cell.replace('\u2212', '-')  # 数学减号
                    cleaned_cell = cleaned_cell.replace('\u2013', '-')  # en dash
                    cleaned_cell = cleaned_cell.replace('\u2014', '-')  # em dash
                    # 注意：片假名字符是正常的日文字符，不需要替换

                    # 移除控制字符和不可见字符
                    cleaned_cell = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', cleaned_cell)

                    # 🆕 处理零宽字符和其他不可见字符
                    cleaned_cell = re.sub(r'[\u200b-\u200f\u2028-\u202f\u205f-\u206f]', '', cleaned_cell)

                    # 🆕 移除可能导致JSON解析错误的特殊字符
                    cleaned_cell = re.sub(r'[\u0000-\u001f\u007f-\u009f]', '', cleaned_cell)

                    # 确保字符串不会导致JSON解析错误
                    cleaned_cell = cleaned_cell.replace('\n', ' ').replace('\r', ' ')
                    cleaned_cell = cleaned_cell.replace('\t', ' ')  # 制表符
                    cleaned_cell = cleaned_cell.replace('\b', '')   # 退格符
                    cleaned_cell = cleaned_cell.replace('\f', '')   # 换页符

                    # 限制字符串长度（Google Sheets单元格限制）
                    if len(cleaned_cell) > 50000:
                        cleaned_cell = cleaned_cell[:50000] + "..."

                else:
                    # 🆕 处理其他复杂对象（DataFrame、dict等）
                    try:
                        # 尝试转换为字符串，但避免复杂对象
                        if hasattr(cell, '__dict__') or isinstance(cell, (dict, list)):
                            cleaned_cell = ""  # 复杂对象设为空
                        else:
                            cleaned_cell = str(cell)
                    except Exception:
                        cleaned_cell = ""

                cleaned_row.append(cleaned_cell)

            except Exception as e:
                logger.warning(f"⚠️ 清理单元格数据失败 [{row_idx},{cell_idx}]: {type(cell)} -> {e}")
                cleaned_row.append("")  # 失败时使用空字符串

        cleaned_data.append(cleaned_row)

    return cleaned_data


def validate_batch_data(batch_data: list, batch_range: str) -> list:
    """
    验证批次数据的最终检查
    🆕 确保数据符合Google Sheets API要求，处理DataFrame等复杂对象
    """
    try:
        # 🆕 检查batch_data是否为空或None
        if not batch_data:
            logger.warning(f"⚠️ 批次数据为空: {batch_range}")
            return []

        validated_data = []

        for row_idx, row in enumerate(batch_data):
            # 🆕 确保row是可迭代的
            if not hasattr(row, '__iter__') or isinstance(row, str):
                row = [row]

            validated_row = []

            for col_idx, cell in enumerate(row):
                try:
                    # 🆕 处理pandas对象
                    if hasattr(cell, 'item'):  # pandas scalar
                        try:
                            cell = cell.item()
                        except Exception:
                            cell = str(cell)

                    # 最终类型检查
                    if isinstance(cell, str):
                        # 确保字符串是有效的UTF-8
                        validated_cell = cell.encode('utf-8', errors='ignore').decode('utf-8')
                        # 移除可能导致JSON错误的字符
                        validated_cell = validated_cell.replace('\x00', '')
                    elif isinstance(cell, (int, float)):
                        # 数字转字符串，避免NaN问题
                        if math.isnan(cell) or math.isinf(cell):
                            validated_cell = ""
                        else:
                            validated_cell = str(cell)
                    elif cell is None:
                        validated_cell = ""
                    else:
                        # 🆕 处理复杂对象
                        try:
                            if hasattr(cell, '__dict__') or isinstance(cell, (dict, list)):
                                validated_cell = ""  # 复杂对象设为空
                            else:
                                validated_cell = str(cell)
                        except Exception:
                            validated_cell = ""

                    # 🆕 JSON兼容性最终检查
                    try:
                        import json
                        # 尝试JSON序列化测试
                        json.dumps(validated_cell, ensure_ascii=False)
                    except (TypeError, ValueError) as e:
                        logger.debug(f"JSON序列化测试失败: {validated_cell} -> {e}")
                        validated_cell = str(validated_cell) if validated_cell is not None else ""
                        # 再次尝试
                        try:
                            json.dumps(validated_cell, ensure_ascii=False)
                        except:
                            validated_cell = ""  # 最终备用：空字符串

                    validated_row.append(validated_cell)

                except Exception as e:
                    logger.debug(f"验证单元格失败 [{row_idx},{col_idx}]: {type(cell)} -> {e}")
                    validated_row.append("")

            validated_data.append(validated_row)

        return validated_data

    except Exception as e:
        logger.error(f"❌ 批次数据验证失败 {batch_range}: {e}")
        # 返回空数据行，避免API错误
        try:
            if batch_data and len(batch_data) > 0:
                first_row_length = len(batch_data[0]) if hasattr(batch_data[0], '__len__') else 1
                return [[""] * first_row_length] * len(batch_data)
            else:
                return []
        except Exception:
            return []


def adjust_column_widths(worksheet, sheet_name: str):
    """
    自动调整工作表的列宽以适应内容
    🆕 智能列宽调整，提高XLSX文件的可读性
    """
    try:
        logger.info(f"🔧 调整 {sheet_name} 工作表列宽...")

        # 定义不同列的最佳宽度（基于常见的账单数据列）
        column_widths = {
            'A': 12,  # 請求年月
            'B': 15,  # サービス提供年月
            'C': 25,  # サービス事業所
            'D': 15,  # 利用者名
            'E': 20,  # 利用者名カナ
            'F': 15,  # 被保険者番号/受給者証番号
            'G': 12,  # 給付対象金額
            'H': 12,  # 介護保険給付額/介護給付額
            'I': 12,  # 公費負担額/その他軽減額
            'J': 12,  # 社会福祉法人等による軽減額/負担上限月額
            'K': 12,  # その他軽減額/請求済金額
            'L': 12,  # 限度額超過負担額/利用者負担額
            'M': 12,  # 請求済金額/その他費用　保険外
            'N': 12,  # 保険分　利用者負担額/その他費用　諸費用
            'O': 12,  # その他費用　保険外/その他費用　消費税
            'P': 12,  # その他費用　販売/利用者請求額　計
            'Q': 12,  # その他費用　諸費用/利用者入金額
            'R': 12,  # その他費用　消費税
            'S': 12,  # 利用者請求額　計
            'T': 12   # 利用者入金額
        }

        # 应用列宽设置
        for col_letter, width in column_widths.items():
            try:
                worksheet.column_dimensions[col_letter].width = width
            except Exception as e:
                logger.debug(f"设置列 {col_letter} 宽度失败: {e}")

        # 🆕 动态调整：检查实际内容长度并调整
        for row in worksheet.iter_rows(min_row=1, max_row=min(100, worksheet.max_row)):  # 只检查前100行以提高性能
            for cell in row:
                if cell.value:
                    col_letter = get_column_letter(cell.column)
                    content_length = len(str(cell.value))

                    # 如果内容长度超过当前设置，适当增加列宽
                    current_width = worksheet.column_dimensions[col_letter].width
                    if content_length > current_width:
                        # 设置新宽度，但不超过50个字符
                        new_width = min(content_length + 2, 50)
                        worksheet.column_dimensions[col_letter].width = new_width

        logger.info(f"✅ {sheet_name} 列宽调整完成")

    except Exception as e:
        logger.warning(f"⚠️ {sheet_name} 列宽调整失败: {e}")
        # 不抛出异常，因为这不是关键功能


async def classify_billing_data(csv_file_path: str):
    """
    分类账单数据
    基于RPA代码的数据分类逻辑
    """
    try:
        logger.info(f"🔄 开始分类账单数据: {csv_file_path}")

        # 读取CSV文件
        df = pd.read_csv(csv_file_path, encoding='utf-8')
        logger.info(f"📊 读取CSV数据: {len(df)} 行")

        # 使用数据分类器进行分类
        classifier = CSVDataClassifier()
        classified_data = classifier.classify_billing_data(df)

        # 验证数据完整性
        classifier.validate_data_integrity(df, classified_data)

        return classified_data

    except Exception as e:
        logger.error(f"❌ 数据分类失败: {e}")
        raise


async def write_to_sheets_with_enhanced_retry(sheets_client, spreadsheet_id: str, sheet_name: str, data: list, data_count: int) -> bool:
    """
    增强的Google Sheets写入策略
    🆕 多层重试机制，解决API限制和网络问题
    """
    try:
        # 策略1: 使用MultiSheetManager（推荐方式）
        try:
            logger.info("🔄 策略1: 使用MultiSheetManager写入")
            multi_sheet_manager = MultiSheetManager(sheets_client, None)
            await multi_sheet_manager.write_to_existing_spreadsheet(
                spreadsheet_id, sheet_name, {'current_month_service': data}
            )
            logger.info("✅ MultiSheetManager写入成功")
            return True
        except Exception as e:
            logger.warning(f"⚠️ MultiSheetManager写入失败: {e}")

        # 策略2: 直接使用SheetsClient分批写入
        try:
            logger.info("🔄 策略2: 使用SheetsClient分批写入")
            success = await write_data_in_batches(sheets_client, spreadsheet_id, sheet_name, data)
            if success:
                logger.info("✅ SheetsClient分批写入成功")
                return True
        except Exception as e:
            logger.warning(f"⚠️ SheetsClient分批写入失败: {e}")

        # 策略3: 最小批次写入（最后的备用方案）
        try:
            logger.info("🔄 策略3: 最小批次写入（备用方案）")
            success = await write_data_minimal_batches(sheets_client, spreadsheet_id, sheet_name, data)
            if success:
                logger.info("✅ 最小批次写入成功")
                return True
        except Exception as e:
            logger.error(f"❌ 最小批次写入也失败: {e}")

        return False

    except Exception as e:
        logger.error(f"❌ 所有写入策略都失败: {e}")
        return False


async def write_data_in_batches(sheets_client, spreadsheet_id: str, sheet_name: str, data: list) -> bool:
    """分批写入数据到Google Sheets"""
    try:
        # 🆕 检查数据是否为空（兼容DataFrame和list）
        if data is None:
            logger.warning("⚠️ 数据为None，无需写入")
            return True

        # 处理DataFrame类型的数据
        if hasattr(data, 'empty'):  # 检查是否是DataFrame
            if data.empty:
                logger.warning("⚠️ DataFrame为空，无需写入")
                return True
            # 将DataFrame转换为list
            logger.info("🔄 检测到DataFrame，转换为list格式")
            data = data.values.tolist()
        elif isinstance(data, list) and len(data) == 0:
            logger.warning("⚠️ 列表为空，无需写入")
            return True

        # 🆕 数据清理：处理NaN、特殊字符和编码问题
        logger.info("🧹 开始数据清理...")
        cleaned_data = clean_data_for_sheets(data)
        # 🆕 最终强制清洗
        cleaned_data = force_clean_data_for_sheets(cleaned_data)
        logger.info(f"✅ 数据清理完成: {len(data)} → {len(cleaned_data)} 行")
        if cleaned_data and len(cleaned_data) > 0:
            logger.info(f"📝 写入前样本行: {cleaned_data[0]}")

        # 🆕 动态计算清空范围
        if cleaned_data and len(cleaned_data) > 0:
            max_cols = max(len(row) for row in cleaned_data)
            end_col = get_column_letter(max_cols)
            clear_range = f"{sheet_name}!A4:{end_col}"
            logger.info(f"📊 检测到 {max_cols} 列数据，使用清空范围: {clear_range}")
        else:
            clear_range = f"{sheet_name}!A4:T"
            logger.info(f"📊 使用默认清空范围: {clear_range}")

        # 清空目标区域
        sheets_client.service.spreadsheets().values().clear(
            spreadsheetId=spreadsheet_id,
            range=clear_range
        ).execute()
        logger.info(f"✅ Sheet数据清空完成: {sheet_name}, 范围: {clear_range}")

        # 分批写入策略
        batch_sizes = [500, 200, 100, 50, 20]  # 逐步减小批次大小

        for batch_size in batch_sizes:
            try:
                logger.info(f"🔄 尝试批次大小: {batch_size}")

                for i in range(0, len(cleaned_data), batch_size):
                    batch_data = cleaned_data[i:i + batch_size]
                    start_row = 4 + i  # 从A4开始
                    end_row = start_row + len(batch_data) - 1

                    # 🆕 使用通用函数计算动态范围
                    batch_range = calculate_dynamic_range(batch_data, sheet_name, start_row, end_row)

                    # 🆕 最终数据验证
                    validated_batch = validate_batch_data(batch_data, batch_range)

                    # 写入批次数据
                    result = sheets_client.service.spreadsheets().values().update(
                        spreadsheetId=spreadsheet_id,
                        range=batch_range,
                        valueInputOption='USER_ENTERED',
                        body={'values': validated_batch}
                    ).execute()

                    updated_cells = result.get('updatedCells', 0)
                    logger.info(f"✅ 批次写入成功: 行 {start_row}-{end_row}, {updated_cells} 个单元格")

                    # API限制保护
                    await asyncio.sleep(0.5)

                logger.info(f"✅ 所有批次写入完成，批次大小: {batch_size}")
                return True

            except Exception as e:
                logger.warning(f"⚠️ 批次大小 {batch_size} 失败: {e}")
                continue

        return False

    except Exception as e:
        logger.error(f"❌ 分批写入异常: {e}")
        return False


async def write_data_minimal_batches(sheets_client, spreadsheet_id: str, sheet_name: str, data: list) -> bool:
    """最小批次写入（每次只写入几行）"""
    try:
        logger.info("🔄 使用最小批次写入策略（每次5行）")
        if data is None:
            logger.warning("⚠️ 数据为None，无需写入")
            return True
        if hasattr(data, 'empty'):
            if data.empty:
                logger.warning("⚠️ DataFrame为空，无需写入")
                return True
            logger.info("🔄 检测到DataFrame，转换为list格式")
            data = data.values.tolist()
        elif isinstance(data, list) and len(data) == 0:
            logger.warning("⚠️ 列表为空，无需写入")
            return True
        logger.info("🧹 开始数据清理...")
        logger.debug(f"📊 原始数据类型: {type(data)}, 行数: {len(data) if hasattr(data, '__len__') else 'Unknown'}")
        cleaned_data = clean_data_for_sheets(data)
        # 🆕 最终强制清洗
        cleaned_data = force_clean_data_for_sheets(cleaned_data)
        logger.info(f"✅ 数据清理完成: {len(data)} → {len(cleaned_data)} 行")
        if cleaned_data and len(cleaned_data) > 0:
            logger.info(f"📝 写入前样本行: {cleaned_data[0]}")
        batch_size = 5
        success_count = 0

        for i in range(0, len(cleaned_data), batch_size):
            try:
                batch_data = cleaned_data[i:i + batch_size]
                start_row = 4 + i
                end_row = start_row + len(batch_data) - 1

                # 🆕 使用通用函数计算动态范围
                batch_range = calculate_dynamic_range(batch_data, sheet_name, start_row, end_row)

                # 🆕 最终数据验证
                validated_batch = validate_batch_data(batch_data, batch_range)

                # 🆕 API调用前的详细日志
                logger.debug(f"📤 准备API调用:")
                logger.debug(f"  - Spreadsheet ID: {spreadsheet_id}")
                logger.debug(f"  - Range: {batch_range}")
                logger.debug(f"  - 数据行数: {len(validated_batch)}")
                logger.debug(f"  - 数据列数: {len(validated_batch[0]) if validated_batch and len(validated_batch) > 0 else 0}")

                result = sheets_client.service.spreadsheets().values().update(
                    spreadsheetId=spreadsheet_id,
                    range=batch_range,
                    valueInputOption='USER_ENTERED',
                    body={'values': validated_batch}
                ).execute()

                success_count += len(batch_data)
                logger.info(f"✅ 微批次写入: 行 {start_row}-{end_row}")

                # 更长的等待时间
                await asyncio.sleep(2)

            except Exception as e:
                # 🆕 详细的错误分析和日志
                error_msg = str(e)
                logger.warning(f"⚠️ 微批次写入失败: 行 {start_row}-{end_row}")
                logger.warning(f"   错误类型: {type(e).__name__}")
                logger.warning(f"   错误信息: {error_msg}")

                # 🆕 特殊错误类型的额外处理
                if "Invalid JSON payload" in error_msg:
                    logger.error("🔍 JSON编码错误详细分析:")
                    logger.error(f"   - Sheet名称: '{sheet_name}'")
                    logger.error(f"   - Range: {batch_range}")
                    logger.error(f"   - 批次数据行数: {len(batch_data)}")
                    if batch_data and len(batch_data) > 0:
                        logger.error(f"   - 第一行数据: {str(batch_data[0])[:200]}...")
                elif "HttpError" in error_msg:
                    logger.error("🔍 HTTP错误详细分析:")
                    logger.error(f"   - 可能是API配额限制或网络问题")
                    logger.error(f"   - 建议增加等待时间或检查API权限")

                continue

        if success_count > 0:
            logger.info(f"✅ 最小批次写入完成: {success_count}/{len(cleaned_data)} 行成功")
            return True
        else:
            return False

    except Exception as e:
        logger.error(f"❌ 最小批次写入异常: {e}")
        return False


async def navigate_back_to_facility_selection(selector_executor: SelectorExecutor):
    """
    从当前页面导航回据点选择页面
    🆕 增强稳定性和容错能力
    """
    page = selector_executor.page
    facility_selection_url = "https://r.kaipoke.biz/kaipokebiz/common/COM020101.do?fromBP=true"

    # 🆕 多种导航策略，逐步降级
    strategies = [
        {"name": "快速导航", "wait_until": "domcontentloaded", "timeout": 15000},
        {"name": "标准导航", "wait_until": "networkidle", "timeout": 20000},
        {"name": "基础导航", "wait_until": "load", "timeout": 25000},
        {"name": "最小导航", "wait_until": "commit", "timeout": 10000}
    ]

    for strategy in strategies:
        try:
            logger.info(f"🔄 尝试{strategy['name']}回据点选择页面")

            # 尝试导航
            await page.goto(
                facility_selection_url,
                wait_until=strategy['wait_until'],
                timeout=strategy['timeout']
            )

            # 短暂等待页面稳定
            await page.wait_for_timeout(2000)

            # 验证页面是否正确加载
            current_url = page.url
            if "COM020101.do" in current_url:
                logger.info(f"✅ {strategy['name']}成功，已返回据点选择页面")

                # 🆕 额外验证：检查页面关键元素
                try:
                    # 等待据点列表加载
                    await page.wait_for_selector('table', timeout=5000)
                    logger.info("✅ 据点列表已加载")
                    return  # 成功返回
                except Exception:
                    logger.warning("⚠️ 据点列表未完全加载，但URL正确")
                    return  # 仍然认为成功
            else:
                logger.warning(f"⚠️ {strategy['name']}后URL不正确: {current_url}")
                continue  # 尝试下一个策略

        except Exception as e:
            logger.warning(f"⚠️ {strategy['name']}失败: {e}")
            continue  # 尝试下一个策略

    # 🆕 所有策略都失败时的最后尝试
    try:
        logger.info("🔄 最后尝试：刷新当前页面")
        await page.reload(wait_until='domcontentloaded', timeout=10000)
        await page.wait_for_timeout(3000)

        current_url = page.url
        if "COM020101.do" in current_url:
            logger.info("✅ 页面刷新成功，已在据点选择页面")
            return
        else:
            # 强制导航到据点选择页面
            logger.info("🔄 强制导航到据点选择页面")
            await page.goto(facility_selection_url, timeout=10000)
            await page.wait_for_timeout(3000)
            logger.info("✅ 强制导航完成")
            return

    except Exception as final_error:
        logger.error(f"❌ 所有导航策略都失败: {final_error}")
        # 🆕 不抛出异常，允许工作流继续处理下一个据点
        logger.warning("⚠️ 导航失败，但允许工作流继续运行")
        return


def run(config: dict):
    """工作流入口函数"""
    asyncio.run(async_run(config))


def force_clean_data_for_sheets(data):
    """
    最终强制清洗，确保所有cell为str或""，彻底剔除NaN/None/inf/复杂对象，防止Google Sheets API 400错误。
    """
    cleaned = []
    for row in data:
        cleaned_row = []
        for cell in row:
            try:
                # 处理None/NaN/inf
                if cell is None:
                    cleaned_cell = ""
                elif isinstance(cell, float) and (math.isnan(cell) or math.isinf(cell)):
                    cleaned_cell = ""
                elif isinstance(cell, (np.integer, np.floating)):
                    if math.isnan(cell) or math.isinf(cell):
                        cleaned_cell = ""
                    else:
                        cleaned_cell = str(cell)
                elif isinstance(cell, (int, float)):
                    cleaned_cell = str(cell)
                elif isinstance(cell, str):
                    cleaned_cell = cell.strip()
                else:
                    # 复杂对象直接置空
                    cleaned_cell = ""
            except Exception:
                cleaned_cell = ""
            cleaned_row.append(cleaned_cell)
        cleaned.append(cleaned_row)
    return cleaned


async def handle_post_login_popups(page, context: str = "general"):
    """
    🆕 处理登录后弹窗（kaipoke_billing专用）
    """
    try:
        logger.debug(f"🔍 处理登录后弹窗 (上下文: {context})")

        # 使用新的模块化弹窗处理器
        try:
            from core.popup_handler.kaipoke_popup_handler import handle_kaipoke_login_popups

            handled = await handle_kaipoke_login_popups(page, f"billing_{context}")

            if handled:
                logger.info("✅ 登录后弹窗处理完成")
            else:
                logger.debug("ℹ️ 未发现需要处理的登录后弹窗")

        except ImportError as e:
            logger.debug(f"弹窗处理器导入失败，跳过弹窗处理: {e}")
        except Exception as e:
            logger.warning(f"⚠️ 登录后弹窗处理失败: {e}")

    except Exception as e:
        logger.debug(f"登录后弹窗处理异常: {e}")


def run(config: dict):
    """工作流入口函数"""
    asyncio.run(async_run(config))
