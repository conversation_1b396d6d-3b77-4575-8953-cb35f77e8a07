#!/usr/bin/env python3
"""
验证分页简化修复的脚本（不依赖外部库）
"""

import os
import re

def check_workflow_simplification():
    """检查工作流简化情况"""
    
    print("🔍 检查工作流简化情况...")
    
    workflow_file = 'workflows/kaipoke_performance_report.py'
    
    if not os.path.exists(workflow_file):
        print(f"❌ 工作流文件不存在: {workflow_file}")
        return False
    
    with open(workflow_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查关键修改
    checks = [
        # 应该移除的内容（过于严格的验证）
        ("分页前最终安全检查", False, "移除了过于严格的分页前验证"),
        ("verify_user_list_page_state", False, "移除了页面状态验证调用"),
        ("检测到月份选择元素", False, "移除了错误的月份选择检查"),
        
        # 应该保留的内容（简化的分页逻辑）
        ("navigate_to_next_page", True, "保留了分页导航函数"),
        ("smart_click", True, "使用智能选择器点击"),
        ("next_page_button", True, "使用配置的下一页按钮选择器"),
        ("简化版本：专注于分页功能", True, "简化了分页导航函数"),
    ]
    
    results = []
    
    for keyword, should_exist, description in checks:
        exists = keyword in content
        
        if should_exist:
            if exists:
                results.append(f"✅ {description}")
            else:
                results.append(f"❌ {description} - 未找到")
        else:
            if not exists:
                results.append(f"✅ {description}")
            else:
                results.append(f"⚠️ {description} - 仍然存在")
    
    for result in results:
        print(f"   {result}")
    
    success_count = len([r for r in results if r.startswith("✅")])
    total_count = len(results)
    
    print(f"\n📊 简化效果评分: {success_count}/{total_count}")
    
    return success_count >= total_count * 0.8  # 80%以上为成功

def check_selectors_config():
    """检查选择器配置"""
    
    print("\n🔍 检查选择器配置...")
    
    config_file = 'configs/selectors.yaml'
    
    if not os.path.exists(config_file):
        print(f"❌ 配置文件不存在: {config_file}")
        return False
    
    with open(config_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 查找next_page_button配置
    next_button_section = re.search(
        r'next_page_button:\s*\n((?:\s*-.*\n)*)', 
        content, 
        re.MULTILINE
    )
    
    if next_button_section:
        selectors = next_button_section.group(1)
        print("📄 下一页按钮选择器:")
        
        # 提取选择器
        selector_lines = re.findall(r'-\s*"([^"]+)"', selectors)
        if not selector_lines:
            selector_lines = re.findall(r'-\s*([^\n#]+)', selectors)
        
        improvements = []
        
        for i, selector in enumerate(selector_lines, 1):
            selector = selector.strip()
            if selector:
                print(f"   {i}. {selector}")
                
                # 分析改进
                if '.pager-btm' in selector:
                    improvements.append("限定分页区域")
                if ':not(' in selector and 'month' in selector:
                    improvements.append("排除月份链接")
                if '.next02' in selector or '.next' in selector:
                    improvements.append("包含next关键词")
        
        if improvements:
            print(f"\n🎯 选择器改进: {', '.join(set(improvements))}")
        
        return len(selector_lines) > 0
    
    else:
        print("❌ 未找到next_page_button配置")
        return False

def analyze_pagination_logic():
    """分析分页逻辑"""
    
    print("\n🔍 分析分页逻辑...")
    
    workflow_file = 'workflows/kaipoke_performance_report.py'
    
    with open(workflow_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 查找navigate_to_next_page函数
    func_match = re.search(
        r'async def navigate_to_next_page.*?(?=async def|\Z)', 
        content, 
        re.DOTALL
    )
    
    if func_match:
        func_content = func_match.group(0)
        
        print("📄 分页导航函数分析:")
        
        # 分析函数特征
        features = []
        
        if "简化版本" in func_content:
            features.append("✅ 函数已简化")
        
        if "smart_click" in func_content:
            features.append("✅ 使用智能选择器")
        
        if "#tableData" in func_content:
            features.append("✅ 检查用户详情页面")
        
        if "严格验证" not in func_content:
            features.append("✅ 移除了严格验证")
        
        if "月份选择元素" not in func_content:
            features.append("✅ 移除了月份选择检查")
        
        if len(func_content.split('\n')) < 50:  # 函数行数较少
            features.append("✅ 函数逻辑简洁")
        
        for feature in features:
            print(f"   {feature}")
        
        return len(features) >= 4  # 至少4个特征为成功
    
    else:
        print("❌ 未找到navigate_to_next_page函数")
        return False

def check_error_patterns():
    """检查可能导致错误的模式"""
    
    print("\n🔍 检查可能导致错误的模式...")
    
    workflow_file = 'workflows/kaipoke_performance_report.py'
    
    with open(workflow_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查可能导致问题的模式
    error_patterns = [
        ("serviceOfferYmSelectId.*验证失败", "月份选择元素验证错误"),
        ("分页前页面状态验证失败", "过于严格的页面验证"),
        ("检测到月份选择元素.*错误页面", "错误的月份元素检查"),
    ]
    
    issues_found = []
    
    for pattern, description in error_patterns:
        if re.search(pattern, content, re.IGNORECASE):
            issues_found.append(description)
    
    if issues_found:
        print("⚠️ 发现可能导致错误的模式:")
        for issue in issues_found:
            print(f"   - {issue}")
        return False
    else:
        print("✅ 未发现可能导致错误的模式")
        return True

def main():
    """主函数"""
    
    print("🚀 开始验证分页简化修复...")
    print("=" * 60)
    
    # 运行各项检查
    checks = [
        ("工作流简化", check_workflow_simplification),
        ("选择器配置", check_selectors_config),
        ("分页逻辑", analyze_pagination_logic),
        ("错误模式", check_error_patterns),
    ]
    
    results = {}
    
    for check_name, check_func in checks:
        try:
            result = check_func()
            results[check_name] = result
            
            if result:
                print(f"\n✅ {check_name}: 通过")
            else:
                print(f"\n❌ {check_name}: 需要改进")
                
        except Exception as e:
            print(f"\n❌ {check_name}: 检查失败 - {e}")
            results[check_name] = False
    
    # 总结
    print("\n" + "=" * 60)
    print("📋 验证总结:")
    
    passed_checks = sum(1 for result in results.values() if result)
    total_checks = len(results)
    
    for check_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {check_name}: {status}")
    
    print(f"\n🎯 总体结果: {passed_checks}/{total_checks} 检查通过")
    
    if passed_checks == total_checks:
        print("\n🎉 所有检查通过！分页功能应该能正常工作")
        print("\n📝 修复总结:")
        print("1. ✅ 移除了过于严格的分页前验证")
        print("2. ✅ 简化了分页导航函数")
        print("3. ✅ 移除了错误的月份选择元素检查")
        print("4. ✅ 保留了智能选择器点击功能")
        print("5. ✅ 保留了基本的用户详情页面检查")
        
    elif passed_checks >= total_checks * 0.75:
        print("\n✅ 大部分检查通过，修复基本成功")
        
    else:
        print("\n⚠️ 多个检查失败，可能需要进一步修复")
    
    print("\n📝 测试建议:")
    print("1. 运行修复后的kaipoke_performance_report工作流")
    print("2. 观察是否还会出现'分页前页面状态验证失败'错误")
    print("3. 确认分页功能能正常工作")
    print("4. 验证不会意外更改月份")
    
    return passed_checks >= total_checks * 0.75

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
