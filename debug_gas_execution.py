#!/usr/bin/env python3
"""
GAS执行调试脚本
用于检查GAS函数执行过程中的问题
"""

import urllib.request
import urllib.parse
import json
from datetime import datetime

def test_gas_with_debug():
    """测试GAS并添加调试信息"""
    
    # 修改GAS代码以添加更多日志
    debug_gas_code = '''
function formatData(sheetName) {
  console.log('formatData开始执行，sheetName:', sheetName);
  
  try {
    // スプレッドシートとシートの指定
    const ss = SpreadsheetApp.openById('1vRutYYUmIBNKsgYKpkvb3HSkR_46Oa2_w9Brqt7R3N8');
    console.log('スプレッドシート取得成功');
    
    const sourceSheet = ss.getSheetByName(sheetName);
    if (!sourceSheet) {
      console.error('ソースシートが見つかりません:', sheetName);
      return;
    }
    console.log('ソースシート取得成功:', sheetName);
    
    // データ範囲を取得
    const dataRange = sourceSheet.getDataRange();
    const data = dataRange.getValues();
    console.log('データ取得成功，行数:', data.length);
    
    if (data.length === 0) {
      console.log('データが空です');
      return;
    }
    
    // 最初の数行をログ出力
    console.log('最初の5行のデータ:');
    for (let i = 0; i < Math.min(5, data.length); i++) {
      console.log('Row', i + 1, ':', data[i]);
    }
    
    // 新しいシート名の生成
    const now = new Date();
    const formattedDate = Utilities.formatDate(now, Session.getScriptTimeZone(), 'yyyyMMdd-HHmmss');
    const newSheetName = `【${sheetName}】抽出結果${formattedDate}`;
    console.log('新しいシート名:', newSheetName);
    
    const targetSheet = ss.insertSheet(newSheetName);
    console.log('新しいシート作成成功');
    
    // ヘッダーの設定
    const headers = ['連番', '利用者氏名', 'サービス提供年月日', '訪問開始時間', '訪問終了時間', 'ヘルパー氏名', 'サービス内容', '施設区分', '作成日付'];
    targetSheet.appendRow(headers);
    console.log('ヘッダー設定完了');
    
    // データ処理ロジック（簡略版）
    let recordCounter = 1;
    const executionDate = Utilities.formatDate(now, Session.getScriptTimeZone(), 'yyyy/MM/dd');
    const formattedData = [];
    
    console.log('データ処理開始');
    
    // 処理完了
    console.log('formatData実行完了');
    return newSheetName;
    
  } catch (error) {
    console.error('formatData実行中にエラー:', error);
    throw error;
  }
}

function doPost(e) {
  console.log('doPost開始');
  
  try {
    let jsonData = JSON.parse(e.postData.contents);
    let password = jsonData.password;
    console.log('受信したパスワード:', password);

    switch (password) {
      case 'doFormat_Kagoshima':
        console.log('鹿児島シートの処理開始');
        const result1 = formatData('鹿児島');
        console.log('鹿児島シートの処理完了:', result1);
        break;
      case 'doFormat_Fukuoka':
        console.log('福岡シートの処理開始');
        const result2 = formatData('福岡');
        console.log('福岡シートの処理完了:', result2);
        break;
      default:
        console.log('未知のパスワード:', password);
    }
    
    console.log('doPost完了');
    
  } catch (error) {
    console.error('doPost実行中にエラー:', error);
    throw error;
  }
}
'''
    
    print("🔍 GAS调试信息")
    print("=" * 60)
    print("📋 建议的GAS调试代码已生成")
    print("📝 请将上述代码替换到您的GAS项目中")
    print("🔄 然后重新部署GAS并测试")
    print("=" * 60)
    
    # 测试当前GAS
    test_configs = [
        {
            "name": "福岡",
            "url": "https://script.google.com/macros/s/AKfycbxeGLzVLdm59NEt1kMLjvsY6nDGyvAY-mWip4ieSUpvNKO24NLZnTjJ6Ou9qwsmdFC6/exec",
            "password": "doFormat_Fukuoka"
        }
    ]
    
    for config in test_configs:
        print(f"\n📍 测试 {config['name']} 据点（调试模式）")
        
        try:
            json_data = {'password': config['password']}
            data = json.dumps(json_data).encode('utf-8')
            
            req = urllib.request.Request(
                config['url'],
                data=data,
                headers={'Content-Type': 'application/json'}
            )
            
            with urllib.request.urlopen(req, timeout=60) as response:
                response_text = response.read().decode('utf-8')
                status_code = response.getcode()
                
                print(f"📡 响应状态: {status_code}")
                print(f"📡 响应内容: {response_text}")
                
                if status_code == 200 and "スクリプトが完了しましたが、何も返されませんでした" in response_text:
                    print(f"✅ {config['name']}: GAS执行成功")
                    print("💡 建议检查:")
                    print("   1. Google Sheets中是否有数据")
                    print("   2. Sheet名称是否正确（福岡/鹿児島）")
                    print("   3. 数据格式是否符合GAS期望")
                    print("   4. 查看GAS执行日志（Google Apps Script编辑器 > 执行 > 查看日志）")
                else:
                    print(f"❌ {config['name']}: 响应异常")
                        
        except Exception as e:
            print(f"❌ {config['name']}: 错误 - {e}")

if __name__ == "__main__":
    test_gas_with_debug()
