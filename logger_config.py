import logging
import sys
import os

def setup_logger():
    """プロジェクト全体で使用するロガーを設定します。"""
    # ロガーの取得
    logger = logging.getLogger('AozoraAutomatedWorkflows')
    logger.setLevel(logging.DEBUG)

    # すでにハンドラが設定されている場合は、追加しない
    if logger.hasHandlers():
        return logger

    # フォーマッタの作成
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )

    # コンソールハンドラの設定
    stream_handler = logging.StreamHandler(sys.stdout)
    stream_handler.setFormatter(formatter)
    logger.addHandler(stream_handler)

    # ファイルハンドラの設定（logs/app.logに追記モードで出力）
    log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'logs')
    os.makedirs(log_dir, exist_ok=True)
    log_file_path = os.path.join(log_dir, 'app.log')
    file_handler = logging.FileHandler(log_file_path, mode='a', encoding='utf-8')
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)

    return logger

# グローバルロガーインスタンス
logger = setup_logger()