#!/usr/bin/env python3
"""
删除Google Drive中的指定文件
用于释放服务账号的存储空间
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from core.gsuite.drive_client import DriveClient
    from logger_config import logger
except ImportError as e:
    print(f"导入模块失败: {e}")
    print("请确保已安装所需依赖包")
    sys.exit(1)

def delete_gdrive_file(file_id):
    """删除Google Drive中的指定文件"""
    try:
        print(f"🔍 正在连接Google Drive...")
        drive_client = DriveClient()
        
        # 先获取文件信息
        try:
            file_info = drive_client.service.files().get(
                fileId=file_id,
                fields='id,name,size,mimeType,createdTime'
            ).execute()
            
            name = file_info.get('name', '未知')
            size = file_info.get('size')
            mime_type = file_info.get('mimeType', '未知')
            created_time = file_info.get('createdTime', '未知')
            
            print(f"📄 文件信息:")
            print(f"  名称: {name}")
            print(f"  类型: {mime_type}")
            print(f"  大小: {format_file_size(int(size)) if size else 'N/A'}")
            print(f"  创建时间: {created_time}")
            print(f"  ID: {file_id}")
            
        except Exception as e:
            print(f"❌ 获取文件信息失败: {e}")
            return False
        
        # 确认删除
        confirm = input(f"\n⚠️  确定要删除文件 '{name}' 吗? (y/N): ").strip().lower()
        if confirm not in ['y', 'yes']:
            print("❌ 取消删除操作")
            return False
        
        # 执行删除
        print(f"🗑️  正在删除文件...")
        drive_client.service.files().delete(fileId=file_id).execute()
        
        print(f"✅ 文件删除成功: {name}")
        return True
        
    except Exception as e:
        print(f"❌ 删除文件失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def format_file_size(size_bytes):
    """格式化文件大小"""
    if size_bytes is None:
        return "未知"
    
    for unit in ['B', 'KB', 'MB', 'GB']:
        if size_bytes < 1024.0:
            return f"{size_bytes:.1f} {unit}"
        size_bytes /= 1024.0
    return f"{size_bytes:.1f} TB"

def batch_delete_files(file_ids):
    """批量删除文件"""
    print(f"🗑️  批量删除 {len(file_ids)} 个文件...")
    
    success_count = 0
    failed_count = 0
    
    for i, file_id in enumerate(file_ids):
        print(f"\n📄 处理文件 {i+1}/{len(file_ids)}: {file_id}")
        if delete_gdrive_file(file_id):
            success_count += 1
        else:
            failed_count += 1
    
    print(f"\n📊 批量删除结果:")
    print(f"  ✅ 成功: {success_count} 个")
    print(f"  ❌ 失败: {failed_count} 个")

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("使用方法:")
        print("  删除单个文件: python3 delete_gdrive_file.py <文件ID>")
        print("  批量删除文件: python3 delete_gdrive_file.py <文件ID1> <文件ID2> ...")
        print("\n💡 提示: 先运行 python3 list_gdrive_files.py 查看文件列表")
        sys.exit(1)
    
    file_ids = sys.argv[1:]
    
    if len(file_ids) == 1:
        delete_gdrive_file(file_ids[0])
    else:
        batch_delete_files(file_ids)
