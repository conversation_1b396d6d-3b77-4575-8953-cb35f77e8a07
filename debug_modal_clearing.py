#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试模态框清除问题的脚本
检查所有可能清除#registModal的代码位置

时间戳: 2025-07-24 16:00:00 JST
"""

import asyncio
import logging
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from playwright.async_api import async_playwright

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def debug_modal_clearing():
    """调试模态框清除问题"""
    playwright = None
    browser = None
    
    try:
        logger.info("🔍 开始调试模态框清除问题...")
        
        # 1. 初始化浏览器
        playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=False)
        page = await browser.new_page()
        
        # 2. 导航到kaipoke登录页面
        logger.info("🌐 导航到kaipoke登录页面...")
        await page.goto("https://r.kaipoke.biz/kaipokebiz/common/COM020101.do?fromBP=true")
        
        # 3. 等待页面加载
        await page.wait_for_load_state('networkidle')
        await page.wait_for_timeout(3000)
        
        # 4. 创建一个模拟的数据登录窗口
        logger.info("🔧 创建模拟的数据登录窗口...")
        await page.evaluate("""
            () => {
                // 创建模拟的registModal
                const modal = document.createElement('div');
                modal.id = 'registModal';
                modal.className = 'modal fade in';
                modal.style.cssText = `
                    display: block !important;
                    position: fixed;
                    top: 50px;
                    left: 50px;
                    width: 500px;
                    height: 400px;
                    background: white;
                    border: 2px solid red;
                    z-index: 9999;
                    padding: 20px;
                `;
                modal.innerHTML = `
                    <h3>模拟数据登录窗口</h3>
                    <p>这是一个模拟的#registModal窗口</p>
                    <input id="inPopupInsuranceDivision01" type="radio" name="insurance" value="1">
                    <label>介護保险</label><br>
                    <input id="inPopupInsuranceDivision02" type="radio" name="insurance" value="2">
                    <label>医疗保险</label><br>
                    <button id="btnRegisPop">登录</button>
                `;
                document.body.appendChild(modal);
                
                console.log('✅ 模拟数据登录窗口已创建');
                return true;
            }
        """)
        
        # 5. 验证窗口是否可见
        modal_visible = await page.locator('#registModal').is_visible()
        logger.info(f"📊 模拟窗口可见: {modal_visible}")
        
        # 6. 监控窗口状态变化
        logger.info("👀 开始监控窗口状态变化...")
        
        for i in range(10):  # 监控10秒
            await page.wait_for_timeout(1000)
            
            # 检查窗口是否还存在
            modal_exists = await page.locator('#registModal').count() > 0
            modal_visible = await page.locator('#registModal').is_visible() if modal_exists else False
            modal_display = await page.evaluate("""
                () => {
                    const modal = document.querySelector('#registModal');
                    if (!modal) return 'not found';
                    return window.getComputedStyle(modal).display;
                }
            """)
            
            logger.info(f"📊 第{i+1}秒 - 存在: {modal_exists}, 可见: {modal_visible}, display: {modal_display}")
            
            if not modal_exists or not modal_visible or modal_display == 'none':
                logger.error(f"❌ 第{i+1}秒时窗口被清除了！")
                
                # 检查页面是否被刷新
                current_url = page.url
                logger.info(f"📄 当前URL: {current_url}")
                
                # 检查控制台错误
                console_logs = []
                page.on("console", lambda msg: console_logs.append(f"{msg.type}: {msg.text}"))
                
                break
        else:
            logger.info("🎉 窗口在10秒内保持稳定，未被清除！")
        
        # 7. 测试点击新规追加按钮（如果存在）
        add_button_count = await page.locator('#btn_area .cf:nth-child(1) :nth-child(1)').count()
        if add_button_count > 0:
            logger.info("🔘 发现新规追加按钮，测试点击...")
            
            # 重新创建模拟窗口
            await page.evaluate("""
                () => {
                    // 先移除旧的
                    const oldModal = document.querySelector('#registModal');
                    if (oldModal) oldModal.remove();
                    
                    // 创建新的
                    const modal = document.createElement('div');
                    modal.id = 'registModal';
                    modal.className = 'modal fade in';
                    modal.style.cssText = `
                        display: block !important;
                        position: fixed;
                        top: 50px;
                        left: 50px;
                        width: 500px;
                        height: 400px;
                        background: yellow;
                        border: 3px solid blue;
                        z-index: 9999;
                        padding: 20px;
                    `;
                    modal.innerHTML = `
                        <h3>点击后的数据登录窗口</h3>
                        <p>测试点击新规追加按钮后是否被清除</p>
                    `;
                    document.body.appendChild(modal);
                    
                    console.log('✅ 新的模拟窗口已创建');
                    return true;
                }
            """)
            
            # 点击按钮
            try:
                await page.click('#btn_area .cf:nth-child(1) :nth-child(1)', timeout=5000)
                logger.info("✅ 新规追加按钮点击成功")
                
                # 监控点击后的状态
                for i in range(5):
                    await page.wait_for_timeout(1000)
                    modal_exists = await page.locator('#registModal').count() > 0
                    modal_visible = await page.locator('#registModal').is_visible() if modal_exists else False
                    
                    logger.info(f"📊 点击后第{i+1}秒 - 存在: {modal_exists}, 可见: {modal_visible}")
                    
                    if not modal_exists or not modal_visible:
                        logger.error(f"❌ 点击后第{i+1}秒时窗口被清除了！")
                        break
                else:
                    logger.info("🎉 点击后窗口保持稳定！")
                    
            except Exception as e:
                logger.error(f"❌ 新规追加按钮点击失败: {e}")
        else:
            logger.warning("⚠️ 未找到新规追加按钮")
        
        # 8. 等待用户观察
        logger.info("⏳ 等待30秒供用户观察...")
        await page.wait_for_timeout(30000)
        
    except Exception as e:
        logger.error(f"❌ 调试过程中发生错误: {e}")
        
    finally:
        if browser:
            try:
                await browser.close()
                logger.info("🔒 浏览器已关闭")
            except:
                pass
        if playwright:
            try:
                await playwright.stop()
            except:
                pass

async def main():
    """主函数"""
    logger.info("🔧 Kaipoke Tennki 模态框清除问题调试")
    logger.info("=" * 50)
    
    await debug_modal_clearing()

if __name__ == "__main__":
    asyncio.run(main())
