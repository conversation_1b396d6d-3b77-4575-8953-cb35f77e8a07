
# core/file_handler.py

import os
import time
import shutil
from datetime import datetime, timedelta
from logger_config import logger
from core.google_drive_utils import GoogleDriveUtils

class FileHandler:
    def __init__(self, download_path: str):
        """
        ダウンロードされたファイルの処理（待機、リネーム、アップロード）を担当します。

        Args:
            download_path (str): ブラウザのダウンロードディレクトリ。
        """
        if not os.path.isdir(download_path):
            logger.info(f"ダウンロードディレクトリが存在しないため作成します: {download_path}")
            os.makedirs(download_path, exist_ok=True)
        self.download_path = download_path

    def wait_for_download_complete(self, timeout: int = 60) -> bool:
        """
        指定されたディレクトリで、現在進行中のダウンロード（.crdownloadファイル）が
        完了するのを待ちます。

        Args:
            timeout (int): タイムアウトまでの秒数。

        Returns:
            bool: タイムアウト前にダウンロードが完了した場合はTrue、そうでなければFalse。
        """
        logger.info("ダウンロード完了を待機しています...")
        start_time = time.time()
        while any(fname.endswith('.crdownload') for fname in os.listdir(self.download_path)):
            if time.time() - start_time > timeout:
                logger.error("ダウンロード待機がタイムアウトしました。")
                return False
            time.sleep(1)
        logger.info("ダウンロードが完了しました。")
        return True

    def get_latest_downloaded_file(self) -> str | None:
        """
        ダウンロードディレクトリ内で最も新しく更新されたファイルを取得します。

        Returns:
            str | None: 最新のファイルのフルパス。見つからない場合はNone。
        """
        files = [os.path.join(self.download_path, f) for f in os.listdir(self.download_path)]
        if not files:
            logger.warning("ダウンロードディレクトリにファイルが見つかりません。")
            return None
        
        latest_file = max(files, key=os.path.getmtime)
        logger.info(f"最新のダウンロードファイルが見つかりました: {latest_file}")
        return latest_file

    def rename_and_move_file(self, source_path: str, new_filename_pattern: str, month_str: str, destination_dir: str = None) -> str | None:
        """
        ファイルをリネームし、必要に応じて新しいディレクトリに移動します。

        Args:
            source_path (str): 元のファイルのパス。
            new_filename_pattern (str): 新しいファイル名のパターン（例: "{month}実績.xlsx"）。
            month_str (str): ファイル名に埋め込む月の文字列（例: "令和6年06月"）。
            destination_dir (str, optional): 移動先のディレクトリ。Noneの場合は元のディレクトリ内。

        Returns:
            str | None: 新しいファイルのパス。成功した場合はそのパス、失敗した場合はNone。
        """
        if not os.path.exists(source_path):
            logger.error(f"リネーム対象のファイルが見つかりません: {source_path}")
            return None

        new_filename = new_filename_pattern.format(month=month_str)
        destination_path = os.path.join(destination_dir or self.download_path, new_filename)

        try:
            shutil.move(source_path, destination_path)
            logger.info(f"ファイルが正常にリネーム/移動されました: {destination_path}")
            return destination_path
        except Exception as e:
            logger.error(f"ファイルのリネーム/移動中にエラーが発生しました: {e}")
            return None

    def upload_to_gdrive(self, file_path: str, folder_id: str):
        """
        指定されたファイルをGoogle Driveの特定のフォルダにアップロードします。
        （注：この機能は google_drive_utils が実装されていることを前提としています）
        """
        logger.info(f"ファイル「{os.path.basename(file_path)}」をGoogle DriveフォルダID「{folder_id}」にアップロードしています...")
        try:
            gdrive_utils = GoogleDriveUtils()
            gdrive_utils.upload_file(file_path, folder_id)
            logger.info("Google Driveへのアップロードが成功しました。")
        except Exception as e:
            logger.error(f"Google Driveへのアップロード中にエラーが発生しました: {e}")
            raise

    def cleanup_download_path(self):
        """
        ダウンロードディレクトリ内のすべてのファイルとサブディレクトリを削除します。
        """
        logger.info(f"ダウンロードディレクトリをクリーンアップしています: {self.download_path}")
        for filename in os.listdir(self.download_path):
            file_path = os.path.join(self.download_path, filename)
            try:
                if os.path.isfile(file_path) or os.path.islink(file_path):
                    os.unlink(file_path)
                elif os.path.isdir(file_path):
                    shutil.rmtree(file_path)
            except Exception as e:
                logger.error(f"クリーンアップ中にファイルの削除に失敗しました {file_path}: {e}")
