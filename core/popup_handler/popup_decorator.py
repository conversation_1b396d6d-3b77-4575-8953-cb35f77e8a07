#!/usr/bin/env python3
"""
弹窗处理装饰器
提供简单易用的装饰器接口，自动处理页面弹窗
"""

import asyncio
import functools
from typing import Callable, Optional, List, Dict, Any
from logger_config import logger
from .popup_engine import popup_engine, PopupRule, PopupType, PopupAction


def handle_popups(
    context: str = "auto",
    timeout: int = 10000,
    retry_count: int = 3,
    custom_rules: Optional[List[Dict]] = None,
    auto_detect: bool = True,
    fallback_enabled: bool = True
):
    """
    弹窗处理装饰器
    
    Args:
        context: 上下文标识，用于日志记录
        timeout: 超时时间（毫秒）
        retry_count: 重试次数
        custom_rules: 自定义规则列表
        auto_detect: 是否启用智能检测
        fallback_enabled: 是否启用备用处理
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            # 获取page对象
            page = None
            
            # 尝试从参数中找到page对象
            for arg in args:
                if hasattr(arg, 'page'):
                    page = arg.page
                    break
                elif hasattr(arg, 'click'):  # 可能是page对象本身
                    page = arg
                    break
            
            # 从kwargs中查找
            if not page:
                page = kwargs.get('page')
            
            if not page:
                logger.warning("⚠️ 未找到page对象，跳过弹窗处理")
                return await func(*args, **kwargs)
            
            # 确保弹窗引擎已初始化
            if not popup_engine.rules:
                await popup_engine.initialize()
            
            # 添加自定义规则
            if custom_rules:
                for rule_data in custom_rules:
                    rule = PopupRule(
                        name=rule_data.get('name', 'custom_rule'),
                        popup_type=PopupType(rule_data.get('type', 'custom')),
                        selectors=rule_data.get('selectors', []),
                        action=PopupAction(rule_data.get('action', 'close')),
                        priority=rule_data.get('priority', 100)
                    )
                    await popup_engine.add_dynamic_rule(rule)
            
            # 执行前处理弹窗
            try:
                await popup_engine.handle_popups(page, f"{context}_before")
            except Exception as e:
                logger.debug(f"执行前弹窗处理失败: {e}")
            
            # 执行原函数
            result = None
            exception = None
            
            for attempt in range(retry_count + 1):
                try:
                    result = await func(*args, **kwargs)
                    break
                except Exception as e:
                    exception = e
                    logger.debug(f"函数执行失败 (尝试 {attempt + 1}/{retry_count + 1}): {e}")
                    
                    if attempt < retry_count:
                        # 处理可能出现的新弹窗
                        await popup_engine.handle_popups(page, f"{context}_retry_{attempt}")
                        await asyncio.sleep(1)
                    else:
                        # 最后一次尝试失败，抛出异常
                        raise exception
            
            # 执行后处理弹窗
            try:
                await popup_engine.handle_popups(page, f"{context}_after")
            except Exception as e:
                logger.debug(f"执行后弹窗处理失败: {e}")
            
            return result
        
        return wrapper
    return decorator


def popup_safe(
    selectors: List[str],
    action: str = "close",
    priority: int = 100,
    context: str = "safe_operation"
):
    """
    安全操作装饰器 - 针对特定弹窗的处理
    
    Args:
        selectors: 目标弹窗选择器列表
        action: 处理动作
        priority: 优先级
        context: 上下文
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            # 创建临时规则
            temp_rule = PopupRule(
                name=f"temp_{func.__name__}",
                popup_type=PopupType.CUSTOM,
                selectors=selectors,
                action=PopupAction(action),
                priority=priority
            )
            
            # 使用handle_popups装饰器
            decorated_func = handle_popups(
                context=context,
                custom_rules=[{
                    'name': temp_rule.name,
                    'type': temp_rule.popup_type.value,
                    'selectors': temp_rule.selectors,
                    'action': temp_rule.action.value,
                    'priority': temp_rule.priority
                }]
            )(func)
            
            return await decorated_func(*args, **kwargs)
        
        return wrapper
    return decorator


def monitor_popups(
    interval: int = 5000,
    max_duration: int = 60000,
    context: str = "monitor"
):
    """
    弹窗监控装饰器 - 在函数执行期间持续监控弹窗
    
    Args:
        interval: 检查间隔（毫秒）
        max_duration: 最大监控时长（毫秒）
        context: 上下文
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            # 获取page对象
            page = None
            for arg in args:
                if hasattr(arg, 'page'):
                    page = arg.page
                    break
                elif hasattr(arg, 'click'):
                    page = arg
                    break
            
            if not page:
                page = kwargs.get('page')
            
            if not page:
                return await func(*args, **kwargs)
            
            # 启动监控任务
            monitoring = True
            
            async def monitor_task():
                start_time = asyncio.get_event_loop().time()
                while monitoring:
                    try:
                        current_time = asyncio.get_event_loop().time()
                        if (current_time - start_time) * 1000 > max_duration:
                            break
                        
                        await popup_engine.handle_popups(page, f"{context}_monitor")
                        await asyncio.sleep(interval / 1000)
                        
                    except Exception as e:
                        logger.debug(f"监控过程中出错: {e}")
                        break
            
            # 启动监控和主函数
            monitor_coro = monitor_task()
            main_coro = func(*args, **kwargs)
            
            try:
                # 并发执行
                done, pending = await asyncio.wait(
                    [monitor_coro, main_coro],
                    return_when=asyncio.FIRST_COMPLETED
                )
                
                # 停止监控
                monitoring = False
                
                # 取消未完成的任务
                for task in pending:
                    task.cancel()
                
                # 获取主函数结果
                for task in done:
                    if task._coro == main_coro:
                        return await task
                
            except Exception as e:
                monitoring = False
                raise e
        
        return wrapper
    return decorator


class PopupContext:
    """弹窗处理上下文管理器"""
    
    def __init__(
        self,
        page,
        context: str = "context",
        auto_handle: bool = True,
        custom_rules: Optional[List[Dict]] = None
    ):
        self.page = page
        self.context = context
        self.auto_handle = auto_handle
        self.custom_rules = custom_rules or []
        self.original_rules = []
    
    async def __aenter__(self):
        """进入上下文"""
        if not popup_engine.rules:
            await popup_engine.initialize()
        
        # 添加自定义规则
        for rule_data in self.custom_rules:
            rule = PopupRule(
                name=rule_data.get('name', 'context_rule'),
                popup_type=PopupType(rule_data.get('type', 'custom')),
                selectors=rule_data.get('selectors', []),
                action=PopupAction(rule_data.get('action', 'close')),
                priority=rule_data.get('priority', 100)
            )
            await popup_engine.add_dynamic_rule(rule)
            self.original_rules.append(rule)
        
        # 初始弹窗处理
        if self.auto_handle:
            await popup_engine.handle_popups(self.page, f"{self.context}_enter")
        
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """退出上下文"""
        # 最终弹窗处理
        if self.auto_handle:
            await popup_engine.handle_popups(self.page, f"{self.context}_exit")
        
        # 移除临时规则
        for rule in self.original_rules:
            if rule in popup_engine.rules:
                popup_engine.rules.remove(rule)
    
    async def handle_now(self):
        """立即处理弹窗"""
        return await popup_engine.handle_popups(self.page, f"{self.context}_manual")


# 便捷函数
async def quick_popup_handle(page, context: str = "quick"):
    """快速弹窗处理"""
    if not popup_engine.rules:
        await popup_engine.initialize()
    return await popup_engine.handle_popups(page, context)


# 使用示例装饰器
def kaipoke_safe(func: Callable) -> Callable:
    """Kaipoke平台安全操作装饰器"""
    return handle_popups(
        context="kaipoke",
        custom_rules=[
            {
                'name': 'kaipoke_karte',
                'type': 'widget',
                'selectors': ['#karte-c', '.karte-widget__container'],
                'action': 'hide',
                'priority': 95
            }
        ]
    )(func)
