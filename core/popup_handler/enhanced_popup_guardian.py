"""
🆕 增强弹窗守护 - 智能弹窗识别与处理
修复问题：避免第三个空闲浏览器误操作弹窗，导致工作流失败

修复日期：2025-08-01
修复内容：
- 智能弹窗类型识别
- 数据表单保护机制
- 空闲浏览器状态管理
- 误操作防护策略
"""

import asyncio
import time
from typing import Dict, List, Optional, Set
from playwright.async_api import Page
from logger_config import logger


class EnhancedPopupGuardian:
    """🆕 增强弹窗守护 - 智能识别与保护"""

    def __init__(self, page: Page, page_id: str, protection_mode: str = "enhanced"):
        self.page = page
        self.page_id = page_id
        self.protection_mode = protection_mode
        self.is_active = False
        self.guardian_task = None
        
        # 🆕 弹窗类型分类
        self.popup_types = {
            'data_form': {
                'selectors': ['#registModal', '.modal-dialog', '[role="dialog"]'],
                'protection_level': 'high',
                'action': 'protect'
            },
            'confirmation': {
                'selectors': ['.confirm-dialog', '.alert-dialog', '.swal2-popup'],
                'protection_level': 'medium', 
                'action': 'handle'
            },
            'notification': {
                'selectors': ['.notification', '.toast', '.alert-info'],
                'protection_level': 'low',
                'action': 'dismiss'
            },
            'error': {
                'selectors': ['.error-dialog', '.alert-danger', '.error-popup'],
                'protection_level': 'high',
                'action': 'handle'
            }
        }
        
        # 🆕 浏览器状态管理
        self.browser_state = {
            'is_idle': False,
            'has_active_form': False,
            'last_activity_time': time.time(),
            'processing_data': False
        }
        
        # 🆕 保护策略配置
        self.protection_config = {
            'idle_timeout': 30,  # 30秒无活动视为空闲
            'form_protection_timeout': 300,  # 表单保护超时时间
            'max_popup_handle_attempts': 3,  # 最大弹窗处理尝试次数
            'popup_check_interval': 2  # 弹窗检查间隔（秒）
        }

    async def start_enhanced_guardian(self) -> bool:
        """🆕 启动增强弹窗守护"""
        try:
            if self.is_active:
                logger.warning(f"⚠️ 弹窗守护 {self.page_id} 已在运行")
                return True

            logger.info(f"🛡️ 启动增强弹窗守护: {self.page_id} (模式: {self.protection_mode})")
            
            # 初始化页面保护机制
            await self._initialize_page_protection()
            
            # 启动守护任务
            self.is_active = True
            self.guardian_task = asyncio.create_task(self._enhanced_guardian_loop())
            
            logger.info(f"✅ 增强弹窗守护 {self.page_id} 启动成功")
            return True
            
        except Exception as e:
            logger.error(f"❌ 启动增强弹窗守护失败 {self.page_id}: {e}")
            return False

    async def _initialize_page_protection(self):
        """🆕 初始化页面保护机制"""
        try:
            # 注入增强保护脚本
            await self.page.evaluate("""
                () => {
                    // 🆕 全局保护状态
                    window.ENHANCED_POPUP_PROTECTION = {
                        enabled: true,
                        pageId: arguments[0],
                        protectionMode: arguments[1],
                        activeForm: null,
                        protectedElements: new Set(),
                        lastActivity: Date.now(),
                        isIdle: false
                    };

                    // 🆕 智能弹窗分类函数
                    window.classifyPopup = function(element) {
                        if (!element) return 'unknown';
                        
                        const classList = element.className.toLowerCase();
                        const id = element.id.toLowerCase();
                        
                        // 数据表单弹窗
                        if (id.includes('regist') || classList.includes('modal') || 
                            element.querySelector('input, select, textarea')) {
                            return 'data_form';
                        }
                        
                        // 确认对话框
                        if (classList.includes('confirm') || classList.includes('alert') ||
                            element.textContent.includes('確認') || element.textContent.includes('削除')) {
                            return 'confirmation';
                        }
                        
                        // 错误弹窗
                        if (classList.includes('error') || classList.includes('danger') ||
                            element.textContent.includes('エラー') || element.textContent.includes('失敗')) {
                            return 'error';
                        }
                        
                        // 通知弹窗
                        if (classList.includes('notification') || classList.includes('toast') ||
                            classList.includes('info')) {
                            return 'notification';
                        }
                        
                        return 'unknown';
                    };

                    // 🆕 活动监控
                    const activityEvents = ['click', 'input', 'change', 'keydown', 'focus'];
                    activityEvents.forEach(eventType => {
                        document.addEventListener(eventType, () => {
                            window.ENHANCED_POPUP_PROTECTION.lastActivity = Date.now();
                            window.ENHANCED_POPUP_PROTECTION.isIdle = false;
                        }, true);
                    });

                    // 🆕 表单状态监控
                    const monitorFormState = () => {
                        const registModal = document.querySelector('#registModal');
                        if (registModal) {
                            const isVisible = registModal.style.display !== 'none' && 
                                            registModal.offsetParent !== null;
                            
                            if (isVisible !== (window.ENHANCED_POPUP_PROTECTION.activeForm !== null)) {
                                window.ENHANCED_POPUP_PROTECTION.activeForm = isVisible ? registModal : null;
                                console.log('🛡️ 表单状态变化:', isVisible ? '激活' : '关闭');
                            }
                        }
                    };

                    // 定期检查表单状态
                    setInterval(monitorFormState, 1000);

                    // 🆕 增强点击拦截 - 保护数据表单
                    const originalAddEventListener = Element.prototype.addEventListener;
                    Element.prototype.addEventListener = function(type, listener, options) {
                        if (type === 'click' && window.ENHANCED_POPUP_PROTECTION.enabled) {
                            const enhancedListener = function(event) {
                                const target = event.target;
                                const activeForm = window.ENHANCED_POPUP_PROTECTION.activeForm;
                                
                                // 如果有活跃的数据表单，检查点击是否会关闭它
                                if (activeForm && !activeForm.contains(target)) {
                                    const popupType = window.classifyPopup(target.closest('.modal, .dialog, .popup'));
                                    
                                    if (popupType === 'data_form' || 
                                        target.classList.contains('close') ||
                                        target.classList.contains('cancel')) {
                                        
                                        console.warn('🛡️ 拦截可能关闭数据表单的点击:', target);
                                        event.preventDefault();
                                        event.stopPropagation();
                                        return false;
                                    }
                                }
                                
                                return listener.call(this, event);
                            };
                            
                            return originalAddEventListener.call(this, type, enhancedListener, options);
                        }
                        
                        return originalAddEventListener.call(this, type, listener, options);
                    };

                    console.log('🛡️ 增强页面保护机制已初始化:', arguments[0]);
                }
            """, self.page_id, self.protection_mode)
            
        except Exception as e:
            logger.warning(f"⚠️ 初始化页面保护失败: {e}")

    async def _enhanced_guardian_loop(self):
        """🆕 增强守护循环"""
        logger.debug(f"🔄 增强弹窗守护循环开始: {self.page_id}")
        
        consecutive_errors = 0
        max_consecutive_errors = 5
        
        while self.is_active:
            try:
                # 1. 更新浏览器状态
                await self._update_browser_state()
                
                # 2. 检查并处理弹窗
                await self._check_and_handle_popups()
                
                # 3. 🆕 空闲浏览器特殊处理
                if self.browser_state['is_idle']:
                    await self._handle_idle_browser()
                
                # 4. 🆕 数据表单保护检查
                if self.browser_state['has_active_form']:
                    await self._protect_active_form()
                
                consecutive_errors = 0
                await asyncio.sleep(self.protection_config['popup_check_interval'])
                
            except Exception as e:
                consecutive_errors += 1
                logger.warning(f"⚠️ 弹窗守护循环异常 {self.page_id}: {e}")
                
                if consecutive_errors >= max_consecutive_errors:
                    logger.error(f"❌ 弹窗守护 {self.page_id} 连续错误过多，停止守护")
                    break
                
                await asyncio.sleep(5)  # 错误后等待更长时间
        
        logger.info(f"🔒 增强弹窗守护循环结束: {self.page_id}")

    async def _update_browser_state(self):
        """🆕 更新浏览器状态"""
        try:
            # 获取页面状态信息
            state_info = await self.page.evaluate("""
                () => {
                    const protection = window.ENHANCED_POPUP_PROTECTION;
                    if (!protection) return null;
                    
                    const now = Date.now();
                    const idleTime = now - protection.lastActivity;
                    const isIdle = idleTime > 30000; // 30秒无活动
                    
                    return {
                        isIdle: isIdle,
                        hasActiveForm: protection.activeForm !== null,
                        lastActivity: protection.lastActivity,
                        idleTime: idleTime,
                        processingData: document.querySelector('#registModal input[value!=""]') !== null
                    };
                }
            """)
            
            if state_info:
                self.browser_state.update(state_info)
                
                # 记录状态变化
                if state_info['isIdle'] != self.browser_state.get('was_idle', False):
                    status = "空闲" if state_info['isIdle'] else "活跃"
                    logger.debug(f"🔄 浏览器 {self.page_id} 状态变化: {status}")
                    self.browser_state['was_idle'] = state_info['isIdle']
                    
        except Exception as e:
            logger.debug(f"⚠️ 更新浏览器状态失败 {self.page_id}: {e}")

    async def _check_and_handle_popups(self):
        """🆕 检查并智能处理弹窗"""
        try:
            # 查找所有可能的弹窗元素
            popup_elements = await self.page.query_selector_all(
                '.modal, .dialog, .popup, .alert, .swal2-popup, [role="dialog"]'
            )
            
            for element in popup_elements:
                try:
                    # 检查元素是否可见
                    is_visible = await element.is_visible()
                    if not is_visible:
                        continue
                    
                    # 🆕 智能分类弹窗
                    popup_type = await self._classify_popup_element(element)
                    
                    # 🆕 根据类型和浏览器状态决定处理策略
                    await self._handle_popup_by_type(element, popup_type)
                    
                except Exception as e:
                    logger.debug(f"⚠️ 处理弹窗元素失败: {e}")
                    
        except Exception as e:
            logger.debug(f"⚠️ 检查弹窗失败 {self.page_id}: {e}")

    async def _classify_popup_element(self, element) -> str:
        """🆕 智能分类弹窗元素"""
        try:
            popup_type = await element.evaluate("""
                (element) => {
                    return window.classifyPopup ? window.classifyPopup(element) : 'unknown';
                }
            """)
            return popup_type
        except:
            return 'unknown'

    async def _handle_popup_by_type(self, element, popup_type: str):
        """🆕 根据类型处理弹窗"""
        try:
            logger.debug(f"🔍 处理弹窗类型: {popup_type} (浏览器: {self.page_id})")
            
            if popup_type == 'data_form':
                # 🆕 数据表单：高优先级保护
                if self.browser_state['is_idle']:
                    logger.warning(f"⚠️ 空闲浏览器 {self.page_id} 检测到数据表单，避免误操作")
                    return  # 空闲浏览器不处理数据表单
                else:
                    logger.debug(f"🛡️ 保护数据表单 (浏览器: {self.page_id})")
                    
            elif popup_type == 'confirmation':
                # 🆕 确认对话框：谨慎处理
                if self.browser_state['is_idle']:
                    logger.warning(f"⚠️ 空闲浏览器 {self.page_id} 跳过确认对话框")
                    return
                else:
                    # 活跃浏览器可以处理确认对话框
                    await self._handle_confirmation_dialog(element)
                    
            elif popup_type == 'notification':
                # 🆕 通知弹窗：可以安全关闭
                await self._dismiss_notification(element)
                
            elif popup_type == 'error':
                # 🆕 错误弹窗：记录并处理
                await self._handle_error_popup(element)
                
            else:
                # 未知类型：保守处理
                if not self.browser_state['is_idle']:
                    logger.debug(f"🤔 未知弹窗类型，保守处理 (浏览器: {self.page_id})")
                    
        except Exception as e:
            logger.warning(f"⚠️ 处理弹窗失败 {popup_type}: {e}")

    async def _handle_idle_browser(self):
        """🆕 处理空闲浏览器"""
        try:
            # 空闲浏览器的特殊保护措施
            idle_time = time.time() - self.browser_state['last_activity_time']
            
            if idle_time > self.protection_config['idle_timeout']:
                # 🆕 禁用空闲浏览器的自动操作
                await self.page.evaluate("""
                    () => {
                        // 临时禁用所有自动点击
                        const originalClick = HTMLElement.prototype.click;
                        HTMLElement.prototype.click = function() {
                            console.warn('🛡️ 空闲浏览器自动点击被拦截');
                            return false;
                        };
                        
                        // 5分钟后恢复
                        setTimeout(() => {
                            HTMLElement.prototype.click = originalClick;
                            console.log('🔄 空闲浏览器点击功能已恢复');
                        }, 300000);
                    }
                """)
                
                logger.info(f"🛡️ 空闲浏览器 {self.page_id} 自动操作已暂时禁用")
                
        except Exception as e:
            logger.debug(f"⚠️ 处理空闲浏览器失败: {e}")

    async def _protect_active_form(self):
        """🆕 保护活跃表单"""
        try:
            # 检查表单数据完整性
            form_data = await self.page.evaluate("""
                () => {
                    const modal = document.querySelector('#registModal');
                    if (!modal || modal.style.display === 'none') return null;
                    
                    const inputs = modal.querySelectorAll('input, select, textarea');
                    const filledFields = Array.from(inputs).filter(input => 
                        input.value && input.value.trim() !== ''
                    ).length;
                    
                    return {
                        totalFields: inputs.length,
                        filledFields: filledFields,
                        hasData: filledFields > 0
                    };
                }
            """)
            
            if form_data and form_data['hasData']:
                logger.debug(f"🛡️ 保护活跃表单数据 (浏览器: {self.page_id}, 已填充: {form_data['filledFields']}/{form_data['totalFields']})")
                
                # 🆕 增强表单保护
                await self.page.evaluate("""
                    () => {
                        const modal = document.querySelector('#registModal');
                        if (modal) {
                            // 防止意外关闭
                            modal.style.pointerEvents = 'auto';
                            
                            // 标记为受保护
                            modal.setAttribute('data-protected', 'true');
                            
                            // 监控关闭按钮
                            const closeButtons = modal.querySelectorAll('.close, .cancel, [data-dismiss="modal"]');
                            closeButtons.forEach(btn => {
                                btn.addEventListener('click', (e) => {
                                    if (!confirm('确定要关闭表单吗？未保存的数据将丢失。')) {
                                        e.preventDefault();
                                        e.stopPropagation();
                                        return false;
                                    }
                                });
                            });
                        }
                    }
                """)
                
        except Exception as e:
            logger.debug(f"⚠️ 保护活跃表单失败: {e}")

    async def _handle_confirmation_dialog(self, element):
        """处理确认对话框"""
        try:
            # 获取对话框文本内容
            text_content = await element.text_content()
            logger.debug(f"🤔 确认对话框: {text_content[:50]}...")
            
            # 🆕 智能决策：基于内容决定是否确认
            if any(keyword in text_content for keyword in ['削除', '删除', 'delete', '取消']):
                # 危险操作，拒绝
                cancel_btn = await element.query_selector('button:has-text("キャンセル"), button:has-text("いいえ"), .btn-cancel')
                if cancel_btn:
                    await cancel_btn.click()
                    logger.info(f"🛡️ 拒绝危险操作确认 (浏览器: {self.page_id})")
            else:
                # 一般确认，接受
                confirm_btn = await element.query_selector('button:has-text("OK"), button:has-text("はい"), .btn-confirm')
                if confirm_btn:
                    await confirm_btn.click()
                    logger.debug(f"✅ 确认对话框已处理 (浏览器: {self.page_id})")
                    
        except Exception as e:
            logger.debug(f"⚠️ 处理确认对话框失败: {e}")

    async def _dismiss_notification(self, element):
        """关闭通知弹窗"""
        try:
            # 查找关闭按钮
            close_btn = await element.query_selector('.close, .dismiss, [aria-label="close"]')
            if close_btn:
                await close_btn.click()
                logger.debug(f"📢 通知弹窗已关闭 (浏览器: {self.page_id})")
            else:
                # 如果没有关闭按钮，点击弹窗外部区域
                await element.click(position={'x': -10, 'y': -10})
                
        except Exception as e:
            logger.debug(f"⚠️ 关闭通知弹窗失败: {e}")

    async def _handle_error_popup(self, element):
        """处理错误弹窗"""
        try:
            # 获取错误信息
            error_text = await element.text_content()
            logger.warning(f"❌ 检测到错误弹窗 (浏览器: {self.page_id}): {error_text[:100]}...")
            
            # 查找确认按钮关闭错误弹窗
            ok_btn = await element.query_selector('button:has-text("OK"), button:has-text("確認"), .btn-ok')
            if ok_btn:
                await ok_btn.click()
                logger.info(f"✅ 错误弹窗已关闭 (浏览器: {self.page_id})")
                
        except Exception as e:
            logger.debug(f"⚠️ 处理错误弹窗失败: {e}")

    async def stop_guardian(self):
        """停止弹窗守护"""
        try:
            self.is_active = False
            
            if self.guardian_task and not self.guardian_task.done():
                self.guardian_task.cancel()
                try:
                    await self.guardian_task
                except asyncio.CancelledError:
                    pass
            
            logger.info(f"🔒 增强弹窗守护 {self.page_id} 已停止")
            
        except Exception as e:
            logger.warning(f"⚠️ 停止弹窗守护失败 {self.page_id}: {e}")


# 🆕 全局守护管理器
class EnhancedPopupGuardianManager:
    """🆕 增强弹窗守护管理器"""
    
    def __init__(self):
        self.guardians: Dict[str, EnhancedPopupGuardian] = {}
    
    async def start_guardian(self, page: Page, page_id: str, protection_mode: str = "enhanced") -> bool:
        """启动守护"""
        try:
            if page_id in self.guardians:
                logger.warning(f"⚠️ 守护 {page_id} 已存在")
                return True
            
            guardian = EnhancedPopupGuardian(page, page_id, protection_mode)
            success = await guardian.start_enhanced_guardian()
            
            if success:
                self.guardians[page_id] = guardian
                logger.info(f"✅ 守护 {page_id} 启动成功")
            
            return success
            
        except Exception as e:
            logger.error(f"❌ 启动守护失败 {page_id}: {e}")
            return False
    
    async def stop_guardian(self, page_id: str):
        """停止指定守护"""
        if page_id in self.guardians:
            await self.guardians[page_id].stop_guardian()
            del self.guardians[page_id]
    
    async def stop_all_guardians(self):
        """停止所有守护"""
        for page_id in list(self.guardians.keys()):
            await self.stop_guardian(page_id)
        logger.info("🔒 所有增强弹窗守护已停止")


# 🆕 全局管理器实例
_enhanced_guardian_manager = EnhancedPopupGuardianManager()

# 🆕 便捷函数
async def start_enhanced_popup_guardian(page: Page, page_id: str, protection_mode: str = "enhanced") -> bool:
    """启动增强弹窗守护"""
    return await _enhanced_guardian_manager.start_guardian(page, page_id, protection_mode)

async def stop_enhanced_popup_guardian(page_id: str):
    """停止增强弹窗守护"""
    await _enhanced_guardian_manager.stop_guardian(page_id)

async def stop_all_enhanced_popup_guardians():
    """停止所有增强弹窗守护"""
    await _enhanced_guardian_manager.stop_all_guardians()