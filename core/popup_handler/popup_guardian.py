"""
Kaipoke弹窗守护模块
实现后台异步弹窗监控和处理机制，与主业务流程并行运行
"""

import asyncio
from typing import Dict, Optional, Set
from logger_config import logger
from .kaipoke_popup_handler import KaipokePopupHandler


class PopupGuardian:
    """
    弹窗守护器 - 后台异步监控和处理弹窗
    
    功能：
    1. 后台持续监控页面弹窗
    2. 自动调用KaipokePopupHandler处理弹窗
    3. 支持多浏览器实例管理
    4. 完整的生命周期管理
    """
    
    def __init__(self, check_interval: float = 2.0):
        """
        初始化弹窗守护器
        
        Args:
            check_interval: 弹窗检测间隔（秒）
        """
        self.check_interval = check_interval
        self.popup_handler = KaipokePopupHandler()
        self.guardian_tasks: Dict[str, asyncio.Task] = {}  # page_id -> task
        self.active_pages: Dict[str, any] = {}  # page_id -> page
        self.is_running = False
        
    async def start_guardian_for_page(self, page, page_id: str, context: str = "general") -> bool:
        """
        为指定页面启动弹窗守护任务
        
        Args:
            page: Playwright页面对象
            page_id: 页面唯一标识符
            context: 上下文信息（用于保护机制）
            
        Returns:
            bool: 是否成功启动
        """
        try:
            if page_id in self.guardian_tasks:
                logger.debug(f"🛡️ 页面 {page_id} 的弹窗守护已存在，跳过启动")
                return True
            
            # 存储页面引用
            self.active_pages[page_id] = page
            
            # 创建守护任务
            guardian_task = asyncio.create_task(
                self._guardian_loop(page, page_id, context)
            )
            self.guardian_tasks[page_id] = guardian_task
            
            logger.info(f"🚀 页面 {page_id} 弹窗守护启动成功 (上下文: {context})")
            return True
            
        except Exception as e:
            logger.error(f"❌ 页面 {page_id} 弹窗守护启动失败: {e}")
            return False
    
    async def stop_guardian_for_page(self, page_id: str) -> bool:
        """
        停止指定页面的弹窗守护任务
        
        Args:
            page_id: 页面唯一标识符
            
        Returns:
            bool: 是否成功停止
        """
        try:
            if page_id not in self.guardian_tasks:
                logger.debug(f"🛡️ 页面 {page_id} 的弹窗守护不存在，无需停止")
                return True
            
            # 取消守护任务
            task = self.guardian_tasks[page_id]
            if not task.done():
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass
            
            # 清理资源
            del self.guardian_tasks[page_id]
            if page_id in self.active_pages:
                del self.active_pages[page_id]
            
            logger.info(f"🛑 页面 {page_id} 弹窗守护停止成功")
            return True
            
        except Exception as e:
            logger.error(f"❌ 页面 {page_id} 弹窗守护停止失败: {e}")
            return False
    
    async def stop_all_guardians(self) -> bool:
        """
        停止所有弹窗守护任务
        
        Returns:
            bool: 是否成功停止所有任务
        """
        try:
            logger.info(f"🛑 开始停止所有弹窗守护任务 ({len(self.guardian_tasks)} 个)")
            
            # 获取所有页面ID的副本，避免在迭代时修改字典
            page_ids = list(self.guardian_tasks.keys())
            
            # 并行停止所有守护任务
            stop_tasks = [self.stop_guardian_for_page(page_id) for page_id in page_ids]
            results = await asyncio.gather(*stop_tasks, return_exceptions=True)
            
            # 统计结果
            success_count = sum(1 for r in results if r is True)
            total_count = len(results)
            
            logger.info(f"✅ 弹窗守护停止完成: {success_count}/{total_count} 成功")
            return success_count == total_count
            
        except Exception as e:
            logger.error(f"❌ 停止所有弹窗守护失败: {e}")
            return False
    
    async def _guardian_loop(self, page, page_id: str, context: str):
        """
        弹窗守护主循环
        
        Args:
            page: Playwright页面对象
            page_id: 页面唯一标识符
            context: 上下文信息
        """
        logger.debug(f"🔄 页面 {page_id} 弹窗守护循环开始")
        
        try:
            while True:
                try:
                    # 检查页面是否仍然有效
                    if page.is_closed():
                        logger.debug(f"📄 页面 {page_id} 已关闭，停止弹窗守护")
                        break
                    
                    # 执行弹窗检测和处理
                    handled = await self.popup_handler.handle_login_popups(page, context)
                    
                    if handled:
                        logger.debug(f"🎯 页面 {page_id} 守护检测到并处理了弹窗")
                    
                except Exception as e:
                    logger.debug(f"⚠️ 页面 {page_id} 弹窗检测异常: {e}")
                
                # 等待下次检测
                await asyncio.sleep(self.check_interval)
                
        except asyncio.CancelledError:
            logger.debug(f"🛑 页面 {page_id} 弹窗守护被取消")
            raise
        except Exception as e:
            logger.error(f"❌ 页面 {page_id} 弹窗守护循环异常: {e}")
        finally:
            logger.debug(f"🔚 页面 {page_id} 弹窗守护循环结束")
    
    def get_guardian_status(self) -> Dict:
        """
        获取守护任务状态信息
        
        Returns:
            Dict: 状态信息
        """
        active_count = len([task for task in self.guardian_tasks.values() if not task.done()])
        total_count = len(self.guardian_tasks)
        
        return {
            'total_guardians': total_count,
            'active_guardians': active_count,
            'check_interval': self.check_interval,
            'page_ids': list(self.guardian_tasks.keys())
        }


# 全局实例
popup_guardian = PopupGuardian()


# 便捷函数
async def start_popup_guardian(page, page_id: str, context: str = "general") -> bool:
    """
    便捷函数：启动弹窗守护
    
    Args:
        page: Playwright页面对象
        page_id: 页面唯一标识符
        context: 上下文信息
        
    Returns:
        bool: 是否成功启动
    """
    return await popup_guardian.start_guardian_for_page(page, page_id, context)


async def stop_popup_guardian(page_id: str) -> bool:
    """
    便捷函数：停止弹窗守护
    
    Args:
        page_id: 页面唯一标识符
        
    Returns:
        bool: 是否成功停止
    """
    return await popup_guardian.stop_guardian_for_page(page_id)


async def stop_all_popup_guardians() -> bool:
    """
    便捷函数：停止所有弹窗守护
    
    Returns:
        bool: 是否成功停止所有
    """
    return await popup_guardian.stop_all_guardians()


def get_popup_guardian_status() -> Dict:
    """
    便捷函数：获取守护状态
    
    Returns:
        Dict: 状态信息
    """
    return popup_guardian.get_guardian_status()
