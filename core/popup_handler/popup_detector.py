#!/usr/bin/env python3
"""
智能弹窗检测器
使用机器学习和启发式算法自动识别新的弹窗模式
"""

import asyncio
import json
from typing import Dict, List, Set, Optional, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime
from logger_config import logger


@dataclass
class PopupPattern:
    """弹窗模式"""
    selectors: List[str]
    characteristics: Dict[str, any]
    confidence: float
    first_seen: datetime
    last_seen: datetime
    occurrence_count: int
    success_rate: float


class PopupDetector:
    """智能弹窗检测器"""
    
    def __init__(self):
        self.known_patterns: Dict[str, PopupPattern] = {}
        self.learning_data: List[Dict] = []
        self.suspicious_elements: Set[str] = set()
        
        # 弹窗特征关键词
        self.popup_keywords = {
            'close': ['close', '×', '✕', '关闭', '閉じる', 'dismiss'],
            'accept': ['accept', 'ok', 'yes', '同意', '确定', 'agree'],
            'modal': ['modal', 'dialog', 'popup', 'overlay', 'backdrop'],
            'notification': ['notification', 'alert', 'toast', 'message'],
            'advertisement': ['ad', 'advertisement', '广告', 'promo']
        }
        
        # 可疑CSS属性
        self.suspicious_styles = {
            'position': ['fixed', 'absolute'],
            'z-index': lambda x: x and int(x) > 1000,
            'display': ['block', 'flex'],
            'visibility': ['visible'],
            'opacity': lambda x: x and float(x) > 0.5
        }
    
    async def analyze_page(self, page) -> List[Dict]:
        """分析页面中的潜在弹窗元素"""
        try:
            # 获取页面中所有可见元素的信息
            elements_data = await page.evaluate("""
                () => {
                    const elements = [];
                    const allElements = document.querySelectorAll('*');
                    
                    allElements.forEach((el, index) => {
                        const rect = el.getBoundingClientRect();
                        const styles = window.getComputedStyle(el);
                        
                        // 只分析可见且有一定大小的元素
                        if (rect.width > 50 && rect.height > 50 && 
                            styles.display !== 'none' && 
                            styles.visibility !== 'hidden') {
                            
                            elements.push({
                                tagName: el.tagName.toLowerCase(),
                                className: el.className,
                                id: el.id,
                                textContent: el.textContent?.substring(0, 100),
                                position: styles.position,
                                zIndex: styles.zIndex,
                                display: styles.display,
                                opacity: styles.opacity,
                                width: rect.width,
                                height: rect.height,
                                top: rect.top,
                                left: rect.left,
                                hasCloseButton: !!el.querySelector('[aria-label*="close"], .close, .×'),
                                isModal: el.classList.contains('modal') || 
                                        el.classList.contains('dialog') ||
                                        el.classList.contains('popup'),
                                parentClasses: el.parentElement?.className || '',
                                selector: this.generateSelector(el, index)
                            });
                        }
                    });
                    
                    // 生成选择器的辅助函数
                    function generateSelector(element, index) {
                        if (element.id) return '#' + element.id;
                        if (element.className) {
                            const classes = element.className.split(' ').filter(c => c.length > 0);
                            if (classes.length > 0) return '.' + classes.join('.');
                        }
                        return element.tagName.toLowerCase() + ':nth-child(' + (index + 1) + ')';
                    }
                    
                    return elements;
                }
            """)
            
            # 分析每个元素的弹窗可能性
            potential_popups = []
            for element in elements_data:
                score = await self._calculate_popup_score(element)
                if score > 0.3:  # 阈值可调
                    potential_popups.append({
                        'element': element,
                        'score': score,
                        'reasons': await self._get_popup_reasons(element)
                    })
            
            # 按分数排序
            potential_popups.sort(key=lambda x: x['score'], reverse=True)
            
            logger.debug(f"🔍 检测到 {len(potential_popups)} 个潜在弹窗元素")
            return potential_popups
            
        except Exception as e:
            logger.error(f"❌ 页面分析失败: {e}")
            return []
    
    async def _calculate_popup_score(self, element: Dict) -> float:
        """计算元素是弹窗的可能性分数"""
        score = 0.0
        
        # 位置特征 (30%)
        if element.get('position') in ['fixed', 'absolute']:
            score += 0.3
        
        # z-index特征 (20%)
        z_index = element.get('zIndex', '0')
        if z_index.isdigit() and int(z_index) > 1000:
            score += 0.2
        
        # 尺寸特征 (15%)
        width = element.get('width', 0)
        height = element.get('height', 0)
        if width > 300 and height > 200:
            score += 0.15
        
        # 类名特征 (20%)
        class_name = element.get('className', '').lower()
        for keyword_type, keywords in self.popup_keywords.items():
            if any(keyword in class_name for keyword in keywords):
                score += 0.05
        
        # 文本内容特征 (10%)
        text_content = element.get('textContent', '').lower()
        if any(keyword in text_content for keywords in self.popup_keywords.values() for keyword in keywords):
            score += 0.1
        
        # 结构特征 (5%)
        if element.get('hasCloseButton'):
            score += 0.05
        if element.get('isModal'):
            score += 0.05
        
        return min(score, 1.0)
    
    async def _get_popup_reasons(self, element: Dict) -> List[str]:
        """获取判断为弹窗的原因"""
        reasons = []
        
        if element.get('position') in ['fixed', 'absolute']:
            reasons.append(f"固定定位: {element.get('position')}")
        
        z_index = element.get('zIndex', '0')
        if z_index.isdigit() and int(z_index) > 1000:
            reasons.append(f"高z-index: {z_index}")
        
        if element.get('hasCloseButton'):
            reasons.append("包含关闭按钮")
        
        if element.get('isModal'):
            reasons.append("模态框类名")
        
        class_name = element.get('className', '').lower()
        for keyword_type, keywords in self.popup_keywords.items():
            matching_keywords = [kw for kw in keywords if kw in class_name]
            if matching_keywords:
                reasons.append(f"{keyword_type}关键词: {matching_keywords}")
        
        return reasons
    
    async def learn_from_success(self, selector: str, action: str, success: bool):
        """从处理结果中学习"""
        learning_entry = {
            'selector': selector,
            'action': action,
            'success': success,
            'timestamp': datetime.now().isoformat()
        }
        
        self.learning_data.append(learning_entry)
        
        # 更新模式数据
        pattern_key = f"{selector}_{action}"
        if pattern_key in self.known_patterns:
            pattern = self.known_patterns[pattern_key]
            pattern.occurrence_count += 1
            pattern.last_seen = datetime.now()
            
            # 更新成功率
            total_attempts = pattern.occurrence_count
            successful_attempts = sum(1 for entry in self.learning_data 
                                    if entry['selector'] == selector and entry['success'])
            pattern.success_rate = successful_attempts / total_attempts
            
        logger.debug(f"📚 学习记录: {selector} -> {action} ({'成功' if success else '失败'})")
    
    async def suggest_new_rules(self, min_confidence: float = 0.7) -> List[Dict]:
        """基于学习数据建议新的处理规则"""
        suggestions = []
        
        # 分析学习数据中的模式
        selector_stats = {}
        for entry in self.learning_data:
            selector = entry['selector']
            if selector not in selector_stats:
                selector_stats[selector] = {
                    'total': 0,
                    'successful': 0,
                    'actions': {}
                }
            
            selector_stats[selector]['total'] += 1
            if entry['success']:
                selector_stats[selector]['successful'] += 1
            
            action = entry['action']
            if action not in selector_stats[selector]['actions']:
                selector_stats[selector]['actions'][action] = {'total': 0, 'successful': 0}
            
            selector_stats[selector]['actions'][action]['total'] += 1
            if entry['success']:
                selector_stats[selector]['actions'][action]['successful'] += 1
        
        # 生成建议
        for selector, stats in selector_stats.items():
            if stats['total'] >= 3:  # 至少出现3次
                success_rate = stats['successful'] / stats['total']
                if success_rate >= min_confidence:
                    # 找到最佳动作
                    best_action = None
                    best_action_rate = 0
                    
                    for action, action_stats in stats['actions'].items():
                        if action_stats['total'] >= 2:
                            action_rate = action_stats['successful'] / action_stats['total']
                            if action_rate > best_action_rate:
                                best_action = action
                                best_action_rate = action_rate
                    
                    if best_action and best_action_rate >= min_confidence:
                        suggestions.append({
                            'name': f"learned_{selector.replace('.', '_').replace('#', '_')}",
                            'type': 'custom',
                            'selectors': [selector],
                            'action': best_action,
                            'priority': int(success_rate * 100),
                            'confidence': success_rate,
                            'description': f"机器学习建议规则 (成功率: {success_rate:.2%})"
                        })
        
        logger.info(f"🤖 基于学习数据建议了 {len(suggestions)} 个新规则")
        return suggestions
    
    async def export_learning_data(self, filepath: str):
        """导出学习数据"""
        try:
            export_data = {
                'learning_data': self.learning_data,
                'known_patterns': {k: asdict(v) for k, v in self.known_patterns.items()},
                'export_time': datetime.now().isoformat(),
                'total_entries': len(self.learning_data)
            }
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2, default=str)
            
            logger.info(f"📤 学习数据已导出到: {filepath}")
            
        except Exception as e:
            logger.error(f"❌ 导出学习数据失败: {e}")
    
    async def import_learning_data(self, filepath: str):
        """导入学习数据"""
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                import_data = json.load(f)
            
            self.learning_data.extend(import_data.get('learning_data', []))
            
            # 重建模式数据
            for pattern_key, pattern_data in import_data.get('known_patterns', {}).items():
                pattern_data['first_seen'] = datetime.fromisoformat(pattern_data['first_seen'])
                pattern_data['last_seen'] = datetime.fromisoformat(pattern_data['last_seen'])
                self.known_patterns[pattern_key] = PopupPattern(**pattern_data)
            
            logger.info(f"📥 学习数据已从 {filepath} 导入")
            
        except Exception as e:
            logger.error(f"❌ 导入学习数据失败: {e}")


# 全局检测器实例
popup_detector = PopupDetector()
