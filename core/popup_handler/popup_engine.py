#!/usr/bin/env python3
"""
企业级通用弹窗处理引擎
支持多种弹窗类型的智能检测和处理
"""

import asyncio
import json
from typing import Dict, List, Optional, Callable, Any
from dataclasses import dataclass
from enum import Enum
from logger_config import logger


class PopupType(Enum):
    """弹窗类型枚举"""
    MODAL = "modal"
    ALERT = "alert"
    NOTIFICATION = "notification"
    OVERLAY = "overlay"
    WIDGET = "widget"
    CONFIRMATION = "confirmation"
    ADVERTISEMENT = "advertisement"
    COOKIE_CONSENT = "cookie_consent"
    CUSTOM = "custom"


class PopupAction(Enum):
    """弹窗处理动作"""
    CLOSE = "close"
    ACCEPT = "accept"
    DISMISS = "dismiss"
    HIDE = "hide"
    CLICK_OUTSIDE = "click_outside"
    PRESS_ESC = "press_esc"
    CUSTOM_SCRIPT = "custom_script"


@dataclass
class PopupRule:
    """弹窗处理规则"""
    name: str
    popup_type: PopupType
    selectors: List[str]
    action: PopupAction
    priority: int = 50
    timeout: int = 5000
    retry_count: int = 3
    custom_script: Optional[str] = None
    validation_selector: Optional[str] = None
    description: str = ""


class PopupEngine:
    """通用弹窗处理引擎"""
    
    def __init__(self, config_path: str = "configs/popup_rules.yaml"):
        self.rules: List[PopupRule] = []
        self.custom_handlers: Dict[str, Callable] = {}
        self.config_path = config_path
        self.stats = {
            "total_detected": 0,
            "total_handled": 0,
            "failed_attempts": 0,
            "by_type": {}
        }
        
    async def initialize(self):
        """初始化弹窗引擎"""
        await self._load_rules()
        await self._register_default_handlers()
        logger.info("🔧 弹窗处理引擎初始化完成")
        
    async def _load_rules(self):
        """加载弹窗处理规则"""
        try:
            import yaml
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            for rule_data in config.get('popup_rules', []):
                rule = PopupRule(
                    name=rule_data['name'],
                    popup_type=PopupType(rule_data['type']),
                    selectors=rule_data['selectors'],
                    action=PopupAction(rule_data['action']),
                    priority=rule_data.get('priority', 50),
                    timeout=rule_data.get('timeout', 5000),
                    retry_count=rule_data.get('retry_count', 3),
                    custom_script=rule_data.get('custom_script'),
                    validation_selector=rule_data.get('validation_selector'),
                    description=rule_data.get('description', '')
                )
                self.rules.append(rule)
            
            # 按优先级排序
            self.rules.sort(key=lambda x: x.priority, reverse=True)
            logger.info(f"📋 加载了 {len(self.rules)} 个弹窗处理规则")
            
        except Exception as e:
            logger.warning(f"⚠️ 弹窗规则加载失败，使用默认规则: {e}")
            await self._create_default_rules()
    
    async def _create_default_rules(self):
        """创建默认弹窗处理规则"""
        default_rules = [
            # Karte客服组件
            PopupRule(
                name="karte_widget",
                popup_type=PopupType.WIDGET,
                selectors=["#karte-c", ".karte-widget__container", ".karte-c"],
                action=PopupAction.HIDE,
                priority=90,
                description="Karte客服聊天组件"
            ),
            # 模态对话框
            PopupRule(
                name="modal_dialog",
                popup_type=PopupType.MODAL,
                selectors=[".modal .close", ".modal-header .close", "button[data-dismiss='modal']"],
                action=PopupAction.CLOSE,
                priority=80,
                description="通用模态对话框"
            ),
            # 通知弹窗
            PopupRule(
                name="notification",
                popup_type=PopupType.NOTIFICATION,
                selectors=[".notification-close", ".alert .close", ".toast .close"],
                action=PopupAction.CLOSE,
                priority=70,
                description="通知类弹窗"
            ),
            # Cookie同意
            PopupRule(
                name="cookie_consent",
                popup_type=PopupType.COOKIE_CONSENT,
                selectors=["button:has-text('同意')", "button:has-text('Accept')", ".cookie-accept"],
                action=PopupAction.ACCEPT,
                priority=60,
                description="Cookie同意弹窗"
            ),
            # 广告弹窗
            PopupRule(
                name="advertisement",
                popup_type=PopupType.ADVERTISEMENT,
                selectors=[".ad-close", ".advertisement .close", "button:has-text('×')"],
                action=PopupAction.CLOSE,
                priority=50,
                description="广告弹窗"
            )
        ]
        
        self.rules.extend(default_rules)
        logger.info("📋 创建了默认弹窗处理规则")
    
    async def _register_default_handlers(self):
        """注册默认处理器"""
        self.custom_handlers.update({
            "hide_element": self._hide_element_handler,
            "remove_element": self._remove_element_handler,
            "click_outside": self._click_outside_handler,
            "press_escape": self._press_escape_handler
        })
    
    async def handle_popups(self, page, context: str = "general") -> bool:
        """
        智能处理页面弹窗（kaipoke tennki完全禁用版本）

        Args:
            page: Playwright页面对象
            context: 上下文信息，用于日志记录

        Returns:
            bool: 是否处理了弹窗
        """
        # 🛡️ 对于kaipoke tennki相关上下文，完全禁用弹窗处理
        if ("kaipoke" in context.lower() or "tennki" in context.lower() or
            "form" in context.lower() or "modal" in context.lower()):
            logger.debug(f"🛡️ Kaipoke Tennki上下文检测到，完全禁用弹窗处理: {context}")
            return False

        handled = False

        try:
            # 等待页面稳定
            await page.wait_for_timeout(1000)

            # 🆕 通知弹窗处理已完全禁用
            # notification_handled = await self._detect_and_close_notification_popups(page)

            # 按优先级处理弹窗（仅限非kaipoke上下文）
            for rule in self.rules:
                if await self._handle_popup_by_rule(page, rule, context):
                    handled = True
                    self.stats["total_handled"] += 1
                    self._update_stats(rule.popup_type)

                    # 验证处理结果
                    if rule.validation_selector:
                        if await page.locator(rule.validation_selector).count() > 0:
                            logger.warning(f"⚠️ 弹窗处理后验证失败: {rule.name}")
                            continue

                    logger.info(f"✅ 成功处理弹窗: {rule.name} ({context})")
                    break

            # 如果没有匹配的规则，尝试通用处理
            if not handled:
                handled = await self._fallback_handling(page, context)

            return handled
            
        except Exception as e:
            logger.error(f"❌ 弹窗处理过程中出错 ({context}): {e}")
            self.stats["failed_attempts"] += 1
            return False
    
    async def _handle_popup_by_rule(self, page, rule: PopupRule, context: str) -> bool:
        """根据规则处理弹窗"""
        try:
            # 检查弹窗是否存在
            popup_found = False
            target_element = None
            
            for selector in rule.selectors:
                try:
                    count = await page.locator(selector).count()
                    if count > 0:
                        element = page.locator(selector).first
                        if await element.is_visible():
                            popup_found = True
                            target_element = element
                            self.stats["total_detected"] += 1
                            logger.debug(f"🔍 发现弹窗: {rule.name} - {selector}")
                            break
                except Exception as e:
                    logger.debug(f"检查选择器失败 {selector}: {e}")
                    continue
            
            if not popup_found:
                return False
            
            # 执行处理动作
            success = await self._execute_action(page, rule, target_element)
            
            if success:
                # 等待处理完成
                await page.wait_for_timeout(500)
                return True
            
            return False
            
        except Exception as e:
            logger.debug(f"规则处理失败 {rule.name}: {e}")
            return False
    
    async def _execute_action(self, page, rule: PopupRule, element) -> bool:
        """执行弹窗处理动作"""
        try:
            if rule.action == PopupAction.CLOSE:
                await element.click()
                
            elif rule.action == PopupAction.ACCEPT:
                await element.click()
                
            elif rule.action == PopupAction.DISMISS:
                await element.click()
                
            elif rule.action == PopupAction.HIDE:
                await self._hide_element_handler(page, rule.selectors)
                
            elif rule.action == PopupAction.PRESS_ESC:
                await page.keyboard.press('Escape')
                
            elif rule.action == PopupAction.CLICK_OUTSIDE:
                await self._click_outside_handler(page, element)
                
            elif rule.action == PopupAction.CUSTOM_SCRIPT:
                if rule.custom_script:
                    await page.evaluate(rule.custom_script)
                    
            return True
            
        except Exception as e:
            logger.debug(f"动作执行失败 {rule.action}: {e}")
            return False
    
    async def _hide_element_handler(self, page, selectors: List[str]):
        """隐藏元素处理器"""
        for selector in selectors:
            try:
                await page.evaluate(f"""
                    const elements = document.querySelectorAll('{selector}');
                    elements.forEach(element => {{
                        element.style.display = 'none';
                        element.style.visibility = 'hidden';
                        element.style.zIndex = '-9999';
                        element.style.opacity = '0';
                    }});
                """)
            except Exception as e:
                logger.debug(f"隐藏元素失败 {selector}: {e}")
    
    async def _remove_element_handler(self, page, selectors: List[str]):
        """移除元素处理器"""
        for selector in selectors:
            try:
                await page.evaluate(f"""
                    const elements = document.querySelectorAll('{selector}');
                    elements.forEach(element => element.remove());
                """)
            except Exception as e:
                logger.debug(f"移除元素失败 {selector}: {e}")
    
    async def _click_outside_handler(self, page, element):
        """点击外部区域处理器"""
        try:
            # 获取元素边界
            box = await element.bounding_box()
            if box:
                # 点击元素外部
                await page.click(f"{box['x'] + box['width'] + 10}, {box['y']}")
        except Exception as e:
            logger.debug(f"点击外部失败: {e}")
    
    async def _press_escape_handler(self, page, *args):
        """按ESC键处理器"""
        try:
            await page.keyboard.press('Escape')
        except Exception as e:
            logger.debug(f"按ESC键失败: {e}")
    
    async def _fallback_handling(self, page, context: str) -> bool:
        """备用处理方案"""
        try:
            # 尝试按ESC键
            await page.keyboard.press('Escape')
            await page.wait_for_timeout(500)
            
            logger.info(f"⌨️ 使用备用方案处理弹窗 ({context})")
            return True
            
        except Exception as e:
            logger.debug(f"备用处理失败: {e}")
            return False

    async def _handle_popups_with_form_protection(self, page, context: str) -> bool:
        """🆕 带表单保护的弹窗处理"""
        logger.debug(f"🛡️ 启动表单保护模式处理弹窗: {context}")
        handled = False

        try:
            # 1. 检测是否存在数据登录表单
            form_exists = await self._detect_data_entry_form(page)

            if form_exists:
                logger.info("🛡️ 检测到数据登录表单，启用保护模式")
                # 只处理非表单相关的弹窗
                handled = await self._handle_non_form_popups(page, context)
            else:
                # 没有表单，正常处理所有弹窗
                logger.debug("📋 未检测到表单，执行标准弹窗处理")
                for rule in self.rules:
                    if await self._handle_popup_by_rule(page, rule, context):
                        handled = True
                        self.stats["total_handled"] += 1
                        self._update_stats(rule.popup_type)
                        break

            return handled

        except Exception as e:
            logger.error(f"❌ 表单保护模式处理失败: {e}")
            return False

    async def _detect_data_entry_form(self, page) -> bool:
        """🆕 检测数据登录表单是否存在"""
        try:
            form_indicators = [
                '#registModal',
                '#inPopupInsuranceDivision01',
                '#inPopupInsuranceDivision02',
                '#inPopupServiceKindId',
                '#btnRegisPop'
            ]

            for indicator in form_indicators:
                count = await page.locator(indicator).count()
                if count > 0:
                    # 进一步检查是否可见
                    is_visible = await page.locator(indicator).is_visible()
                    if is_visible:
                        logger.debug(f"🔍 检测到表单元素: {indicator}")
                        return True

            return False

        except Exception as e:
            logger.debug(f"表单检测失败: {e}")
            return False

    async def _handle_non_form_popups(self, page, context: str) -> bool:
        """🆕 处理非表单相关的弹窗"""
        handled = False

        try:
            # 定义需要清理的遮挡性弹窗（不包括表单）
            blocking_popup_selectors = [
                # Karte客服组件
                "#karte-c",
                ".karte-widget__container",
                ".karte-c",
                "[id*='karte']",

                # 通知类弹窗
                ".notification-close",
                ".alert .close",
                ".toast .close",

                # 广告弹窗
                ".ad-close",
                ".advertisement .close",
                ".popup-ad .close",

                # 遮挡性模态框（排除表单模态框）
                ".modal:not(#registModal) .close",
                ".modal-backdrop:not([data-form-related])"
            ]

            for selector in blocking_popup_selectors:
                try:
                    count = await page.locator(selector).count()
                    if count > 0:
                        element = page.locator(selector).first
                        if await element.is_visible():
                            await element.click()
                            handled = True
                            logger.info(f"✅ 清理遮挡性弹窗: {selector}")
                            await page.wait_for_timeout(500)
                            break
                except Exception as e:
                    logger.debug(f"清理弹窗失败 {selector}: {e}")
                    continue

            # 特殊处理：清理可能遮挡新規追加按钮的元素
            if "add_button" in context or "新規追加" in context:
                await self._clear_button_blocking_elements(page)

            return handled

        except Exception as e:
            logger.error(f"❌ 非表单弹窗处理失败: {e}")
            return False

    async def _clear_button_blocking_elements(self, page):
        """🆕 清理可能遮挡新規追加按钮的元素"""
        logger.debug("🔧 清理可能遮挡新規追加按钮的元素")

        try:
            # 使用JavaScript精准清理遮挡元素
            await page.evaluate("""
                () => {
                    // 1. 清理高z-index的遮挡元素
                    const highZIndexElements = document.querySelectorAll('*');
                    highZIndexElements.forEach(el => {
                        const style = window.getComputedStyle(el);
                        const zIndex = parseInt(style.zIndex);

                        // 如果z-index很高且不是表单相关元素
                        if (zIndex > 1000 &&
                            !el.closest('#registModal') &&
                            !el.id.includes('inPopup') &&
                            !el.id.includes('btnRegisPop')) {

                            // 检查是否遮挡按钮区域
                            const rect = el.getBoundingClientRect();
                            if (rect.width > 100 && rect.height > 50) {
                                el.style.display = 'none';
                                console.log('清理遮挡元素:', el.className || el.id);
                            }
                        }
                    });

                    // 2. 特殊处理：确保新規追加按钮可点击
                    const addButton = document.querySelector('#btn_area .cf:nth-child(1) :nth-child(1)');
                    if (addButton) {
                        addButton.style.zIndex = '99999';
                        addButton.style.position = 'relative';
                        addButton.style.pointerEvents = 'auto';
                    }
                }
            """)

            logger.debug("✅ 按钮遮挡元素清理完成")

        except Exception as e:
            logger.warning(f"⚠️ 按钮遮挡元素清理失败: {e}")

    async def _detect_and_close_notification_popups(self, page) -> bool:
        """🆕 检测并关闭通知类弹窗（完全禁用版本）"""
        logger.debug("🛡️ 通知弹窗处理已完全禁用，保护数据登录表单")

        # 完全禁用通知弹窗处理，避免误关闭数据登录表单
        return False

    async def _close_specific_notification_modal(self, page, modal_index: int = 0) -> bool:
        """🆕 关闭特定的通知模态框"""
        try:
            logger.debug(f"🔧 尝试关闭第 {modal_index + 1} 个通知模态框")

            # 尝试多种关闭方式
            close_result = await page.evaluate(f"""
                (modalIndex) => {{
                    const modals = document.querySelectorAll('.modal:visible');
                    if (modalIndex >= modals.length) return false;

                    const modal = modals[modalIndex];
                    let closed = false;

                    // 方法1: 查找并点击关闭按钮
                    const closeSelectors = [
                        '.close', '.btn-close', '.modal-close',
                        'button[aria-label="Close"]',
                        'button:contains("×")', 'button:contains("✕")',
                        'button:contains("閉じる")', 'button:contains("OK")',
                        'button:contains("お知らせはこちら")',
                        '.modal-header .close',
                        '.modal-footer button'
                    ];

                    for (const selector of closeSelectors) {{
                        const btn = modal.querySelector(selector);
                        if (btn) {{
                            // 确保按钮可点击
                            btn.style.pointerEvents = 'auto';
                            btn.style.zIndex = '99999';
                            btn.style.position = 'relative';

                            // 点击按钮
                            btn.click();
                            closed = true;
                            console.log('通过按钮关闭:', selector);
                            break;
                        }}
                    }}

                    // 方法2: 如果没有找到关闭按钮，尝试点击模态框外部
                    if (!closed) {{
                        const backdrop = document.querySelector('.modal-backdrop');
                        if (backdrop) {{
                            backdrop.click();
                            closed = true;
                            console.log('通过背景关闭');
                        }}
                    }}

                    // 方法3: 强制隐藏模态框
                    if (!closed) {{
                        modal.style.display = 'none';
                        modal.style.visibility = 'hidden';
                        modal.style.opacity = '0';
                        modal.style.zIndex = '-9999';

                        // 移除模态框相关类
                        modal.classList.remove('in', 'show', 'fade');
                        modal.setAttribute('aria-hidden', 'true');

                        // 清理body样式
                        document.body.classList.remove('modal-open');
                        document.body.style.overflow = '';
                        document.body.style.paddingRight = '';

                        // 移除背景遮罩
                        const backdrops = document.querySelectorAll('.modal-backdrop');
                        backdrops.forEach(backdrop => backdrop.remove());

                        closed = true;
                        console.log('强制隐藏模态框');
                    }}

                    return closed;
                }}
            """, modal_index)

            if close_result:
                logger.info("✅ 通知模态框关闭成功")
                await page.wait_for_timeout(1000)  # 等待关闭动画完成
                return True
            else:
                logger.warning("⚠️ 通知模态框关闭失败")
                return False

        except Exception as e:
            logger.error(f"❌ 关闭通知模态框时出错: {e}")
            return False

    async def _try_close_notification(self, page, indicator: str) -> bool:
        """🆕 尝试关闭通知弹窗"""
        logger.debug(f"🔧 尝试关闭通知弹窗: {indicator}")

        # 定义可能的关闭按钮选择器
        close_selectors = [
            # 通用关闭按钮
            ".modal .close",
            ".modal .btn-close",
            ".modal button[aria-label='Close']",
            ".popup .close",
            ".notification .close",

            # 特定的关闭按钮
            "button:has-text('×')",
            "button:has-text('✕')",
            "button:has-text('閉じる')",
            "button:has-text('OK')",

            # Kaipoke特有的按钮
            "button:has-text('お知らせはこちら')",
            ".modal-footer button",

            # 通过点击背景关闭
            ".modal-backdrop",
            ".overlay"
        ]

        for close_selector in close_selectors:
            try:
                element_count = await page.locator(close_selector).count()
                if element_count > 0:
                    element = page.locator(close_selector).first
                    if await element.is_visible():
                        await element.click()
                        await page.wait_for_timeout(1000)  # 等待关闭动画

                        # 验证是否成功关闭
                        remaining_count = await page.locator(indicator).count()
                        if remaining_count == 0:
                            logger.info(f"✅ 使用选择器成功关闭: {close_selector}")
                            return True

            except Exception as e:
                logger.debug(f"关闭尝试失败 {close_selector}: {e}")
                continue

        # 如果常规方法失败，尝试JavaScript强制关闭（增强版）
        try:
            logger.debug("🔄 尝试JavaScript强制关闭通知弹窗")
            result = await page.evaluate("""
                () => {
                    // 🆕 增强的弹窗检测和关闭逻辑
                    let closed = false;

                    // 1. 首先清理所有背景遮挡层
                    const backdrops = document.querySelectorAll('.modal-backdrop, .overlay, .backdrop');
                    backdrops.forEach(backdrop => {
                        backdrop.style.pointerEvents = 'none';
                        backdrop.style.zIndex = '1';
                    });

                    // 2. 查找所有可能的弹窗元素
                    const selectors = [
                        '.modal', '.popup', '.notification', '.alert',
                        '[class*="modal"]', '[class*="popup"]', '[class*="dialog"]',
                        'div:has-text("重要")', 'div:has-text("お知らせ")'
                    ];

                    const allPopups = [];
                    selectors.forEach(selector => {
                        try {
                            const elements = document.querySelectorAll(selector);
                            elements.forEach(el => {
                                const style = window.getComputedStyle(el);
                                if (style.display !== 'none' &&
                                    style.visibility !== 'hidden' &&
                                    (style.position === 'fixed' || style.position === 'absolute') &&
                                    parseInt(style.zIndex) > 100) {
                                    allPopups.push(el);
                                }
                            });
                        } catch (e) {
                            // 忽略选择器错误
                        }
                    });

                    // 3. 按z-index降序处理弹窗
                    allPopups.sort((a, b) => {
                        const aZ = parseInt(window.getComputedStyle(a).zIndex) || 0;
                        const bZ = parseInt(window.getComputedStyle(b).zIndex) || 0;
                        return bZ - aZ;
                    });

                    // 4. 逐个处理弹窗
                    allPopups.forEach(popup => {
                        try {
                            // 方法1: 查找并点击关闭按钮
                            const closeSelectors = [
                                '.close', '.btn-close', '.modal-close',
                                'button[aria-label="Close"]',
                                'button:contains("×")', 'button:contains("✕")',
                                'button:contains("閉じる")', 'button:contains("OK")',
                                'button:contains("お知らせはこちら")'
                            ];

                            let buttonClicked = false;
                            closeSelectors.forEach(selector => {
                                if (buttonClicked) return;

                                const btn = popup.querySelector(selector) ||
                                           popup.querySelector(`button:contains("${selector.replace('button:contains("', '').replace('")', '')}")`);

                                if (btn) {
                                    // 强制设置按钮可点击
                                    btn.style.pointerEvents = 'auto';
                                    btn.style.zIndex = '99999';
                                    btn.style.position = 'relative';

                                    // 触发点击事件
                                    btn.click();
                                    buttonClicked = true;
                                    closed = true;
                                }
                            });

                            // 方法2: 如果没有找到按钮，直接移除弹窗
                            if (!buttonClicked) {
                                popup.style.display = 'none';
                                popup.style.visibility = 'hidden';
                                popup.remove();
                                closed = true;
                            }

                        } catch (e) {
                            // 如果处理失败，强制移除
                            try {
                                popup.remove();
                                closed = true;
                            } catch (e2) {
                                // 最后手段：隐藏
                                popup.style.display = 'none';
                            }
                        }
                    });

                    // 5. 最后清理所有背景层
                    document.querySelectorAll('.modal-backdrop, .overlay, .backdrop').forEach(backdrop => {
                        backdrop.remove();
                    });

                    // 6. 恢复页面滚动（防止弹窗锁定滚动）
                    document.body.style.overflow = '';
                    document.documentElement.style.overflow = '';

                    return closed;
                }
            """)

            if result:
                logger.info("✅ JavaScript强制关闭成功")
                return True

        except Exception as e:
            logger.warning(f"⚠️ JavaScript强制关闭失败: {e}")

        return False
    
    def _update_stats(self, popup_type: PopupType):
        """更新统计信息"""
        type_name = popup_type.value
        if type_name not in self.stats["by_type"]:
            self.stats["by_type"][type_name] = 0
        self.stats["by_type"][type_name] += 1
    
    def get_stats(self) -> Dict[str, Any]:
        """获取处理统计信息"""
        return self.stats.copy()
    
    def register_custom_handler(self, name: str, handler: Callable):
        """注册自定义处理器"""
        self.custom_handlers[name] = handler
        logger.info(f"📝 注册自定义弹窗处理器: {name}")
    
    async def add_dynamic_rule(self, rule: PopupRule):
        """动态添加弹窗处理规则"""
        self.rules.append(rule)
        self.rules.sort(key=lambda x: x.priority, reverse=True)
        logger.info(f"📝 动态添加弹窗规则: {rule.name}")


# 全局弹窗引擎实例
popup_engine = PopupEngine()
