"""
Google Sheets多Sheet管理器
基于RPA代码的多Sheet创建和数据写入逻辑

核心功能：
1. 创建新的Google Spreadsheet
2. 设置多个Sheet（当月请求、当月服务提供、前月以前请求）
3. 批量写入风险控制
4. Google Drive文件夹管理
"""

import asyncio
from datetime import datetime
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
from logger_config import logger
from core.gsuite.sheets_client import SheetsClient
from core.gsuite.drive_client import DriveClient
import time


class MultiSheetManager:
    """Google Sheets多Sheet管理器"""
    
    def __init__(self, sheets_client: SheetsClient = None, drive_client: DriveClient = None):
        self.sheets_client = sheets_client or SheetsClient()
        self.drive_client = drive_client
        
        # 修正后的Sheet名称
        self.sheet_names = {
            'original': '当月請求',  # 原来的シート1改名为当月請求
            'current_service': '当月サービス提供',  # 目标月数据
            'previous_billing': '前月以前請求'  # 目标月以外数据
        }
    
    async def create_billing_spreadsheet(self, facility_name: str, service_type: str, gdrive_folder_id: str = None):
        """
        创建账单Google Spreadsheet
        🆕 权限问题解决方案：优先尝试创建新表格，失败时使用现有表格
        🆕 支持直接创建在指定的Google Drive文件夹中
        """
        try:
            # 生成Spreadsheet标题（基于RPA代码）
            spreadsheet_title = f"{service_type}{facility_name}"

            logger.info(f"📊 尝试创建Google Spreadsheet: {spreadsheet_title}")
            if gdrive_folder_id:
                logger.info(f"📁 目标文件夹: {gdrive_folder_id}")

            # 🆕 优先尝试使用Drive API创建Google Sheets
            try:
                return await self._create_spreadsheet_via_drive_api(spreadsheet_title, gdrive_folder_id)
            except Exception as drive_error:
                logger.warning(f"⚠️ Drive API创建失败: {drive_error}")

                # 回退到Sheets API（不支持文件夹指定）
                logger.info("🔄 尝试使用Sheets API...")
                spreadsheet_body = {
                    'properties': {
                        'title': spreadsheet_title
                    }
                }

                spreadsheet = self.sheets_client.service.spreadsheets().create(
                    body=spreadsheet_body
                ).execute()

                spreadsheet_id = spreadsheet.get('spreadsheetId')
                logger.info(f"✅ Spreadsheet创建成功: {spreadsheet_id}")

                # 如果指定了文件夹，尝试移动文件
                if gdrive_folder_id and self.drive_client:
                    try:
                        await self._move_file_to_folder(spreadsheet_id, gdrive_folder_id)
                    except Exception as move_error:
                        logger.warning(f"⚠️ 移动文件到文件夹失败: {move_error}")

                return spreadsheet_id

        except Exception as e:
            logger.warning(f"⚠️ 创建新Spreadsheet失败: {e}")
            logger.info("🔄 尝试使用现有表格作为替代方案...")

            # 使用现有表格作为替代方案
            return await self._use_existing_spreadsheet_fallback(facility_name, service_type)

    async def _use_existing_spreadsheet_fallback(self, facility_name: str, service_type: str):
        """
        使用现有表格的备用方案
        当无法创建新表格时，在现有表格中创建新的工作表
        """
        try:
            # 使用配置中的现有表格ID
            existing_spreadsheet_id = "17WdiO-6blMe2LFVp4ZjmR2F49mhF0YA_VgmDZJsBAAI"  # 更新的表格ID

            logger.info(f"📋 使用现有表格: {existing_spreadsheet_id}")

            # 验证表格是否可访问
            try:
                spreadsheet_info = self.sheets_client.service.spreadsheets().get(
                    spreadsheetId=existing_spreadsheet_id
                ).execute()

                logger.info(f"✅ 成功访问现有表格: {spreadsheet_info.get('properties', {}).get('title', 'Unknown')}")

                # 使用标准的工作表名称，不添加前缀
                self.sheet_names = {
                    'original': '当月請求',
                    'current_service': '当月サービス提供',
                    'previous_billing': '前月以前請求'
                }

                logger.info(f"📋 使用标准工作表名称: {list(self.sheet_names.values())}")

                return existing_spreadsheet_id

            except Exception as access_error:
                logger.error(f"❌ 无法访问现有表格: {access_error}")
                raise Exception(f"无法创建新表格且无法访问现有表格: {access_error}")

        except Exception as e:
            logger.error(f"❌ 备用方案失败: {e}")
            raise

    async def _create_spreadsheet_via_drive_api(self, title: str, gdrive_folder_id: str = None):
        """
        使用Drive API创建Google Sheets
        这可能会绕过Sheets API的权限限制
        🆕 支持直接创建在指定的Google Drive文件夹中
        """
        try:
            logger.info(f"📊 尝试使用Drive API创建表格: {title}")

            if not self.drive_client:
                raise Exception("Drive客户端未初始化")

            # 使用Drive API创建Google Sheets文件
            file_metadata = {
                'name': title,
                'mimeType': 'application/vnd.google-apps.spreadsheet'
            }

            # 🆕 如果指定了文件夹ID，设置父文件夹
            if gdrive_folder_id:
                file_metadata['parents'] = [gdrive_folder_id]
                logger.info(f"📁 设置父文件夹: {gdrive_folder_id}")

            file = self.drive_client.service.files().create(
                body=file_metadata,
                fields='id'
            ).execute()

            spreadsheet_id = file.get('id')
            logger.info(f"✅ 使用Drive API成功创建表格: {spreadsheet_id}")
            if gdrive_folder_id:
                logger.info(f"📁 表格已创建在指定文件夹中")

            return spreadsheet_id

        except Exception as e:
            logger.error(f"❌ Drive API创建表格失败: {e}")
            raise

    async def _move_file_to_folder(self, file_id: str, folder_id: str):
        """
        将文件移动到指定文件夹
        """
        try:
            logger.info(f"📁 移动文件到文件夹: {file_id} → {folder_id}")

            if not self.drive_client:
                raise Exception("Drive客户端未初始化")

            # 获取文件当前的父文件夹
            file = self.drive_client.service.files().get(
                fileId=file_id,
                fields='parents'
            ).execute()

            previous_parents = ",".join(file.get('parents'))

            # 移动文件到新文件夹
            self.drive_client.service.files().update(
                fileId=file_id,
                addParents=folder_id,
                removeParents=previous_parents,
                fields='id, parents'
            ).execute()

            logger.info(f"✅ 文件移动成功")

        except Exception as e:
            logger.error(f"❌ 移动文件失败: {e}")
            raise

    async def rename_spreadsheet(self, spreadsheet_id: str, new_name: str):
        """
        重命名Spreadsheet
        """
        try:
            logger.info(f"📝 重命名Spreadsheet: {spreadsheet_id} → {new_name}")

            if not self.drive_client:
                logger.warning("⚠️ Drive客户端未初始化，无法重命名")
                return False

            # 使用Drive API重命名文件
            file_metadata = {
                'name': new_name
            }

            self.drive_client.service.files().update(
                fileId=spreadsheet_id,
                body=file_metadata
            ).execute()

            logger.info(f"✅ Spreadsheet重命名成功: {new_name}")
            return True

        except Exception as e:
            logger.error(f"❌ Spreadsheet重命名失败: {e}")
            return False

    async def write_to_existing_spreadsheet(self, spreadsheet_id: str, target_sheet_name: str, classified_data: dict):
        """
        写入数据到现有的Spreadsheet的指定Sheet（如インポート表）
        🆕 新增功能：支持使用现有表格
        """
        try:
            logger.info(f"📊 写入数据到现有Spreadsheet: {spreadsheet_id}, Sheet: {target_sheet_name}")

            # 1. 检查目标Sheet是否存在
            await self._ensure_target_sheet_exists(spreadsheet_id, target_sheet_name)

            # 2. 清空目标Sheet的数据区域（保留表头）
            await self._clear_sheet_data(spreadsheet_id, target_sheet_name)

            # 3. 合并所有分类数据
            all_data = self._merge_classified_data(classified_data)

            # 4. 🆕 批量写入数据到インポート表（基于RPA代码：从A4开始，使用SheetsClient）
            if all_data is not None and not all_data.empty:
                await self._write_to_existing_sheet_with_sheets_client(spreadsheet_id, target_sheet_name, all_data)
                logger.info(f"✅ 数据写入完成到 {target_sheet_name}: {len(all_data)} 行")
            else:
                logger.warning(f"⚠️ 没有数据需要写入到 {target_sheet_name}")

        except Exception as e:
            logger.error(f"❌ 写入现有Spreadsheet失败: {e}")
            raise
    
    async def setup_multiple_sheets(self, spreadsheet_id: str, classified_data: dict):
        """
        设置多个Sheet并写入分类数据
        修正后的多Sheet结构
        """
        try:
            logger.info(f"🔄 开始设置多Sheet结构: {spreadsheet_id}")

            # 1. 首先重命名シート1为当月請求并写入原始数据
            await self._rename_and_write_original_sheet(spreadsheet_id, classified_data)

            # 2. 创建并设置当月服务提供Sheet（目标月数据）
            await self._create_and_setup_sheet(
                spreadsheet_id,
                self.sheet_names['current_service'],
                classified_data.get('current_month_service')
            )

            # 3. 创建并设置前月以前请求Sheet（目标月以外数据）
            await self._create_and_setup_sheet(
                spreadsheet_id,
                self.sheet_names['previous_billing'],
                classified_data.get('previous_month_billing')
            )
            
            # 5. 🆕 保留原始Sheet，不删除
            # await self._delete_original_sheet(spreadsheet_id)  # 注释掉，保留当月請求Sheet
            logger.info("✅ 保留原始Sheet: 当月請求")
            
            logger.info("✅ 多Sheet设置完成")
            
        except Exception as e:
            logger.error(f"❌ 多Sheet设置失败: {e}")
            raise
    
    async def _rename_and_write_original_sheet(self, spreadsheet_id: str, classified_data: dict):
        """重命名シート1为当月請求并写入原始数据"""
        try:
            # 1. 重命名シート1为当月請求
            await self._rename_sheet(spreadsheet_id, 'シート1', self.sheet_names['original'])

            # 2. 写入原始数据
            original_data = classified_data.get('original_data')
            if original_data is not None and not original_data.empty:
                # 准备数据（表头 + 数据行）
                all_data = [list(original_data.columns)]
                all_data.extend(original_data.values.tolist())

                range_name = f"{self.sheet_names['original']}!A1"
                self.sheets_client.write_to_sheet(spreadsheet_id, range_name, all_data)
                logger.info(f"✅ 原始数据写入完成: {len(all_data)} 行")
            else:
                logger.warning("⚠️ 原始数据为空")

        except Exception as e:
            logger.error(f"❌ 重命名和写入原始数据失败: {e}")
            raise

    async def _rename_sheet(self, spreadsheet_id: str, old_name: str, new_name: str):
        """重命名工作表"""
        try:
            logger.info(f"📋 重命名工作表: {old_name} → {new_name}")

            # 获取工作表信息
            sheet_metadata = self.sheets_client.service.spreadsheets().get(
                spreadsheetId=spreadsheet_id
            ).execute()

            # 查找目标工作表的ID
            sheet_id = None
            for sheet in sheet_metadata['sheets']:
                if sheet['properties']['title'] == old_name:
                    sheet_id = sheet['properties']['sheetId']
                    break

            if sheet_id is None:
                raise Exception(f"未找到工作表: {old_name}")

            # 执行重命名
            rename_request = {
                'updateSheetProperties': {
                    'properties': {
                        'sheetId': sheet_id,
                        'title': new_name
                    },
                    'fields': 'title'
                }
            }

            self.sheets_client.service.spreadsheets().batchUpdate(
                spreadsheetId=spreadsheet_id,
                body={'requests': [rename_request]}
            ).execute()

            logger.info(f"✅ 工作表重命名完成: {old_name} → {new_name}")

        except Exception as e:
            logger.error(f"❌ 重命名工作表失败: {e}")
            raise
    
    async def _create_and_setup_sheet(self, spreadsheet_id: str, sheet_name: str, data):
        """创建并设置单个Sheet"""
        try:
            if data is None or data.empty:
                logger.warning(f"⚠️ {sheet_name} 数据为空，跳过创建")
                return
            
            logger.info(f"📋 创建Sheet: {sheet_name}")
            
            # 1. 复制当月請求创建新Sheet
            await self._duplicate_sheet(spreadsheet_id, self.sheet_names['original'], sheet_name)
            
            # 2. 清空新Sheet的数据区域（保留表头）
            await self._clear_sheet_data(spreadsheet_id, sheet_name)
            
            # 3. 🆕 批量写入风险控制：分批写入数据
            await self._batch_write_data(spreadsheet_id, sheet_name, data)
            
            logger.info(f"✅ Sheet {sheet_name} 设置完成")
            
        except Exception as e:
            logger.error(f"❌ 创建Sheet {sheet_name} 失败: {e}")
            raise
    
    async def _duplicate_sheet(self, spreadsheet_id: str, source_sheet: str, target_sheet: str):
        """复制Sheet"""
        try:
            # 获取源Sheet的ID
            spreadsheet = self.sheets_client.service.spreadsheets().get(
                spreadsheetId=spreadsheet_id
            ).execute()
            
            source_sheet_id = None
            for sheet in spreadsheet.get('sheets', []):
                if sheet.get('properties', {}).get('title') == source_sheet:
                    source_sheet_id = sheet.get('properties', {}).get('sheetId')
                    break
            
            if source_sheet_id is None:
                raise Exception(f"找不到源Sheet: {source_sheet}")
            
            # 复制Sheet
            duplicate_request = {
                'duplicateSheet': {
                    'sourceSheetId': source_sheet_id,
                    'newSheetName': target_sheet
                }
            }
            
            self.sheets_client.service.spreadsheets().batchUpdate(
                spreadsheetId=spreadsheet_id,
                body={'requests': [duplicate_request]}
            ).execute()
            
            # 等待操作完成
            await asyncio.sleep(1)
            
            logger.info(f"✅ Sheet复制完成: {source_sheet} → {target_sheet}")
            
        except Exception as e:
            logger.error(f"❌ 复制Sheet失败: {e}")
            raise
    
    async def _clear_sheet_data(self, spreadsheet_id: str, sheet_name: str, start_row: int = 4):
        """
        清空Sheet数据区域（保留表头）
        🆕 支持自定义起始行，默认从A4开始（基于RPA代码）
        """
        try:
            # 🆕 基于RPA代码：清空A4:T行的数据（保留前3行表头）
            clear_range = f"{sheet_name}!A{start_row}:T"

            self.sheets_client.service.spreadsheets().values().clear(
                spreadsheetId=spreadsheet_id,
                range=clear_range
            ).execute()

            logger.info(f"✅ Sheet数据清空完成: {sheet_name}, 范围: A{start_row}:T")

        except Exception as e:
            logger.error(f"❌ 清空Sheet数据失败: {e}")
            raise
    
    async def _batch_write_data(self, spreadsheet_id: str, sheet_name: str, data, batch_size: int = 1000):
        """
        批量写入数据
        🆕 批量写入风险控制：分批处理，避免API限制
        🆕 支持DataFrame和list两种数据格式
        """
        try:
            # 检查数据类型和是否为空
            if data is None:
                logger.info(f"📋 {sheet_name} 数据为空，跳过写入")
                return

            # 处理DataFrame类型
            if hasattr(data, 'empty') and data.empty:
                logger.info(f"📋 {sheet_name} DataFrame为空，跳过写入")
                return

            # 处理list类型
            if isinstance(data, list) and len(data) == 0:
                logger.info(f"📋 {sheet_name} 列表为空，跳过写入")
                return

            logger.info(f"🔄 开始批量写入 {sheet_name}: {len(data)} 行数据")

            # 准备数据（包含表头）
            if hasattr(data, 'columns'):  # DataFrame
                all_data = [list(data.columns)] + data.values.tolist()
            else:  # list
                all_data = data

            # 分批写入
            total_batches = (len(all_data) + batch_size - 1) // batch_size

            for batch_index in range(total_batches):
                start_idx = batch_index * batch_size
                end_idx = min(start_idx + batch_size, len(all_data))
                batch_data = all_data[start_idx:end_idx]

                # 计算写入范围
                start_row = start_idx + 1  # Google Sheets行号从1开始
                range_name = f"{sheet_name}!A{start_row}"

                # 写入当前批次
                self.sheets_client.write_to_sheet(spreadsheet_id, range_name, batch_data)

                logger.info(f"📦 批次 {batch_index + 1}/{total_batches} 写入完成: {len(batch_data)} 行")

                # 批次间短暂休息，避免API限制
                if batch_index < total_batches - 1:
                    await asyncio.sleep(0.5)

            logger.info(f"✅ {sheet_name} 批量写入完成")

        except Exception as e:
            logger.error(f"❌ 批量写入数据失败: {e}")
            raise
    
    async def _delete_original_sheet(self, spreadsheet_id: str):
        """删除原始シート1"""
        try:
            # 获取シート1的ID
            spreadsheet = self.sheets_client.service.spreadsheets().get(
                spreadsheetId=spreadsheet_id
            ).execute()
            
            original_sheet_id = None
            for sheet in spreadsheet.get('sheets', []):
                if sheet.get('properties', {}).get('title') == self.sheet_names['original']:
                    original_sheet_id = sheet.get('properties', {}).get('sheetId')
                    break
            
            if original_sheet_id is None:
                logger.warning(f"⚠️ 找不到原始Sheet: {self.sheet_names['original']}")
                return
            
            # 删除Sheet
            delete_request = {
                'deleteSheet': {
                    'sheetId': original_sheet_id
                }
            }
            
            self.sheets_client.service.spreadsheets().batchUpdate(
                spreadsheetId=spreadsheet_id,
                body={'requests': [delete_request]}
            ).execute()
            
            logger.info(f"✅ 原始Sheet删除完成: {self.sheet_names['original']}")
            
        except Exception as e:
            logger.error(f"❌ 删除原始Sheet失败: {e}")
            # 删除失败不影响主流程
    
    async def move_to_drive_folder(self, spreadsheet_id: str, folder_id: str, facility_name: str):
        """
        移动Spreadsheet到指定Google Drive文件夹
        基于RPA代码的文件夹管理
        """
        try:
            if not self.drive_client or not folder_id:
                logger.warning("⚠️ Drive客户端或文件夹ID未配置，跳过文件移动")
                return
            
            logger.info(f"📁 移动Spreadsheet到文件夹: {folder_id}")
            
            # 使用DriveClient移动文件
            success = self.drive_client.move_file_to_folder(spreadsheet_id, folder_id)
            
            if success:
                logger.info(f"✅ Spreadsheet移动成功: {facility_name}")
            else:
                logger.warning(f"⚠️ Spreadsheet移动失败: {facility_name}")
            
        except Exception as e:
            logger.error(f"❌ 移动Spreadsheet失败: {e}")
            # 移动失败不影响主流程

    async def _ensure_target_sheet_exists(self, spreadsheet_id: str, target_sheet_name: str):
        """确保目标Sheet存在，如果不存在则创建"""
        try:
            # 获取Spreadsheet信息
            spreadsheet = self.sheets_client.service.spreadsheets().get(
                spreadsheetId=spreadsheet_id
            ).execute()

            # 检查目标Sheet是否存在
            sheet_exists = False
            for sheet in spreadsheet.get('sheets', []):
                if sheet.get('properties', {}).get('title') == target_sheet_name:
                    sheet_exists = True
                    break

            if not sheet_exists:
                logger.info(f"📋 创建新Sheet: {target_sheet_name}")
                # 创建新Sheet
                add_sheet_request = {
                    'addSheet': {
                        'properties': {
                            'title': target_sheet_name
                        }
                    }
                }

                self.sheets_client.service.spreadsheets().batchUpdate(
                    spreadsheetId=spreadsheet_id,
                    body={'requests': [add_sheet_request]}
                ).execute()

                # 等待操作完成
                await asyncio.sleep(1)
                logger.info(f"✅ Sheet创建完成: {target_sheet_name}")
            else:
                logger.info(f"📋 Sheet已存在: {target_sheet_name}")

        except Exception as e:
            logger.error(f"❌ 确保Sheet存在失败: {e}")
            raise

    def _merge_classified_data(self, classified_data: dict):
        """
        合并分类数据为单一DataFrame
        🆕 将所有分类数据合并，用于写入インポート表
        """
        try:
            import pandas as pd

            all_dataframes = []

            # 添加当月请求数据（原始数据）
            original_data = classified_data.get('original_data')
            if original_data is not None and not original_data.empty:
                original_data_copy = original_data.copy()
                original_data_copy['データ分類'] = '当月請求'
                all_dataframes.append(original_data_copy)

            # 添加当月服务提供数据
            current_service = classified_data.get('current_month_service')
            if current_service is not None and not current_service.empty:
                current_service_copy = current_service.copy()
                current_service_copy['データ分類'] = '当月サービス提供'
                all_dataframes.append(current_service_copy)

            # 添加前月以前请求数据
            previous_billing = classified_data.get('previous_month_billing')
            if previous_billing is not None and not previous_billing.empty:
                previous_billing_copy = previous_billing.copy()
                previous_billing_copy['データ分類'] = '前月以前請求'
                all_dataframes.append(previous_billing_copy)

            if all_dataframes:
                # 合并所有数据
                merged_df = pd.concat(all_dataframes, ignore_index=True)
                logger.info(f"📊 数据合并完成: {len(merged_df)} 行，包含 {len(all_dataframes)} 个分类")
                return merged_df
            else:
                logger.warning("⚠️ 没有数据需要合并")
                return None

        except Exception as e:
            logger.error(f"❌ 数据合并失败: {e}")
            raise

    async def _batch_write_data_to_existing_sheet(self, spreadsheet_id: str, sheet_name: str, data, start_row: int = 4, batch_size: int = 1000):
        """
        批量写入数据到现有表格
        🆕 基于RPA代码：从指定行开始写入，不包含表头
        """
        try:
            if data is None or data.empty:
                logger.info(f"📋 {sheet_name} 数据为空，跳过写入")
                return

            logger.info(f"🔄 开始批量写入 {sheet_name}: {len(data)} 行数据，从第{start_row}行开始")

            # 🆕 准备数据（不包含表头，直接使用数据行）
            all_data = data.values.tolist()

            # 分批写入
            total_batches = (len(all_data) + batch_size - 1) // batch_size

            for batch_index in range(total_batches):
                start_idx = batch_index * batch_size
                end_idx = min(start_idx + batch_size, len(all_data))
                batch_data = all_data[start_idx:end_idx]

                # 🆕 计算写入范围（从指定行开始）
                current_start_row = start_row + start_idx
                range_name = f"{sheet_name}!A{current_start_row}"

                # 写入当前批次
                self.sheets_client.write_to_sheet(spreadsheet_id, range_name, batch_data)

                logger.info(f"📦 批次 {batch_index + 1}/{total_batches} 写入完成: {len(batch_data)} 行，位置: A{current_start_row}")

                # 批次间短暂休息，避免API限制
                if batch_index < total_batches - 1:
                    await asyncio.sleep(0.5)

            logger.info(f"✅ {sheet_name} 批量写入完成，总计: {len(all_data)} 行")

        except Exception as e:
            logger.error(f"❌ 批量写入现有表格失败: {e}")
            raise

    async def _write_to_existing_sheet_with_sheets_client(self, spreadsheet_id: str, target_sheet_name: str, data):
        """
        使用SheetsClient写入数据到现有表格
        🆕 基于RPA代码和现有SheetsClient逻辑：从A4开始写入
        """
        try:
            logger.info(f"🔄 使用SheetsClient写入数据到 {target_sheet_name}: {len(data)} 行")

            # 1. 🆕 准备数据：不包含表头，只有数据行（基于RPA代码）
            data_values = data.values.tolist()

            # 2. 🆕 写入范围：从A4开始（基于RPA代码）
            write_range = f"{target_sheet_name}!A4"

            # 3. 使用SheetsClient写入数据
            result = self.sheets_client.write_to_sheet(
                spreadsheet_id=spreadsheet_id,
                range_name=write_range,
                values=data_values
            )

            if result:
                logger.info(f"✅ SheetsClient写入完成: {target_sheet_name}, 范围: {write_range}, 行数: {len(data_values)}")
            else:
                raise Exception("SheetsClient写入返回None")

        except Exception as e:
            logger.error(f"❌ SheetsClient写入失败: {e}")
            raise

    async def import_service_data_to_target_table(self, source_spreadsheet_id: str,
                                                target_table_name: str, target_sheet_name: str = "インポート"):
        """
        从源数据表的"当月サービス提供"Sheet提取数据，导入到目标表格的指定工作表

        Args:
            source_spreadsheet_id: 源数据表ID（新创建的表格）
            target_table_name: 目标表格ID（配置中的sheet_id）
            target_sheet_name: 目标工作表名称（配置中的target_sheet_name）
        """
        try:
            logger.info(f"📊 开始从源表格提取数据并导入到目标表格")
            logger.info(f"   源表格: {source_spreadsheet_id}")
            logger.info(f"   目标表格: {target_table_name}")
            logger.info(f"   目标Sheet: {target_sheet_name}")

            # 1. 从源表格的"当月サービス提供"Sheet读取数据
            service_sheet_name = "当月サービス提供"
            service_data_range = f"{service_sheet_name}!A2:T"  # 基于RPA代码：A2:T范围

            logger.info(f"🔍 读取源数据: {service_data_range}")
            service_data = self.sheets_client.read_sheet(source_spreadsheet_id, service_data_range)

            if not service_data:
                logger.warning(f"⚠️ 源表格中没有找到数据: {service_data_range}")
                return

            logger.info(f"📋 读取到数据: {len(service_data)} 行")

            # 2. 清空目标表格的インポート表A4:T范围（基于RPA代码）
            target_clear_range = f"{target_sheet_name}!A4:T"
            logger.info(f"🧹 清空目标范围: {target_clear_range}")

            try:
                self.sheets_client.service.spreadsheets().values().clear(
                    spreadsheetId=target_table_name,
                    range=target_clear_range
                ).execute()
                logger.info(f"✅ 目标范围清空完成")
            except Exception as e:
                logger.error(f"❌ 清空目标范围失败: {e}")
                raise

            # 3. 写入数据到目标表格的インポート表A4位置（基于RPA代码）
            target_write_range = f"{target_sheet_name}!A4"
            logger.info(f"📝 写入数据到: {target_write_range}")

            result = self.sheets_client.write_to_sheet(
                spreadsheet_id=target_table_name,
                range_name=target_write_range,
                values=service_data
            )

            if result:
                logger.info(f"✅ 数据导入完成: {len(service_data)} 行数据已写入到 {target_table_name} 的 {target_sheet_name} 表")
            else:
                raise Exception("数据写入返回None")

        except Exception as e:
            logger.error(f"❌ 数据导入失败: {e}")
            raise
