import os
from dotenv import load_dotenv
from logger_config import logger
import google.auth
from google.oauth2 import service_account
from googleapiclient.discovery import build
from googleapiclient.http import MediaFileUpload
from googleapiclient.errors import HttpError

# .envファイルから環境変数を読み込む（ローカル開発用）
load_dotenv()

class DriveClient:
    """Google Driveとのやり取りを管理します。"""
    def __init__(self):
        """初期化時に認証情報を設定します。
        GCP環境とローカル環境で認証方法を自動的に切り替えます。"""
        credentials_path = os.getenv("GCP_SERVICE_ACCOUNT_PATH")
        
        try:
            # 🆕 使用更广泛的权限范围
            scopes = [
                'https://www.googleapis.com/auth/drive',
                'https://www.googleapis.com/auth/drive.file',
                'https://www.googleapis.com/auth/spreadsheets'
            ]

            if credentials_path and os.path.exists(credentials_path):
                # ローカル開発環境：.envに設定されたファイルパスから認証
                logger.info(f"ローカル環境として認証します。サービスアカウントファイル: {credentials_path}")
                self.creds = service_account.Credentials.from_service_account_file(
                    credentials_path, scopes=scopes)
                self.project_id = self.creds.project_id
            else:
                # GCP環境：ADC (Application Default Credentials) を使用して認証
                logger.info("GCP環境として認証します。Application Default Credentials (ADC) を使用します。")
                self.creds, self.project_id = google.auth.default(scopes=scopes)
            
            self.service = build('drive', 'v3', credentials=self.creds)
            logger.info("DriveClientが正常に初期化され、認証されました。")

        except Exception as e:
            logger.error(f"Google Driveクライアントの初期化中にエラーが発生しました: {e}", exc_info=True)
            raise

    def upload_file(self, file_path: str, folder_id: str) -> str:
        """指定されたフォルダにファイルをアップロードします。"""
        if not os.path.exists(file_path):
            logger.error(f"アップロード対象のファイルが見つかりません: {file_path}")
            return None

        logger.info(f"ファイル '{os.path.basename(file_path)}' を Drive フォルダ ID '{folder_id}' にアップロードしようとしています。")
        try:
            file_metadata = {
                'name': os.path.basename(file_path),
                'parents': [folder_id]
            }
            # 根据文件扩展名设置正确的MIME类型
            file_extension = os.path.splitext(file_path)[1].lower()
            
            if file_extension == '.csv':
                mimetype = 'text/csv'
            elif file_extension == '.xlsx':
                mimetype = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            elif file_extension == '.xls':
                mimetype = 'application/vnd.ms-excel'
            else:
                # 让Google Drive自动检测MIME类型
                mimetype = None
            
            if mimetype:
                media = MediaFileUpload(file_path, mimetype=mimetype, resumable=True)
            else:
                media = MediaFileUpload(file_path, resumable=True)
            
            file = self.service.files().create(
                body=file_metadata,
                media_body=media,
                fields='id',
                supportsAllDrives=True).execute()
            
            file_id = file.get('id')
            logger.info(f"ファイルが正常にアップロードされました。File ID: {file_id}")
            return file_id
        except HttpError as error:
            logger.error(f"Google Driveへのアップロード中にAPIエラーが発生しました: {error}")
            return None
        except Exception as e:
            logger.error(f"Google Driveへのアップロード中に予期せぬエラーが発生しました: {e}", exc_info=True)
            return None

    def move_file_to_folder(self, file_id: str, folder_id: str) -> bool:
        """指定されたファイルを指定されたフォルダに移動します。"""
        try:
            logger.info(f"ファイル {file_id} をフォルダ {folder_id} に移動しています...")

            # 現在のファイル情報を取得
            file = self.service.files().get(fileId=file_id, fields='parents').execute()
            previous_parents = ",".join(file.get('parents'))

            # ファイルを新しいフォルダに移動
            file = self.service.files().update(
                fileId=file_id,
                addParents=folder_id,
                removeParents=previous_parents,
                fields='id, parents'
            ).execute()

            logger.info(f"✅ ファイルの移動が完了しました: {file_id}")
            return True

        except HttpError as error:
            logger.error(f"❌ ファイル移動中にAPIエラーが発生しました: {error}")
            return False
        except Exception as e:
            logger.error(f"❌ ファイル移動中に予期せぬエラーが発生しました: {e}", exc_info=True)
            return False