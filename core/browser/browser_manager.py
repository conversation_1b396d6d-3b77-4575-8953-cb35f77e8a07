import asyncio
from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, Page
from logger_config import logger

class BrowserManager:
    """Playwrightブラウザのライフサイクルとセッションを管理するシングルトンクラス。"""
    _instance = None
    _browser: Browser = None
    _page: Page = None
    _playwright = None

    def __new__(cls, *args, **kwargs):
        if cls._instance is None:
            cls._instance = super(BrowserManager, cls).__new__(cls)
        return cls._instance

    async def start_browser(self, headless=True):
        """ブラウザを起動し、新しいページを作成します。改良版：既存リソースクリーンアップ"""
        try:
            # 既存のブラウザが存在するが接続が切れている場合はクリーンアップ
            if self._browser is not None and not self._browser.is_connected():
                logger.warning("既存のブラウザ接続が切れています。リソースをクリーンアップします。")
                self._browser = None
                self._page = None
                if self._playwright is not None:
                    try:
                        await self._playwright.stop()
                    except:
                        pass
                    self._playwright = None

            # 新しいブラウザを起動（まだ存在しない場合）
            if self._browser is None or not self._browser.is_connected():
                logger.info(f"ブラウザを起動します...(headless={headless})")
                self._playwright = await async_playwright().start()
                # Firefoxを使用（他のブラウザも選択可能: p.chromium, p.webkit）
                self._browser = await self._playwright.firefox.launch(headless=headless)
                self._page = await self._browser.new_page()
                logger.info("ブラウザが正常に起動しました。")

            return self._page
        except Exception as e:
            logger.error(f"ブラウザ起動中にエラーが発生しました: {e}")
            # エラー時はリソースをクリーンアップ
            self._browser = None
            self._page = None
            if self._playwright is not None:
                try:
                    await self._playwright.stop()
                except:
                    pass
                self._playwright = None
            raise

    async def get_page(self) -> Page:
        """現在アクティブなページオブジェクトを返します。改良版：接続状態チェック強化"""
        try:
            # ブラウザが利用できない場合
            if self._browser is None or not self._browser.is_connected():
                logger.warning("ブラウザが利用できません。新しいブラウザを起動します。")
                await self.start_browser()
                return self._page

            # ページが利用できない場合
            if self._page is None or self._page.is_closed():
                logger.warning("ページが利用できません。新しいページを作成します。")
                self._page = await self._browser.new_page()

            return self._page
        except Exception as e:
            logger.error(f"ページ取得中にエラーが発生しました: {e}")
            # エラー時は新しいブラウザとページを作成
            await self.start_browser()
            return self._page

    async def close_browser(self):
        """ブラウザを安全に閉じます。改良版：接続状態チェックとエラーハンドリング強化"""
        try:
            # ページを先に閉じる（存在する場合）
            if self._page is not None and not self._page.is_closed():
                try:
                    await self._page.close()
                    logger.info("ページを正常に閉じました。")
                except Exception as e:
                    logger.warning(f"ページの閉じる処理でエラー（無視）: {e}")
                finally:
                    self._page = None

            # ブラウザを閉じる（接続されている場合のみ）
            if self._browser is not None:
                try:
                    # 接続状態を確認してから閉じる
                    if self._browser.is_connected():
                        logger.info("ブラウザをシャットダウンします...")
                        await self._browser.close()
                        logger.info("ブラウザが正常にシャットダウンしました。")
                    else:
                        logger.info("ブラウザは既に切断されています。")
                except Exception as e:
                    # ブラウザ閉じる処理でエラーが発生した場合は警告レベルで記録
                    logger.warning(f"ブラウザの終了中にエラーが発生しました: {e}")
                finally:
                    self._browser = None

            # Playwrightインスタンスを停止
            if self._playwright is not None:
                try:
                    await self._playwright.stop()
                    logger.info("Playwrightインスタンスを停止しました。")
                except Exception as e:
                    logger.warning(f"Playwright停止中にエラー（無視）: {e}")
                finally:
                    self._playwright = None

        except Exception as e:
            # 予期しないエラーをキャッチ
            logger.error(f"ブラウザ終了処理で予期しないエラー: {e}")
        finally:
            # 確実にリセット
            self._browser = None
            self._page = None
            self._playwright = None
            logger.info("ブラウザリソースのクリーンアップが完了しました。")

    async def login(self, login_url: str, account: str, password: str):
        """指定されたサイトにログインします。autoroのロジックを参考に実装。"""
        page = await self.get_page()
        logger.info(f"サイトへのログインを開始します: {login_url}")
        
        try:
            # 1. ページに移動
            await page.goto(login_url, wait_until='networkidle', timeout=60000)
            logger.info(f"ページにアクセスしました: {login_url}")

            # 2. ユーザー名を入力
            username_selector = '#josso_username'
            await page.fill(username_selector, account)
            logger.info(f"ユーザー名を入力しました。")

            # 3. パスワードを入力
            password_selector = '#josso_password'
            await page.fill(password_selector, password)
            logger.info("パスワードを入力しました。")

            # 4. ログインボタンをクリックして、ナビゲーションが完了するのを待つ
            login_button_selector = '#form > form > div.submit-container.lastChild > input'
            logger.info("ログインボタンをクリックします...")

            # Playwright async API: 正确等待导航
            async with page.expect_navigation(wait_until='networkidle', timeout=60000):
                await page.click(login_button_selector)

            # ログイン成功の確認（次のページの要素が表示されるのを待つ）
            await page.wait_for_selector('.tab-content', timeout=30000)
            logger.info("ログインに成功し、ダッシュボードが正常に読み込まれました。")

        except Exception as e:
            logger.error(f"ログイン処理中にエラーが発生しました: {e}", exc_info=True)
            # デバッグ用にスクリーンショットを保存
            screenshot_path = 'logs/login_error.png'
            await page.screenshot(path=screenshot_path)
            logger.error(f"エラー発生時のスクリーンショットを {screenshot_path} に保存しました。")
            raise # エラーを再送出してワークフローを停止させる

# グローバルなブラウザマネージャーインスタンス
browser_manager = BrowserManager()