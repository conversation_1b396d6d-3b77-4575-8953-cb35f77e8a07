"""
浏览器连接检测和恢复机制
解决 "Connection closed while reading from the driver" 重复错误问题
"""

import asyncio
from typing import Optional
from logger_config import logger
from core.browser.browser_manager import browser_manager


class BrowserConnectionRecovery:
    """浏览器连接恢复管理器"""
    
    def __init__(self):
        self.max_recovery_attempts = 3
        self.recovery_delay = 5  # 秒
        self.connection_check_timeout = 3000  # 毫秒
        
    async def check_browser_connection(self, page) -> bool:
        """检查浏览器连接状态"""
        try:
            # 尝试执行一个简单的操作来检测连接
            await page.evaluate("() => document.readyState")
            return True
        except Exception as e:
            error_msg = str(e)
            if any(keyword in error_msg for keyword in [
                "Connection closed", 
                "Target page", 
                "Target closed", 
                "Browser has been closed"
            ]):
                logger.warning(f"⚠️ 浏览器连接已断开: {e}")
                return False
            else:
                # 其他类型的错误，可能是临时的
                logger.debug(f"🔍 连接检查遇到其他错误: {e}")
                return True
    
    async def recover_browser_connection(self, current_url: Optional[str] = None) -> bool:
        """恢复浏览器连接"""
        logger.info("🔄 开始恢复浏览器连接...")
        
        for attempt in range(self.max_recovery_attempts):
            try:
                logger.info(f"🔄 恢复尝试 {attempt + 1}/{self.max_recovery_attempts}")
                
                # 1. 关闭现有浏览器（如果还存在）
                try:
                    await browser_manager.close_browser()
                except:
                    pass  # 忽略关闭错误
                
                # 2. 等待一段时间
                await asyncio.sleep(self.recovery_delay)
                
                # 3. 重新启动浏览器
                await browser_manager.start_browser(headless=False)
                page = await browser_manager.get_page()
                
                # 4. 如果有URL，导航到该页面
                if current_url:
                    logger.info(f"🔄 导航到页面: {current_url}")
                    await page.goto(current_url, wait_until='domcontentloaded', timeout=30000)
                
                # 5. 验证连接
                if await self.check_browser_connection(page):
                    logger.info("✅ 浏览器连接恢复成功")
                    return True
                else:
                    logger.warning(f"⚠️ 恢复尝试 {attempt + 1} 失败，连接仍然无效")
                    
            except Exception as e:
                logger.error(f"❌ 恢复尝试 {attempt + 1} 失败: {e}")
                
                if attempt < self.max_recovery_attempts - 1:
                    await asyncio.sleep(self.recovery_delay * (attempt + 1))  # 递增延迟
        
        logger.error("❌ 所有浏览器连接恢复尝试都失败")
        return False
    
    async def safe_execute(self, operation, *args, **kwargs):
        """安全执行操作，自动处理连接断开"""
        max_retries = 2
        
        for retry in range(max_retries + 1):
            try:
                return await operation(*args, **kwargs)
                
            except Exception as e:
                error_msg = str(e)
                
                # 检查是否是连接错误
                if any(keyword in error_msg for keyword in [
                    "Connection closed", 
                    "Target page", 
                    "Target closed"
                ]):
                    if retry < max_retries:
                        logger.warning(f"⚠️ 连接错误，尝试恢复 (重试 {retry + 1}/{max_retries}): {e}")
                        
                        # 尝试恢复连接
                        if await self.recover_browser_connection():
                            continue  # 重试操作
                        else:
                            logger.error("❌ 连接恢复失败，停止重试")
                            break
                    else:
                        logger.error(f"❌ 达到最大重试次数，操作失败: {e}")
                        break
                else:
                    # 非连接错误，直接抛出
                    raise e
        
        # 如果到这里，说明所有重试都失败了
        raise Exception(f"操作失败，已尝试 {max_retries + 1} 次")


# 全局实例
connection_recovery = BrowserConnectionRecovery()


class ConnectionAwareOperation:
    """连接感知操作装饰器"""
    
    def __init__(self, recovery_manager: BrowserConnectionRecovery):
        self.recovery_manager = recovery_manager
    
    def __call__(self, func):
        async def wrapper(*args, **kwargs):
            return await self.recovery_manager.safe_execute(func, *args, **kwargs)
        return wrapper


# 装饰器实例
connection_aware = ConnectionAwareOperation(connection_recovery)


async def handle_connection_error(error: Exception, context: str = "") -> bool:
    """处理连接错误的通用函数"""
    error_msg = str(error)
    
    if any(keyword in error_msg for keyword in [
        "Connection closed", 
        "Target page", 
        "Target closed",
        "Browser has been closed"
    ]):
        logger.error(f"❌ 检测到连接错误 {context}: {error}")
        
        # 尝试恢复连接
        if await connection_recovery.recover_browser_connection():
            logger.info("✅ 连接已恢复，可以继续操作")
            return True
        else:
            logger.error("❌ 连接恢复失败，需要停止当前操作")
            return False
    else:
        # 不是连接错误，重新抛出
        raise error


def is_connection_error(error: Exception) -> bool:
    """判断是否是连接错误"""
    error_msg = str(error)
    return any(keyword in error_msg for keyword in [
        "Connection closed", 
        "Target page", 
        "Target closed",
        "Browser has been closed"
    ])


async def stop_infinite_loop_on_connection_error(error: Exception, context: str = "") -> bool:
    """检测并停止因连接错误导致的无限循环"""
    if is_connection_error(error):
        logger.error(f"🛑 检测到连接错误导致的潜在无限循环 {context}: {error}")
        logger.error("🛑 为避免资源浪费，停止当前操作")
        
        # 强制关闭浏览器以确保清理
        try:
            await browser_manager.close_browser()
        except:
            pass
            
        return True  # 表示应该停止循环
    
    return False  # 表示可以继续
