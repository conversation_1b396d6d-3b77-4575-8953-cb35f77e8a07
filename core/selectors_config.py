"""
选择器配置管理模块
集中管理所有工作流的CSS选择器，支持选择器优先级和备用选择器
基于autoro平台的实际工作流代码进行优化
"""

import os
import yaml
from typing import Dict, List, Optional
from dataclasses import dataclass, field
from logger_config import logger

@dataclass
class SelectorConfig:
    """选择器配置类"""
    primary: str = ""  # 主要选择器，默认为空字符串
    fallbacks: List[str] = field(default_factory=list)  # 备用选择器列表
    description: str = ""  # 选择器描述
    timeout: int = 10000  # 超时时间（毫秒）
    
    def __post_init__(self):
        if self.primary is None:
            self.primary = ""
        # fallbacks已由default_factory保证为list，无需再判断None

class SelectorsManager:
    """选择器管理器"""
    
    def __init__(self):
        self._selectors = self._load_selectors_from_yaml()
        # フォールバック: YAMLが読み込めない場合はハードコードされた設定を使用
        if not self._selectors:
            logger.warning("YAML設定の読み込みに失敗、ハードコードされた設定を使用します")
            self._selectors = self._initialize_selectors()

        # 🆕 学习统计
        self.learning_stats = {
            'total_learned': 0,
            'auto_saved': 0,
            'save_failed': 0,
            'learned_selectors': []
        }

    def _load_selectors_from_yaml(self) -> Dict[str, Dict[str, Dict[str, SelectorConfig]]]:
        """YAML設定ファイルから選択器を読み込む"""
        try:
            yaml_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'configs', 'selectors.yaml')

            if not os.path.exists(yaml_path):
                logger.warning(f"選択器設定ファイルが見つかりません: {yaml_path}")
                return {}

            with open(yaml_path, 'r', encoding='utf-8') as file:
                yaml_data = yaml.safe_load(file)

            if not yaml_data:
                logger.warning("YAML設定ファイルが空です")
                return {}

            # YAML構造をSelectorConfigオブジェクトに変換
            selectors = {}
            for workflow_name, workflow_data in yaml_data.items():
                if not isinstance(workflow_data, dict):
                    continue

                selectors[workflow_name] = {}
                for category_name, category_data in workflow_data.items():
                    if not isinstance(category_data, dict):
                        continue

                    selectors[workflow_name][category_name] = {}
                    for element_name, element_selectors in category_data.items():
                        if isinstance(element_selectors, list) and element_selectors:
                            # リストの最初の要素を主要選択器、残りを備用選択器として設定
                            primary = element_selectors[0]
                            fallbacks = element_selectors[1:] if len(element_selectors) > 1 else []

                            selectors[workflow_name][category_name][element_name] = SelectorConfig(
                                primary=primary,
                                fallbacks=fallbacks,
                                description=f"{workflow_name}.{category_name}.{element_name}",
                                timeout=10000
                            )
                        else:
                            logger.warning(f"無効な選択器設定: {workflow_name}.{category_name}.{element_name}")

            logger.info(f"YAML設定から{len(selectors)}個のワークフロー設定を読み込みました")
            return selectors

        except Exception as e:
            logger.error(f"YAML設定の読み込みに失敗しました: {e}")
            return {}

    def _initialize_selectors(self) -> Dict[str, Dict[str, Dict[str, SelectorConfig]]]:
        """初始化所有选择器配置"""
        return {
            # Kaipoke 工作流选择器
            "kaipoke": {
                # 登录相关选择器
                "login": {
                    "corporation_id": SelectorConfig(
                        primary='#form\\:corporation_id',
                        fallbacks=[
                            'input[name="corporationId"]',
                            'input[id*="corporation"]',
                            'input[placeholder*="法人"]',
                            'input[name="form:corporation_id"]'
                        ],
                        description="法人ID入力フィールド"
                    ),
                    "member_login_id": SelectorConfig(
                        primary='#form\\:member_login_id',
                        fallbacks=[
                            'input[name="memberLoginId"]',
                            'input[id*="member"]',
                            'input[placeholder*="メンバー"]',
                            'input[name="form:member_login_id"]'
                        ],
                        description="メンバーログインID入力フィールド"
                    ),
                    "password": SelectorConfig(
                        primary='#form\\:password',
                        fallbacks=[
                            'input[name="password"]',
                            'input[type="password"]',
                            'input[name="form:password"]'
                        ],
                        description="パスワード入力フィールド"
                    ),
                    "login_button": SelectorConfig(
                        primary='button[onclick*="doLogin"]',
                        fallbacks=[
                            '[onclick*="doLogin"]',
                            '#form\\:logn_nochklogin',
                            'input[type="submit"]',
                            'button[type="submit"]',
                            'input[value*="ログイン"]'
                        ],
                        description="ログインボタン"
                    )
                },
                # 月次実績管理関連選択器
                "monthly_report": {
                    "receipt_menu": SelectorConfig(
                        primary='.mainCtg li:nth-of-type(1) a',
                        fallbacks=[
                            'text=レセプト',
                            'a:contains("レセプト")',
                            '[href*="receipt"]'
                        ],
                        description="レセプトメニュー"
                    ),
                    "facility_selection": SelectorConfig(
                        primary='//a[contains(text(), "通所介護/4670106956")]',
                        fallbacks=[
                            'text=通所介護/4670106956',
                            'a:contains("通所介護")',
                            'a:contains("4670106956")'
                        ],
                        description="施設選択（通所介護/4670106956）"
                    ),
                    "info_output_hover": SelectorConfig(
                        primary='li:nth-of-type(7) img',
                        fallbacks=[
                            'text=各種情報出力',
                            'img[alt*="各種情報出力"]',
                            'li:contains("各種情報出力")'
                        ],
                        description="各種情報出力（マウスオーバー）"
                    ),
                    "output_target_selection": SelectorConfig(
                        primary='li:nth-of-type(7) li a',
                        fallbacks=[
                            'text=出力対象選択',
                            'a:contains("出力対象選択")',
                            '[href*="output"]'
                        ],
                        description="出力対象選択"
                    ),
                    "monthly_result_tip": SelectorConfig(
                        primary='#form\\:useMonthlyScheduleTip span',
                        fallbacks=[
                            'text=利用月間予定実績表',
                            'span:contains("利用月間予定実績表")',
                            '[id*="useMonthlyScheduleTip"] span'
                        ],
                        description="利用月間予定実績表"
                    ),
                    "service_offer_month": SelectorConfig(
                        primary='#form\\:serviceOfferYm',
                        fallbacks=[
                            'select[name*="serviceOfferYm"]',
                            'select[id*="serviceOfferYm"]',
                            'select[name*="month"]'
                        ],
                        description="サービス提供年月選択"
                    ),
                    "result_division": SelectorConfig(
                        primary='#form\\:planAchieveDivision\\:1',
                        fallbacks=[
                            'input[value="1"]',
                            'input[name*="planAchieveDivision"][value="1"]',
                            'radio[value="1"]'
                        ],
                        description="実績区分選択"
                    ),
                    "excel_export_button": SelectorConfig(
                        primary='#form\\:export img',
                        fallbacks=[
                            '#form\\:export',
                            'img[alt*="出力"]',
                            'input[value*="出力"]',
                            'text=出力する'
                        ],
                        description="Excel出力ボタン"
                    ),
                    "receipt_menu": SelectorConfig(
                        primary='.mainCtg li:nth-of-type(1) a',
                        fallbacks=[
                            'a:contains("レセプト")',
                            '[href*="receipt"]',
                            'nav li:first-child a'
                        ],
                        description="レセプトメニュー"
                    ),
                    "output_target_selection": SelectorConfig(
                        primary='li:nth-of-type(7) li a',
                        fallbacks=[
                            'a:contains("出力対象選択")',
                            '[href*="output"]',
                            'li:contains("出力") a'
                        ],
                        description="出力対象選択"
                    ),
                    "monthly_result_tip": SelectorConfig(
                        primary='#form\\:useMonthlyScheduleTip span',
                        fallbacks=[
                            '[id*="useMonthlyScheduleTip"] span',
                            'span:contains("月次実績")',
                            '[onclick*="monthly"] span'
                        ],
                        description="月次実績チップ"
                    ),
                    "result_division": SelectorConfig(
                        primary='#form\\:planAchieveDivision\\:1',
                        fallbacks=[
                            '[id*="planAchieveDivision"]:nth-of-type(2)',
                            'input[name*="planAchieveDivision"][value="1"]',
                            'input[type="radio"]:nth-of-type(2)'
                        ],
                        description="実績区分選択"
                    ),
                    "info_output_hover": SelectorConfig(
                        primary='li:nth-of-type(7) img',
                        fallbacks=[
                            'img[alt*="情報出力"]',
                            'li:contains("情報出力") img',
                            'li:nth-child(7) img'
                        ],
                        description="各種情報出力ホバー"
                    )
                }
            },
            
            # Kanamic 工作流选择器（基于autoro平台代码）
            "kanamic": {
                # 登录相关选择器
                "login": {
                    "username_field": SelectorConfig(
                        primary='#josso_username',
                        fallbacks=[
                            'input[name="josso_username"]',
                            'input[type="text"][name*="username"]',
                            'input[placeholder*="ユーザー"]'
                        ],
                        description="ユーザー名入力フィールド"
                    ),
                    "password_field": SelectorConfig(
                        primary='#josso_password',
                        fallbacks=[
                            'input[name="josso_password"]',
                            'input[type="password"]'
                        ],
                        description="パスワード入力フィールド"
                    ),
                    "login_button": SelectorConfig(
                        primary='#form > form > div.submit-container.lastChild > input',
                        fallbacks=[
                            'input[type="submit"]',
                            'button[type="submit"]',
                            '.submit-button',
                            'input[value*="ログイン"]',
                            '.login-btn'
                        ],
                        description="ログインボタン"
                    )
                },
                # CSV下载相关选择器（基于autoro代码）
                "csv_download": {
                    "menu_link_28": SelectorConfig(
                        primary='a:nth-of-type(28) .btn',
                        fallbacks=[
                            'a:nth-child(28) .btn',
                            '.btn:nth-of-type(28)'
                        ],
                        description="28番目のメニューリンク",
                        timeout=30000
                    ),
                    "button_b": SelectorConfig(
                        primary='.button-B',
                        fallbacks=[
                            'input.button-B',
                            'button.button-B',
                            '[class*="button-B"]'
                        ],
                        description="ボタンB",
                        timeout=30000
                    ),
                    "service_type_select": SelectorConfig(
                        primary='#servicetype',
                        fallbacks=[
                            'select[name="servicetype"]',
                            'select[id*="service"]'
                        ],
                        description="サービス種別選択（地域密着型介護福祉施設入所者生活介護）"
                    ),
                    "target_month_select": SelectorConfig(
                        primary='#targetMonth',
                        fallbacks=[
                            'select[name="targetMonth"]',
                            'select[id*="month"]'
                        ],
                        description="対象月選択"
                    ),
                    "search_button": SelectorConfig(
                        primary='.mb5 span',
                        fallbacks=[
                            'input[type="submit"]',
                            'button:contains("検索")',
                            '.search-btn'
                        ],
                        description="検索ボタン"
                    ),
                    "result_selection": SelectorConfig(
                        primary='.rowB p:nth-of-type(2) span',
                        fallbacks=[
                            '.rowB p:nth-child(2) span',
                            '.result-row span'
                        ],
                        description="結果選択"
                    ),
                    "download_button": SelectorConfig(
                        primary='#predlbtn',
                        fallbacks=[
                            'input[id="predlbtn"]',
                            'button[id="predlbtn"]',
                            '[onclick*="download"]'
                        ],
                        description="ダウンロードボタン"
                    )
                }
            }
        }
    
    def get_selector(self, workflow: str, category: str, element: str) -> Optional[SelectorConfig]:
        """指定されたワークフロー、カテゴリ、要素の選択器設定を取得"""
        try:
            return self._selectors[workflow][category][element]
        except KeyError:
            logger.warning(f"選択器が見つかりません: {workflow}.{category}.{element}")
            return None

    def learn_from_success(self, workflow: str, category: str, element: str, successful_selector: str):
        """从成功的选择器中学习，提高其优先级并自动保存到配置文件"""
        try:
            config = self._selectors[workflow][category][element]

            # 如果成功的选择器不是primary，则提升它
            if config.primary != successful_selector:
                # 从fallbacks中移除成功的选择器
                if successful_selector in config.fallbacks:
                    config.fallbacks.remove(successful_selector)

                # 将当前primary移到fallbacks的开头
                config.fallbacks.insert(0, config.primary)

                # 设置成功的选择器为新的primary
                config.primary = successful_selector

                logger.info(f"✅ 学习成功: {workflow}.{category}.{element} 的primary选择器更新为 {successful_selector}")

                # 🆕 更新学习统计
                self.learning_stats['total_learned'] += 1
                self.learning_stats['learned_selectors'].append({
                    'workflow': workflow,
                    'category': category,
                    'element': element,
                    'new_primary': successful_selector,
                    'timestamp': __import__('datetime').datetime.now().isoformat()
                })

                # 🆕 自动保存学习结果到YAML文件
                save_success = self._save_learned_selector_to_yaml(workflow, category, element, successful_selector)
                if save_success:
                    self.learning_stats['auto_saved'] += 1
                else:
                    self.learning_stats['save_failed'] += 1

        except KeyError:
            logger.warning(f"无法学习，选择器配置不存在: {workflow}.{category}.{element}")

    def get_learning_stats(self) -> dict:
        """获取学习统计信息"""
        return self.learning_stats.copy()

    def print_learning_summary(self):
        """打印学习总结"""
        stats = self.learning_stats
        logger.info("📊 选择器学习总结:")
        logger.info(f"   总学习次数: {stats['total_learned']}")
        logger.info(f"   自动保存成功: {stats['auto_saved']}")
        logger.info(f"   保存失败: {stats['save_failed']}")

        if stats['learned_selectors']:
            logger.info("   学习到的选择器:")
            for learned in stats['learned_selectors'][-5:]:  # 显示最近5个
                logger.info(f"     - {learned['workflow']}.{learned['category']}.{learned['element']} → {learned['new_primary']}")

        success_rate = (stats['auto_saved'] / stats['total_learned'] * 100) if stats['total_learned'] > 0 else 0
        logger.info(f"   自动保存成功率: {success_rate:.1f}%")

    def _save_learned_selector_to_yaml(self, workflow: str, category: str, element: str, successful_selector: str) -> bool:
        """将学习到的选择器自动保存到YAML配置文件，返回是否成功"""
        try:
            yaml_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'configs', 'selectors.yaml')

            if not os.path.exists(yaml_path):
                logger.warning(f"YAML配置文件不存在，无法保存学习结果: {yaml_path}")
                return

            # 读取当前YAML文件
            with open(yaml_path, 'r', encoding='utf-8') as file:
                yaml_data = yaml.safe_load(file)

            if not yaml_data or workflow not in yaml_data:
                logger.warning(f"YAML文件中未找到工作流配置: {workflow}")
                return

            # 更新YAML数据结构
            if category in yaml_data[workflow] and element in yaml_data[workflow][category]:
                current_selectors = yaml_data[workflow][category][element]

                if isinstance(current_selectors, list) and successful_selector in current_selectors:
                    # 移除成功的选择器从当前位置
                    current_selectors.remove(successful_selector)
                    # 将其插入到列表开头（成为主选择器）
                    current_selectors.insert(0, successful_selector)

                    # 保存更新后的YAML文件
                    with open(yaml_path, 'w', encoding='utf-8') as file:
                        yaml.dump(yaml_data, file, default_flow_style=False, allow_unicode=True, sort_keys=False)

                    logger.info(f"🔄 自动保存学习结果到配置文件: {workflow}.{category}.{element}")
                    logger.info(f"   新的主选择器: {successful_selector}")
                    return True
                else:
                    logger.warning(f"选择器格式不正确或选择器不在列表中: {workflow}.{category}.{element}")
                    return False
            else:
                logger.warning(f"YAML文件中未找到元素配置: {workflow}.{category}.{element}")
                return False

        except Exception as e:
            logger.error(f"❌ 保存学习结果到YAML文件失败: {e}")
            logger.info("💡 学习结果仍在内存中生效，但下次重启后会丢失")
            return False
    
    def get_all_selectors_for_element(self, workflow: str, category: str, element: str) -> List[str]:
        """指定された要素のすべての選択器（主要+備用）を取得"""
        selector_config = self.get_selector(workflow, category, element)
        if not selector_config:
            return []
        
        selectors = [selector_config.primary]
        selectors.extend(selector_config.fallbacks)
        return selectors
    
    def add_selector(self, workflow: str, category: str, element: str, config: SelectorConfig):
        """新しい選択器設定を追加"""
        if workflow not in self._selectors:
            self._selectors[workflow] = {}
        if category not in self._selectors[workflow]:
            self._selectors[workflow][category] = {}
        
        self._selectors[workflow][category][element] = config
        logger.info(f"選択器を追加しました: {workflow}.{category}.{element}")
    
    def update_selector_priority(self, workflow: str, category: str, element: str, 
                                working_selector: str):
        """動作する選択器を主要選択器として更新"""
        selector_config = self.get_selector(workflow, category, element)
        if not selector_config:
            return
        
        # 現在の主要選択器を備用リストに移動
        if selector_config.primary != working_selector:
            old_primary = selector_config.primary
            selector_config.primary = working_selector
            
            # 備用リストから動作する選択器を削除し、古い主要選択器を先頭に追加
            if working_selector in selector_config.fallbacks:
                selector_config.fallbacks.remove(working_selector)
            selector_config.fallbacks.insert(0, old_primary)
            
            logger.info(f"選択器の優先度を更新しました: {workflow}.{category}.{element} -> {working_selector}")

# グローバルな選択器管理インスタンス
selectors_manager = SelectorsManager()

# 便利な関数
def get_selector_config(workflow: str, category: str, element: str) -> Optional[SelectorConfig]:
    """選択器設定を取得する便利関数"""
    return selectors_manager.get_selector(workflow, category, element)

def get_all_selectors(workflow: str, category: str, element: str) -> List[str]:
    """すべての選択器を取得する便利関数"""
    return selectors_manager.get_all_selectors_for_element(workflow, category, element)

def update_working_selector(workflow: str, category: str, element: str, working_selector: str):
    """動作する選択器を更新する便利関数"""
    selectors_manager.update_selector_priority(workflow, category, element, working_selector)