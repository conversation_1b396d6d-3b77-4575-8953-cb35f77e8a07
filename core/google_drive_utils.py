
# core/google_drive_utils.py

import os
from google.oauth2 import service_account
from googleapiclient.discovery import build
from googleapiclient.http import MediaFileUpload
from logger_config import logger

class GoogleDriveUtils:
    def __init__(self):
        """
        Google Drive APIとの連携を処理するユーティリティクラス。
        環境変数 `GCP_SERVICE_ACCOUNT_PATH` からサービスアカウント情報を読み込みます。
        """
        self.creds = self._get_credentials()
        if self.creds:
            self.service = build('drive', 'v3', credentials=self.creds)
        else:
            self.service = None
            logger.error("Google Driveサービスを初期化できませんでした。認証情報を確認してください。")

    def _get_credentials(self):
        """
        環境変数からサービスアカウントのキーファイルのパスを取得し、認証情報を生成します。
        """
        key_path = os.getenv("GCP_SERVICE_ACCOUNT_PATH")
        if not key_path or not os.path.exists(key_path):
            logger.error(f"GCPサービスアカウントのキーファイルが見つかりません: {key_path}")
            return None
        
        try:
            # スコープを定義：ファイルの作成、変更、削除を許可
            scopes = ['https://www.googleapis.com/auth/drive']
            creds = service_account.Credentials.from_service_account_file(
                key_path, scopes=scopes)
            logger.info("Google Driveの認証に成功しました。")
            return creds
        except Exception as e:
            logger.error(f"サービスアカウントファイルからの認証情報生成に失敗しました: {e}")
            return None

    def upload_file(self, file_path: str, folder_id: str) -> str | None:
        """
        指定されたファイルをGoogle Driveの特定のフォルダにアップロードします。

        Args:
            file_path (str): アップロードするファイルのローカルパス。
            folder_id (str): アップロード先のGoogle DriveフォルダID。

        Returns:
            str | None: アップロードされたファイルのID。失敗した場合はNone。
        """
        if not self.service:
            logger.error("Google Driveサービスが利用できません。アップロードを中止します。")
            return None

        if not os.path.exists(file_path):
            logger.error(f"アップロード対象のファイルが見つかりません: {file_path}")
            return None

        file_name = os.path.basename(file_path)
        logger.info(f"ファイル「{file_name}」をGoogle DriveフォルダID「{folder_id}」にアップロードしています...")

        try:
            file_metadata = {
                'name': file_name,
                'parents': [folder_id]
            }
            media = MediaFileUpload(file_path, mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', resumable=True)
            
            request = self.service.files().create(
                media_body=media,
                body=file_metadata,
                fields='id'
            )
            
            response = None
            while response is None:
                status, response = request.next_chunk()
                if status:
                    logger.info(f"アップロード進捗: {int(status.progress() * 100)}%")
            
            file_id = response.get('id')
            logger.info(f"ファイル「{file_name}」のアップロードが成功しました。File ID: {file_id}")
            return file_id

        except Exception as e:
            logger.error(f"Google Driveへのアップロード中にエラーが発生しました: {e}", exc_info=True)
            return None
