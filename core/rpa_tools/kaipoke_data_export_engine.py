"""
Kaipoke Data Export Engine
基于RPA代码重构的数据导出核心引擎

核心功能：
1. 文件导出注册：选择据点、配置导出参数、注册导出任务
2. 状态检查：轮询导出状态，等待文件生成完成
3. 文件下载：下载成功的文件并保存到Google Drive
4. 多据点支持：支持19个据点的配置化处理

基于现有架构：
- 复用SelectorExecutor的MCP三层备份机制
- 集成KaipokeLoginService的登录管理
- 使用DriveClient的Google Drive集成
"""

import asyncio
import os
import re
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from playwright.async_api import Page

from logger_config import logger
from core.selector_executor import SelectorExecutor
from core.gsuite.drive_client import DriveClient
from core.rpa_tools.data_processor import FileManager


class KaipokeDataExportEngine:
    """卡イポケ数据导出核心引擎"""
    
    def __init__(self, page: Page, selector_executor: SelectorExecutor, config: Dict[str, Any]):
        self.page = page
        self.selector_executor = selector_executor
        self.config = config
        self.drive_client = DriveClient()
        self.file_manager = FileManager()
        
        # 初始化下载目录
        self.download_path = config.get('download_path', '/tmp/kaipoke_data_export')
        self.file_manager.ensure_download_directory(self.download_path)
        
    async def export_facility_data(self, facility_config: Dict[str, Any]) -> bool:
        """
        导出单个据点的数据
        
        Args:
            facility_config: 据点配置信息
            
        Returns:
            bool: 导出是否成功
        """
        try:
            facility_name = facility_config.get('facility_name')
            logger.info(f"🏢 开始处理据点: {facility_name}")
            
            # Step 1: 导航到数据连携页面
            if not await self._navigate_to_data_cooperation():
                logger.error(f"❌ 导航到数据连携页面失败: {facility_name}")
                return False
            
            # Step 2: 进入文件输出页面
            if not await self._enter_file_output_page():
                logger.error(f"❌ 进入文件输出页面失败: {facility_name}")
                return False
            
            # Step 3: 选择据点
            if not await self._select_facility(facility_config):
                logger.error(f"❌ 选择据点失败: {facility_name}")
                return False
            
            # Step 4: 配置导出类型
            if not await self._configure_export_type():
                logger.error(f"❌ 配置导出类型失败: {facility_name}")
                return False
            
            # Step 5: 设置日期并搜索
            if not await self._set_date_and_search():
                logger.error(f"❌ 设置日期并搜索失败: {facility_name}")
                return False
            
            # Step 6: 注册导出任务
            if not await self._register_export_task(facility_config):
                logger.error(f"❌ 注册导出任务失败: {facility_name}")
                return False
            
            logger.info(f"✅ 据点数据导出注册成功: {facility_name}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 据点数据导出失败: {facility_config.get('facility_name')}, 错误: {e}")
            return False
    
    async def download_exported_files(self, facilities: List[Dict[str, Any]]) -> Dict[str, bool]:
        """
        下载所有已导出的文件
        
        Args:
            facilities: 据点配置列表
            
        Returns:
            Dict[str, bool]: 各据点的下载结果
        """
        try:
            logger.info("📥 开始下载导出文件...")
            
            # 导航到输出管理页面
            if not await self._navigate_to_output_management():
                logger.error("❌ 导航到输出管理页面失败")
                return {}
            
            # 获取今日的日期字符串
            today_date = datetime.now().strftime('%m.%d')
            logger.info(f"📅 今日日期过滤: {today_date}")
            
            # 获取输出管理列表
            table_data = await self._extract_output_management_table()
            if not table_data:
                logger.error("❌ 获取输出管理列表失败")
                return {}
            
            # 处理下载
            download_results = {}
            for facility in facilities:
                facility_name = facility.get('facility_name')
                result = await self._download_facility_file(
                    facility, table_data, today_date
                )
                download_results[facility_name] = result
            
            return download_results
            
        except Exception as e:
            logger.error(f"❌ 下载导出文件失败: {e}")
            return {}
    
    async def _navigate_to_data_cooperation(self) -> bool:
        """导航到数据连携页面"""
        try:
            # 点击レセプト菜单
            if not await self.selector_executor.smart_click(
                workflow="kaipoke_data_export",
                category="navigation", 
                element="receipt_menu",
                target_text="レセプト"
            ):
                return False
            
            await self.page.wait_for_load_state("load")
            await asyncio.sleep(2)
            
            # 点击数据连携菜单
            if not await self.selector_executor.smart_click(
                workflow="kaipoke_data_export",
                category="navigation",
                element="data_cooperation_menu", 
                target_text="データ連携（他社ソフト）"
            ):
                return False
            
            await self.page.wait_for_load_state("load")
            logger.info("✅ 成功导航到数据连携页面")
            return True
            
        except Exception as e:
            logger.error(f"❌ 导航到数据连携页面失败: {e}")
            return False
    
    async def _enter_file_output_page(self) -> bool:
        """进入文件输出页面"""
        try:
            if not await self.selector_executor.smart_click(
                workflow="kaipoke_data_export",
                category="navigation",
                element="file_output_button",
                target_text="ファイル出力へ"
            ):
                return False
            
            await self.page.wait_for_load_state("load")
            logger.info("✅ 成功进入文件输出页面")
            return True
            
        except Exception as e:
            logger.error(f"❌ 进入文件输出页面失败: {e}")
            return False
    
    async def _select_facility(self, facility_config: Dict[str, Any]) -> bool:
        """选择据点"""
        try:
            facility_id = facility_config.get('facility_id', 'radio0')
            facility_name = facility_config.get('facility_name')
            
            # 使用动态选择器选择据点
            selector = f"#{facility_id}"
            
            try:
                await self.page.click(selector, timeout=10000)
                logger.info(f"✅ 成功选择据点: {facility_name} ({facility_id})")
                
                # 点击下一步
                if not await self.selector_executor.smart_click(
                    workflow="kaipoke_data_export",
                    category="facility_selection",
                    element="next_button",
                    target_text="次へ"
                ):
                    return False
                
                await self.page.wait_for_load_state("load")
                return True
                
            except Exception as e:
                logger.error(f"❌ 选择据点失败: {facility_name}, 错误: {e}")
                return False
            
        except Exception as e:
            logger.error(f"❌ 据点选择过程失败: {e}")
            return False
    
    async def _configure_export_type(self) -> bool:
        """配置导出类型（提供票実績）"""
        try:
            # 选择提供票実績复选框
            if not await self.selector_executor.smart_click(
                workflow="kaipoke_data_export",
                category="export_type",
                element="service_plan_checkbox"
            ):
                return False
            
            await asyncio.sleep(1)
            
            # 点击下一步
            if not await self.selector_executor.smart_click(
                workflow="kaipoke_data_export",
                category="export_type",
                element="next_button_step2",
                target_text="次へ"
            ):
                return False
            
            await self.page.wait_for_load_state("load")
            logger.info("✅ 成功配置导出类型")
            return True
            
        except Exception as e:
            logger.error(f"❌ 配置导出类型失败: {e}")
            return False
    
    async def _set_date_and_search(self) -> bool:
        """设置日期并搜索（前月）"""
        try:
            # 计算前月
            today = datetime.now()
            target_date = today.replace(day=1) - timedelta(days=1)
            target_year = str(target_date.year)
            target_month = target_date.strftime('%m月')
            
            logger.info(f"📅 设置目标日期: {target_year}年{target_month}")
            
            # 选择年份
            if not await self.selector_executor.smart_select_option(
                workflow="kaipoke_data_export",
                category="date_selection",
                element="year_select",
                text=target_year
            ):
                return False
            
            # 选择月份
            if not await self.selector_executor.smart_select_option(
                workflow="kaipoke_data_export", 
                category="date_selection",
                element="month_select",
                text=target_month
            ):
                return False
            
            # 点击搜索
            if not await self.selector_executor.smart_click(
                workflow="kaipoke_data_export",
                category="date_selection",
                element="search_button",
                target_text="検索する"
            ):
                return False
            
            await self.page.wait_for_load_state("load")
            await asyncio.sleep(3)  # 等待搜索结果加载
            
            logger.info("✅ 成功设置日期并搜索")
            return True
            
        except Exception as e:
            logger.error(f"❌ 设置日期并搜索失败: {e}")
            return False

    async def _register_export_task(self, facility_config: Dict[str, Any]) -> bool:
        """注册导出任务"""
        try:
            facility_name = facility_config.get('facility_name')

            # 查找目标据点行并选择
            if not await self._find_and_select_target_facility(facility_config):
                return False

            # 点击注册按钮
            if not await self.selector_executor.smart_click(
                workflow="kaipoke_data_export",
                category="export_registration",
                element="register_button",
                target_text="ファイル出力の登録"
            ):
                return False

            await self.page.wait_for_load_state("load")
            await asyncio.sleep(2)  # 等待注册处理完成

            logger.info(f"✅ 成功注册导出任务: {facility_name}")
            return True

        except Exception as e:
            logger.error(f"❌ 注册导出任务失败: {e}")
            return False

    async def _find_and_select_target_facility(self, facility_config: Dict[str, Any]) -> bool:
        """查找并选择目标据点行"""
        try:
            facility_name = facility_config.get('facility_name')
            target_facility = self.config.get('target_facility', 'あおぞら介護ステーション居宅介護支援事業所')

            # 获取表格数据
            table_selector = 'body > main > article > section > div.card.card-body.shadow-sm.rounded-0.my-3.p-5 > table'

            try:
                # 等待表格加载
                await self.page.wait_for_selector(table_selector, timeout=10000)

                # 提取表格数据
                table_data = await self.page.evaluate('''
                    () => {
                        const table = document.querySelector('body > main > article > section > div.card.card-body.shadow-sm.rounded-0.my-3.p-5 > table');
                        if (!table) return [];

                        const rows = Array.from(table.querySelectorAll('tr'));
                        return rows.map((row, index) => {
                            const cells = Array.from(row.querySelectorAll('td, th'));
                            return {
                                index: index,
                                cells: cells.map(cell => cell.textContent.trim())
                            };
                        });
                    }
                ''')

                # 查找目标据点行
                target_row_index = -1
                for row in table_data:
                    if len(row['cells']) > 3 and target_facility in row['cells'][3]:
                        target_row_index = row['index']
                        logger.info(f"✅ 找到目标据点行: {target_facility}, 行索引: {target_row_index}")
                        break

                if target_row_index == -1:
                    logger.error(f"❌ 未找到目标据点: {target_facility}")
                    return False

                # 选择对应的radio按钮
                radio_selector = f'#radio{target_row_index-1}'  # 减1因为表头占用一行
                await self.page.click(radio_selector, timeout=5000)
                logger.info(f"✅ 成功选择据点radio按钮: {radio_selector}")

                return True

            except Exception as e:
                logger.error(f"❌ 查找目标据点失败: {e}")
                return False

        except Exception as e:
            logger.error(f"❌ 查找并选择目标据点失败: {e}")
            return False

    async def _navigate_to_output_management(self) -> bool:
        """导航到输出管理页面"""
        try:
            # 返回到レセプト菜单
            if not await self.selector_executor.smart_click(
                workflow="kaipoke_data_export",
                category="navigation",
                element="receipt_menu",
                target_text="レセプト"
            ):
                return False

            await self.page.wait_for_load_state("load")
            await asyncio.sleep(2)

            # 点击数据连携菜单
            if not await self.selector_executor.smart_click(
                workflow="kaipoke_data_export",
                category="navigation",
                element="data_cooperation_menu",
                target_text="データ連携（他社ソフト）"
            ):
                return False

            await self.page.wait_for_load_state("load")
            await asyncio.sleep(1)

            # 点击输出管理菜单
            if not await self.selector_executor.smart_click(
                workflow="kaipoke_data_export",
                category="navigation",
                element="output_management_menu",
                target_text="出力管理"
            ):
                return False

            await self.page.wait_for_load_state("load")
            await asyncio.sleep(2)

            logger.info("✅ 成功导航到输出管理页面")
            return True

        except Exception as e:
            logger.error(f"❌ 导航到输出管理页面失败: {e}")
            return False

    async def _extract_output_management_table(self) -> List[List[str]]:
        """提取输出管理表格数据"""
        try:
            table_selector = 'body > main > article > section > div > table'

            # 等待表格加载
            await self.page.wait_for_selector(table_selector, timeout=10000)

            # 提取表格数据
            table_data = await self.page.evaluate('''
                () => {
                    const table = document.querySelector('body > main > article > section > div > table');
                    if (!table) return [];

                    const rows = Array.from(table.querySelectorAll('tr'));
                    return rows.map(row => {
                        const cells = Array.from(row.querySelectorAll('td, th'));
                        return cells.map(cell => cell.textContent.trim());
                    });
                }
            ''')

            logger.info(f"✅ 成功提取输出管理表格数据，共 {len(table_data)} 行")
            return table_data

        except Exception as e:
            logger.error(f"❌ 提取输出管理表格数据失败: {e}")
            return []

    async def _download_facility_file(self, facility: Dict[str, Any],
                                    table_data: List[List[str]],
                                    today_date: str) -> bool:
        """下载单个据点的文件"""
        try:
            facility_name = facility.get('facility_name')

            # 查找今日成功的文件
            target_row_index = -1
            for i, row in enumerate(table_data):
                if len(row) > 7:
                    # 检查状态是否为成功
                    if " 成功" in row[4]:
                        # 检查日期是否为今日
                        date_match = re.search(r'^\d{2}\.\d{2}', row[7])
                        if date_match and date_match.group() == today_date:
                            target_row_index = i
                            logger.info(f"✅ 找到今日成功文件: {facility_name}, 行 {i}")
                            break

            if target_row_index == -1:
                logger.warning(f"⚠️ 未找到今日成功文件: {facility_name}")
                return False

            # 点击下载链接
            download_link_selector = f'tr:nth-of-type({target_row_index}) .download-link'

            try:
                await self.page.click(download_link_selector, timeout=10000)
                await self.page.wait_for_load_state("load")
                await asyncio.sleep(1)

                # 点击输出按钮并等待下载
                async with self.page.expect_download(timeout=30000) as download_info:
                    if not await self.selector_executor.smart_click(
                        workflow="kaipoke_data_export",
                        category="download_management",
                        element="output_button",
                        target_text="出力する"
                    ):
                        return False

                # 处理下载文件
                download = await download_info.value
                filename = download.suggested_filename
                local_path = os.path.join(self.download_path, filename)

                await download.save_as(local_path)
                logger.info(f"✅ 文件下载成功: {filename}")

                # 上传到Google Drive
                gdrive_folder_id = self.config.get('gdrive_folder_id')
                if gdrive_folder_id:
                    file_id = self.drive_client.upload_file(local_path, gdrive_folder_id)
                    if file_id:
                        logger.info(f"✅ 文件上传到Google Drive成功: {file_id}")
                    else:
                        logger.warning(f"⚠️ 文件上传到Google Drive失败: {filename}")

                # 返回到列表页面
                await self.selector_executor.smart_click(
                    workflow="kaipoke_data_export",
                    category="download_management",
                    element="back_button",
                    target_text="一覧に戻る"
                )

                await self.page.wait_for_load_state("load")
                return True

            except Exception as e:
                logger.error(f"❌ 下载文件失败: {facility_name}, 错误: {e}")
                return False

        except Exception as e:
            logger.error(f"❌ 下载据点文件失败: {e}")
            return False
