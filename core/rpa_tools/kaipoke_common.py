"""
Kaipoke共通RPA工具
基于现有代码重构，提供统一的Kaipoke操作接口
"""
import os
import asyncio
from datetime import datetime, timedelta
from logger_config import logger
from core.selector_executor import SelectorExecutor


import jaconv

def get_previous_month_wareki():
   """
   获取上一个月的和历表示。
   例如，如果当前是2025年8月，则返回 "令和7年7月"。
   """
   today = datetime.today()
   # 计算上一个月
   first_day_of_current_month = today.replace(day=1)
   last_day_of_previous_month = first_day_of_current_month - timedelta(days=1)
   
   year = last_day_of_previous_month.year
   month = last_day_of_previous_month.month

   # 转换为和历
   era_year = year - 2018  # 令和元年是2019年
   era_name = "令和"
   
   # jaconv.to_jp_era(year, month, 1, format='%g%n年%m月') # This is a more robust way
   
   return f"{era_name}{era_year}年{month}月"


class KaipokeRPATools:
    """Kaipoke RPA操作的共通工具类"""
    
    def __init__(self, page, selector_executor: SelectorExecutor = None):
        self.page = page
        self.selector_executor = selector_executor or SelectorExecutor(page)
        self.current_account = None
    
    async def login_with_account_switch(self, corporation_id: str, member_login_id: str, password: str, login_url: str = None):
        """
        账号切换登录 - 基于现有的kaipoke_login_with_selectors函数重构
        如果当前账号与目标账号不同，会完全登出后重新登录
        """
        if login_url is None:
            login_url = "https://r.kaipoke.biz/kaipokebiz/login/COM020102.do"
        
        target_account = f"{corporation_id}_{member_login_id}"
        
        # 检查是否需要切换账号
        if self.current_account and self.current_account != target_account:
            logger.info(f"账号切换: {self.current_account} -> {target_account}")
            await self._complete_logout()
        
        # 执行登录
        login_success = await self._login_with_selectors(login_url, corporation_id, member_login_id, password)
        
        if login_success:
            self.current_account = target_account
            logger.info(f"✅ 登录成功: {target_account}")
        else:
            logger.error(f"❌ 登录失败: {target_account}")
            
        return login_success
    
    async def _complete_logout(self):
        """完全登出 - 基于现有代码"""
        try:
            logger.info("执行完全登出...")
            
            # 移动到登出页面
            await self.page.goto("https://r.kaipoke.biz/kaipokebiz/login/COM020102.do", wait_until='networkidle')
            logger.info("移动到登出页面")
            
            # 清除会话
            await self.page.context.clear_cookies()
            await self.page.context.clear_permissions()
            logger.info("清除会话和Cookie")
            
            # 等待
            await self.page.wait_for_timeout(2000)
            
            self.current_account = None
            
        except Exception as e:
            logger.warning(f"登出处理出错: {e}")
    
    async def _login_with_selectors(self, login_url: str, corporation_id: str, member_login_id: str, password: str):
        """
        使用选择器登录 - 基于现有的kaipoke_login_with_selectors函数
        """
        logger.info("执行Kaipoke登录（选择器优先）...")

        try:
            # 移动到登录页面
            await self.page.goto(login_url, wait_until='networkidle', timeout=60000)
            logger.info(f"访问登录页面: {login_url}")

            # 页面内容确认
            await self.page.wait_for_timeout(2000)

            # 使用选择器执行器登录
            login_success = await self.selector_executor.execute_kaipoke_login(
                corporation_id, member_login_id, password
            )
            
            if login_success:
                logger.info("✅ Kaipoke登录完成（选择器优先）")
                return True
            else:
                logger.warning("⚠️ 选择器登录失败，执行Agent后备方案")
                # Agent fallback
                await self._call_agent_for_login(corporation_id, member_login_id, password)
                return True

        except Exception as e:
            logger.error(f"Kaipoke登录出错: {e}", exc_info=True)
            # 保存错误截图
            try:
                screenshot_path = '/tmp/kaipoke_login_error.png'
                await self.page.screenshot(path=screenshot_path)
                logger.error(f"错误截图保存至: {screenshot_path}")
            except:
                pass
            
            # Agent fallback
            logger.info("🤖 错误后执行Agent后备方案")
            await self._call_agent_for_login(corporation_id, member_login_id, password)
            return True
    
    async def _call_agent_for_login(self, corporation_id: str, member_login_id: str, password: str):
        """
        选择器失效时调用Agent进行登录 - 基于现有的call_agent_for_kaipoke_login函数
        """
        logger.info("选择器失效，尝试基础登录方法")

        try:
            # 使用基础的Playwright操作进行登录
            await self.page.fill('#form\\:logn_nochklogin', corporation_id)
            await self.page.fill('#form\\:logn_nochkloginid', member_login_id)
            await self.page.fill('#form\\:logn_nochkpassword', password)
            await self.page.click('#form\\:logn_nochkloginbtn')

            # 等待登录完成
            await self.page.wait_for_timeout(3000)

            logger.info("✅ 基础登录方法完成")

        except Exception as e:
            logger.error(f"❌ 基础登录方法失败: {e}")
            # 如果需要Agent支持，可以在这里添加动态导入
            logger.warning("⚠️ 登录失败，请检查凭据或网络连接")
    
    async def download_monthly_report_with_selectors(self, params: dict, download_path: str):
        """
        使用选择器下载月度报告 - 基于现有的download_kaipoke_monthly_report_with_selectors函数
        """
        service_center_name = params.get('service_center_name')
        element_text = params.get('element_text')
        output_filename_pattern = params.get('output_filename_pattern')

        logger.info(f"下载月度实绩报告（选择器优先）: {service_center_name}")

        try:
            # 移动到主页
            await self.page.goto("https://r.kaipoke.biz/kaipokebiz/login/COM020102.do", wait_until='networkidle')

            # 使用选择器执行器执行下载处理
            downloaded_file = await self.selector_executor.execute_kaipoke_monthly_report_download(
                element_text, output_filename_pattern, service_center_name, download_path
            )
            
            if downloaded_file:
                logger.info(f"✅ 文件下载成功（选择器优先）: {downloaded_file}")
                return downloaded_file
            else:
                logger.warning("⚠️ 选择器下载失败")
                return None

        except Exception as e:
            logger.error(f"月度实绩报告下载出错: {e}", exc_info=True)
            return None
    
    def get_current_account(self):
        """获取当前登录账号"""
        return self.current_account
    
    @staticmethod
    def group_tasks_by_account(tasks: list, default_corporation_id: str, default_member_login_id: str, default_password: str):
        """
        按账号分组任务 - 基于现有逻辑重构
        """
        tasks_by_account = {}
        default_account_key = f"{default_corporation_id}_{default_member_login_id}"
        
        for task_config in tasks:
            params = task_config.get('params', {})
            
            # 检查是否有特殊登录信息
            task_corporation_id_env = params.get('corporation_id_env')
            task_member_login_id_env = params.get('member_login_id_env')
            task_password_env = params.get('password_env')
            
            if task_corporation_id_env and task_member_login_id_env and task_password_env:
                task_corporation_id = os.getenv(task_corporation_id_env)
                task_member_login_id = os.getenv(task_member_login_id_env)
                task_password = os.getenv(task_password_env)
                
                if task_corporation_id and task_member_login_id and task_password:
                    account_key = f"{task_corporation_id}_{task_member_login_id}"
                    if account_key not in tasks_by_account:
                        tasks_by_account[account_key] = {
                            'corporation_id': task_corporation_id,
                            'member_login_id': task_member_login_id,
                            'password': task_password,
                            'tasks': []
                        }
                    tasks_by_account[account_key]['tasks'].append(task_config)
                else:
                    # 环境变量未设置，使用默认账号
                    if default_account_key not in tasks_by_account:
                        tasks_by_account[default_account_key] = {
                            'corporation_id': default_corporation_id,
                            'member_login_id': default_member_login_id,
                            'password': default_password,
                            'tasks': []
                        }
                    tasks_by_account[default_account_key]['tasks'].append(task_config)
            else:
                # 使用默认账号
                if default_account_key not in tasks_by_account:
                    tasks_by_account[default_account_key] = {
                        'corporation_id': default_corporation_id,
                        'member_login_id': default_member_login_id,
                        'password': default_password,
                        'tasks': []
                    }
                tasks_by_account[default_account_key]['tasks'].append(task_config)
        
        logger.info(f"任务分为 {len(tasks_by_account)} 个账号组")
        return tasks_by_account


class KaipokeAccountManager:
    """Kaipoke账号管理器 - 支持多据点配置"""
    
    def __init__(self):
        self.accounts = {}
        self._load_accounts_from_env()
    
    def _load_accounts_from_env(self):
        """从环境变量加载账号配置"""
        # 默认账号
        default_corp_id = os.getenv('KAIPOKE_CORPORATION_ID')
        default_member_id = os.getenv('KAIPOKE_MEMBER_LOGIN_ID') 
        default_password = os.getenv('KAIPOKE_PASSWORD')
        
        if default_corp_id and default_member_id and default_password:
            self.accounts['default'] = {
                'corporation_id': default_corp_id,
                'member_login_id': default_member_id,
                'password': default_password,
                'name': '默认账号'
            }
        
        # 永吉据点账号
        nagayoshi_corp_id = os.getenv('KAIPOKE_NAGAYOSHI_CORPORATION_ID')
        nagayoshi_member_id = os.getenv('KAIPOKE_NAGAYOSHI_MEMBER_LOGIN_ID')
        nagayoshi_password = os.getenv('KAIPOKE_NAGAYOSHI_PASSWORD')
        
        if nagayoshi_corp_id and nagayoshi_member_id and nagayoshi_password:
            self.accounts['nagayoshi'] = {
                'corporation_id': nagayoshi_corp_id,
                'member_login_id': nagayoshi_member_id,
                'password': nagayoshi_password,
                'name': '永吉据点'
            }
        
        # 东千石据点账号
        higashisengoku_corp_id = os.getenv('KAIPOKE_HIGASHISENGOKU_CORPORATION_ID')
        higashisengoku_member_id = os.getenv('KAIPOKE_HIGASHISENGOKU_MEMBER_LOGIN_ID')
        higashisengoku_password = os.getenv('KAIPOKE_HIGASHISENGOKU_PASSWORD')
        
        if higashisengoku_corp_id and higashisengoku_member_id and higashisengoku_password:
            self.accounts['higashisengoku'] = {
                'corporation_id': higashisengoku_corp_id,
                'member_login_id': higashisengoku_member_id,
                'password': higashisengoku_password,
                'name': '东千石据点'
            }
        
        logger.info(f"加载了 {len(self.accounts)} 个Kaipoke账号配置")
    
    def get_account(self, account_key: str = 'default'):
        """获取指定账号信息"""
        return self.accounts.get(account_key)
    
    def get_all_accounts(self):
        """获取所有账号信息"""
        return self.accounts
    
    def get_account_for_task(self, task_config: dict):
        """根据任务配置获取对应账号"""
        params = task_config.get('params', {})
        
        # 检查是否指定了特殊账号
        corp_id_env = params.get('corporation_id_env')
        member_id_env = params.get('member_login_id_env')
        password_env = params.get('password_env')
        
        if corp_id_env and member_id_env and password_env:
            corp_id = os.getenv(corp_id_env)
            member_id = os.getenv(member_id_env)
            password = os.getenv(password_env)
            
            if corp_id and member_id and password:
                return {
                    'corporation_id': corp_id,
                    'member_login_id': member_id,
                    'password': password,
                    'name': f'任务指定账号 ({corp_id})'
                }
        
        # 返回默认账号
        return self.get_account('default')