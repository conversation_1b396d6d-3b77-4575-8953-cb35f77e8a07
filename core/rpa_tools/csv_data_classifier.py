"""
CSV数据智能分类器
基于RPA代码的数据分类逻辑，实现账单数据的智能分类

核心功能：
1. 根据月份字段分类数据（当月 vs 前月以前）
2. 生成当月服务提供数据
3. 批量写入风险控制
"""

import pandas as pd
from datetime import datetime, timedelta
from logger_config import logger
import re


class CSVDataClassifier:
    """CSV数据智能分类器"""
    
    def __init__(self):
        self.current_month = datetime.now().strftime('%Y年%m月')
        self.target_month = self._get_target_month()
        self.target_month_reiwa = self._convert_to_reiwa(self.target_month)
        logger.info(f"📅 数据分类器初始化: 当前月={self.current_month}, 目标月={self.target_month}, 令和={self.target_month_reiwa}")
    
    def _get_target_month(self):
        """获取目标月份（基于RPA代码的Target_Month逻辑）"""
        # 获取前一个月
        today = datetime.now()
        first_day_current_month = today.replace(day=1)
        last_month = first_day_current_month - timedelta(days=1)
        return last_month.strftime('%Y年%m月')

    def _convert_to_reiwa(self, western_date):
        """将西历转换为令和年号格式"""
        try:
            # 解析西历格式 "YYYY年MM月"
            year_str = western_date.split('年')[0]
            month_str = western_date.split('年')[1].replace('月', '')

            year = int(year_str)
            month = int(month_str)

            # 令和元年是2019年，所以令和年 = 西历年 - 2018
            reiwa_year = year - 2018

            # 格式化为令和年号（去掉月份前导零）
            return f"令和{reiwa_year}年{month}月"

        except Exception as e:
            logger.error(f"❌ 令和年号转换失败: {e}")
            return western_date
    
    def classify_billing_data(self, df: pd.DataFrame):
        """
        分类账单数据
        基于用户需求的分类逻辑

        Returns:
            dict: {
                'original_data': DataFrame,          # 当月请求（原始数据）
                'current_month_service': DataFrame,  # 当月服务提供（目标月数据）
                'previous_month_billing': DataFrame  # 前月以前请求（目标月以外数据）
            }
        """
        try:
            logger.info(f"🔄 开始分类账单数据，总行数: {len(df)}")

            # 查找月份相关的列
            month_column = self._find_month_column(df)
            if not month_column:
                logger.warning("⚠️ 未找到月份列，使用默认分类")
                return self._default_classification(df)

            logger.info(f"📋 使用月份列进行分类: {month_column}")

            # 修正后的分类逻辑：
            # 1. 原始数据作为"当月请求"
            # 2. 目标月数据作为"当月服务提供"
            # 3. 目标月以外数据作为"前月以前请求"
            target_month_mask = self._create_target_month_mask(df, month_column)

            # 原始数据（当月请求）
            original_data = df.copy()

            # 当月服务提供数据（目标月数据）
            current_month_service = df[target_month_mask].copy()

            # 前月以前请求数据（目标月以外数据）
            previous_month_billing = df[~target_month_mask].copy()

            logger.info(f"📊 数据分类完成:")
            logger.info(f"   当月请求（原始数据）: {len(original_data)} 行")
            logger.info(f"   当月服务提供（目标月数据）: {len(current_month_service)} 行")
            logger.info(f"   前月以前请求（其他月数据）: {len(previous_month_billing)} 行")

            return {
                'original_data': original_data,
                'current_month_service': current_month_service,
                'previous_month_billing': previous_month_billing
            }

        except Exception as e:
            logger.error(f"❌ 数据分类失败: {e}")
            return self._default_classification(df)
    
    def _find_month_column(self, df: pd.DataFrame):
        """查找包含月份信息的列"""
        # 🆕 调整优先级：サービス提供年月 优先于 請求年月
        possible_columns = ['サービス提供年月', '提供年月', '請求年月', '年月', '利用年月']

        for col in possible_columns:
            if col in df.columns:
                logger.info(f"✅ 找到月份列: {col}")
                return col

        # 如果没有找到标准列名，搜索包含"月"的列（排除金额相关列）
        for col in df.columns:
            if '月' in str(col) and '金額' not in str(col) and '負担' not in str(col):
                logger.info(f"✅ 找到可能的月份列: {col}")
                return col

        logger.warning("⚠️ 未找到月份相关列")
        return None

    def _create_target_month_mask(self, df: pd.DataFrame, month_column: str):
        """创建目标月份匹配掩码，支持多种格式"""
        try:
            # 尝试多种匹配模式
            patterns_to_try = [
                self.target_month,           # 2025年06月
                self.target_month_reiwa,     # 令和7年6月
                self.target_month.replace('年', '/').replace('月', ''),  # 2025/06
                self.target_month.replace('年', '-').replace('月', ''),  # 2025-06
                self.target_month.replace('0', ''),  # 去掉前导零：2025年6月
            ]

            # 如果是令和格式，也尝试带前导零的版本
            if '令和' in self.target_month_reiwa:
                reiwa_with_zero = self.target_month_reiwa.replace('年6月', '年06月')
                patterns_to_try.append(reiwa_with_zero)

            logger.info(f"🔍 尝试匹配模式: {patterns_to_try}")

            # 逐个尝试匹配模式
            for pattern in patterns_to_try:
                mask = df[month_column].astype(str).str.contains(pattern, na=False)
                match_count = mask.sum()
                logger.info(f"   模式 '{pattern}': {match_count} 行匹配")

                if match_count > 0:
                    logger.info(f"✅ 使用匹配模式: {pattern}")
                    return mask

            # 如果所有模式都不匹配，返回空掩码
            logger.warning("⚠️ 所有匹配模式都失败，返回空掩码")
            return pd.Series([False] * len(df), index=df.index)

        except Exception as e:
            logger.error(f"❌ 创建目标月份掩码失败: {e}")
            return pd.Series([False] * len(df), index=df.index)
    
    def _generate_service_provision_data(self, billing_data: pd.DataFrame):
        """
        生成当月服务提供数据
        基于RPA代码的逻辑，从当月请求数据生成服务提供数据
        """
        try:
            if billing_data.empty:
                return pd.DataFrame()
            
            # 复制当月请求数据作为服务提供数据的基础
            service_data = billing_data.copy()
            
            # 可以在这里添加特定的服务提供数据处理逻辑
            # 例如：修改特定列的值、添加服务提供相关字段等
            
            logger.info(f"📋 生成当月服务提供数据: {len(service_data)} 行")
            return service_data
            
        except Exception as e:
            logger.error(f"❌ 生成服务提供数据失败: {e}")
            return pd.DataFrame()
    
    def _default_classification(self, df: pd.DataFrame):
        """默认分类（当无法识别月份时）"""
        logger.warning("⚠️ 使用默认分类策略")

        # 修正后的默认分类策略
        # 1. 原始数据作为"当月请求"
        # 2. 前半部分作为"当月服务提供"
        # 3. 后半部分作为"前月以前请求"
        mid_point = len(df) // 2

        original_data = df.copy()
        current_month_service = df.iloc[:mid_point].copy()
        previous_month_billing = df.iloc[mid_point:].copy()

        return {
            'original_data': original_data,
            'current_month_service': current_month_service,
            'previous_month_billing': previous_month_billing
        }
    
    def prepare_data_for_batch_write(self, classified_data: dict, batch_size: int = 1000):
        """
        准备数据进行批量写入
        🆕 批量写入风险控制：将大数据集分批处理
        
        Args:
            classified_data: 分类后的数据字典
            batch_size: 每批写入的行数
            
        Returns:
            dict: 分批后的数据结构
        """
        try:
            logger.info(f"🔄 准备批量写入数据，批次大小: {batch_size}")
            
            batched_data = {}
            
            for data_type, df in classified_data.items():
                if df.empty:
                    batched_data[data_type] = []
                    continue
                
                # 将DataFrame分批
                batches = []
                for i in range(0, len(df), batch_size):
                    batch = df.iloc[i:i+batch_size]
                    batches.append({
                        'data': batch.values.tolist(),
                        'start_row': i + 2,  # +2 因为第1行是表头，从第2行开始数据
                        'batch_index': len(batches),
                        'total_batches': (len(df) + batch_size - 1) // batch_size
                    })
                
                batched_data[data_type] = batches
                logger.info(f"📦 {data_type}: {len(df)} 行数据分为 {len(batches)} 批")
            
            return batched_data
            
        except Exception as e:
            logger.error(f"❌ 准备批量写入数据失败: {e}")
            raise
    
    def get_sheet_headers(self, df: pd.DataFrame):
        """获取Sheet表头"""
        if df.empty:
            return []
        return [list(df.columns)]
    
    def validate_data_integrity(self, original_df: pd.DataFrame, classified_data: dict):
        """
        验证数据完整性
        确保分类后的数据总数与原始数据一致
        """
        try:
            original_count = len(original_df)
            classified_count = (
                len(classified_data.get('current_month_service', pd.DataFrame())) +
                len(classified_data.get('previous_month_billing', pd.DataFrame()))
            )

            if original_count == classified_count:
                logger.info(f"✅ 数据完整性验证通过: 原始{original_count}行 = 分类后{classified_count}行")
                return True
            else:
                logger.warning(f"⚠️ 数据完整性验证失败: 原始{original_count}行 ≠ 分类后{classified_count}行")
                return False

        except Exception as e:
            logger.error(f"❌ 数据完整性验证异常: {e}")
            return False
