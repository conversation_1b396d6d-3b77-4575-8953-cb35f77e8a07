import time
from logger_config import logger

class TennkiPerformanceMonitor:
    """
    Tennki性能监控器（修复版 - 补全缺失方法）
    - 记录处理开始/结束时间
    - 统计用时、输出日志
    - 支持用户切换、处理成功/失败统计
    """

    def __init__(self):
        self.start_time = None
        self.end_time = None
        self.records_processed = 0
        # 🆕 补全缺失的统计字段
        self.records_failed = 0
        self.user_switches = 0
        self.processing_times = []
        self.error_log = []

    def start(self):
        """开始计时"""
        self.start_time = time.time()

    def end(self):
        """结束计时"""
        self.end_time = time.time()

    def log_record_processed(self):
        """记录处理的记录数（兼容旧方法）"""
        self.records_processed += 1

    def record_processed(self):
        """🆕 记录处理成功的记录"""
        self.records_processed += 1
        logger.debug(f"📊 记录处理成功，总计: {self.records_processed}")

    def record_failed(self):
        """🆕 记录处理失败的记录"""
        self.records_failed += 1
        logger.debug(f"📊 记录处理失败，总计: {self.records_failed}")

    def record_user_switch(self):
        """🆕 记录用户切换次数"""
        self.user_switches += 1
        logger.debug(f"📊 用户切换，总计: {self.user_switches}")

    def record_processing_time(self, duration: float):
        """🆕 记录单个处理时间"""
        self.processing_times.append(duration)

    def record_error(self, error_message: str, context: str = ""):
        """🆕 记录错误信息"""
        error_entry = {
            'timestamp': time.time(),
            'message': error_message,
            'context': context
        }
        self.error_log.append(error_entry)

    def get_summary(self):
        """获取性能摘要"""
        duration = (self.end_time - self.start_time) if self.start_time and self.end_time else None

        # 计算成功率
        total_records = self.records_processed + self.records_failed
        success_rate = (self.records_processed / total_records * 100) if total_records > 0 else 0

        # 计算平均处理时间
        avg_processing_time = sum(self.processing_times) / len(self.processing_times) if self.processing_times else 0

        return {
            "records_processed": self.records_processed,
            "records_failed": self.records_failed,
            "total_records": total_records,
            "success_rate": success_rate,
            "user_switches": self.user_switches,
            "duration_seconds": duration,
            "average_processing_time": avg_processing_time,
            "error_count": len(self.error_log)
        }

    def log_final_report(self):
        """🆕 输出最终性能报告"""
        summary = self.get_summary()

        logger.info("=" * 60)
        logger.info("📊 Tennki性能监控报告")
        logger.info("=" * 60)
        logger.info(f"⏱️ 总执行时间: {summary['duration_seconds']:.2f} 秒")
        logger.info(f"✅ 处理成功: {summary['records_processed']} 条记录")
        logger.info(f"❌ 处理失败: {summary['records_failed']} 条记录")
        logger.info(f"📈 成功率: {summary['success_rate']:.1f}%")
        logger.info(f"🔄 用户切换: {summary['user_switches']} 次")
        logger.info(f"⚡ 平均处理时间: {summary['average_processing_time']:.2f} 秒/条")
        logger.info(f"🚨 错误数量: {summary['error_count']} 个")
        logger.info("=" * 60)