"""
数据处理共通工具
基于现有的data_tools.py重构，提供统一的数据处理接口
"""
import os
import csv
import glob
import pandas as pd
from datetime import datetime, timedelta
from logger_config import logger


class DataProcessor:
    """数据处理共通工具类"""
    
    @staticmethod
    def process_csv_to_excel(download_path: str, file_name_template: str, 
                           original_encoding: str = 'cp932', target_encoding: str = 'UTF-8'):
        """
        处理下载的CSV文件 - 基于现有的process_downloaded_csv函数重构
        """
        try:
            logger.info(f"开始CSV处理。下载路径: {download_path}")

            list_of_files = glob.glob(f'{download_path}/*.csv')
            if not list_of_files:
                raise FileNotFoundError(f"下载路径中未找到CSV文件: {download_path}")
            latest_file = max(list_of_files, key=os.path.getctime)
            logger.info(f"发现最新文件: {latest_file}")

            # CSV手动解析（对应复杂结构）
            try:
                # 首先读取原始数据
                with open(latest_file, 'r', encoding=original_encoding) as f:
                    lines = f.readlines()

                logger.info(f"从CSV文件读取了{len(lines)}行")

                # 找到实际数据行（第8行以后是数据）
                data_lines = []
                for i, line in enumerate(lines):
                    if i >= 7:  # 第8行以后（0基准所以7以后）
                        # 跳过空行
                        if line.strip():
                            data_lines.append(line.strip())

                logger.info(f"数据行数: {len(data_lines)}")

                if not data_lines:
                    raise ValueError("未找到数据行")

                # 解析CSV数据
                import csv
                from io import StringIO

                # 将数据行作为CSV解析
                csv_data = []
                csv_reader = csv.reader(data_lines)
                for row in csv_reader:
                    csv_data.append(row)

                # 转换为DataFrame
                df = pd.DataFrame(csv_data)
                logger.info(f"CSV文件正常读取。形状: {df.shape}")

            except Exception as e:
                logger.error(f"手动CSV解析失败: {e}")
                # 后备方案：尝试标准方法
                try:
                    df = pd.read_csv(latest_file, encoding=original_encoding, header=None,
                                   sep=None, engine='python', on_bad_lines='skip')
                    logger.warning("使用了后备解析")
                except Exception as e2:
                    logger.error(f"后备解析也失败: {e2}")
                    raise

            # 模板变量处理
            template_vars = DataProcessor._generate_template_vars(df)
            
            # 解析模板变量
            import re
            placeholders = re.findall(r'\{(.+?)\}', file_name_template)
            for placeholder in placeholders:
                if placeholder.startswith('cell_'):
                    try:
                        # 例: cell_B2 -> row=1, col=1
                        col_str = placeholder[5:6]
                        row_str = placeholder[6:]
                        row_idx = int(row_str) - 1
                        col_idx = ord(col_str.upper()) - ord('A')
                        if row_idx < len(df) and col_idx < len(df.columns):
                            template_vars[placeholder] = df.iat[row_idx, col_idx]
                        else:
                            template_vars[placeholder] = ''  # 单元格不存在时为空字符
                    except (ValueError, IndexError) as e:
                        logger.warning(f"模板变量 '{placeholder}' 解析失败: {e}")
                        template_vars[placeholder] = ''
                elif placeholder not in template_vars:
                     template_vars[placeholder] = ''  # 其他未定义变量为空字符

            new_filename_xlsx = file_name_template.format(**template_vars)
            new_filepath_xlsx = os.path.join(download_path, new_filename_xlsx)

            df.to_excel(new_filepath_xlsx, index=False, header=False)
            logger.info(f"保存为XLSX: {new_filepath_xlsx}")

            os.remove(latest_file)
            logger.info(f"删除了原CSV文件 {latest_file}")

            return new_filepath_xlsx

        except Exception as e:
            logger.error(f"CSV处理中发生错误: {e}", exc_info=True)
            return f"错误: CSV文件处理失败。原因: {e}"
    
    @staticmethod
    def _generate_template_vars(df=None):
        """生成模板变量"""
        template_vars = {
            'datetime': datetime.now().strftime('%Y-%m-%d_%H%M'),
            'year': datetime.now().year,
            'month': datetime.now().month
        }
        
        # 添加令和年月格式
        today = datetime.now()
        last_month = today.replace(day=1) - timedelta(days=1)
        reiwa_year = last_month.year - 2018  # 2019年是令和元年
        template_vars['reiwa_month'] = f"令和{reiwa_year}年{last_month.month:02d}月"
        
        return template_vars
    
    @staticmethod
    def generate_filename_with_month(pattern: str, service_center_name: str = None):
        """
        生成带月份的文件名 - 基于现有逻辑
        """
        today = datetime.now()
        last_month = today.replace(day=1) - timedelta(days=1)
        
        # 令和年计算
        reiwa_year = last_month.year - 2018  # 2019年是令和元年
        month_str = f"令和{reiwa_year}年{last_month.month:02d}月"
        
        template_vars = {
            'month': month_str,
            'service_center_name': service_center_name or '',
            'datetime': datetime.now().strftime('%Y-%m-%d_%H%M'),
            'year': last_month.year,
            'month_num': last_month.month
        }
        
        if pattern:
            return pattern.format(**template_vars)
        else:
            return f"{month_str}{service_center_name}.xlsx" if service_center_name else f"{month_str}.xlsx"


class FileManager:
    """文件管理工具"""
    
    @staticmethod
    def ensure_download_directory(download_path: str):
        """确保下载目录存在"""
        os.makedirs(download_path, exist_ok=True)
        logger.info(f"下载目录已准备: {download_path}")
    
    @staticmethod
    def get_latest_file(directory: str, extension: str = '.xlsx'):
        """获取目录中最新的指定扩展名文件"""
        try:
            files = glob.glob(f'{directory}/*{extension}')
            if not files:
                return None
            return max(files, key=os.path.getctime)
        except Exception as e:
            logger.error(f"获取最新文件失败: {e}")
            return None
    
    @staticmethod
    def cleanup_old_files(directory: str, keep_count: int = 5):
        """清理旧文件，保留指定数量的最新文件"""
        try:
            files = glob.glob(f'{directory}/*')
            if len(files) <= keep_count:
                return
            
            # 按修改时间排序
            files.sort(key=os.path.getctime, reverse=True)
            
            # 删除多余的文件
            for file_to_delete in files[keep_count:]:
                os.remove(file_to_delete)
                logger.info(f"删除旧文件: {file_to_delete}")
                
        except Exception as e:
            logger.error(f"清理旧文件失败: {e}")


class ExcelProcessor:
    """Excel文件处理工具"""
    
    @staticmethod
    def read_excel_data(file_path: str, sheet_name: str = None):
        """读取Excel数据"""
        try:
            if sheet_name:
                df = pd.read_excel(file_path, sheet_name=sheet_name)
            else:
                df = pd.read_excel(file_path)
            logger.info(f"Excel文件读取成功: {file_path}, 形状: {df.shape}")
            return df
        except Exception as e:
            logger.error(f"Excel文件读取失败: {e}")
            return None
    
    @staticmethod
    def filter_data_by_column(df: pd.DataFrame, column_name: str, filter_value: str):
        """按列值过滤数据"""
        try:
            filtered_df = df[df[column_name] == filter_value]
            logger.info(f"数据过滤完成: {column_name}={filter_value}, 结果行数: {len(filtered_df)}")
            return filtered_df
        except Exception as e:
            logger.error(f"数据过滤失败: {e}")
            return df
    
    @staticmethod
    def save_to_excel(df: pd.DataFrame, file_path: str, sheet_name: str = 'Sheet1'):
        """保存数据到Excel文件"""
        try:
            df.to_excel(file_path, sheet_name=sheet_name, index=False)
            logger.info(f"Excel文件保存成功: {file_path}")
            return True
        except Exception as e:
            logger.error(f"Excel文件保存失败: {e}")
            return False