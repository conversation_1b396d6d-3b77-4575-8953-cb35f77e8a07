"""
Google Workspace集成工具
基于现有的gsuite工具重构，提供统一的Google服务接口
"""
import os
from logger_config import logger
from core.gsuite.drive_client import DriveClient
from core.gsuite.sheets_client import SheetsClient


class GoogleWorkspaceManager:
    """Google Workspace管理器"""
    
    def __init__(self):
        self.drive_client = DriveClient()
        self.sheets_client = SheetsClient()
    
    def upload_to_drive(self, file_path: str, folder_id: str = None, new_filename: str = None):
        """
        上传文件到Google Drive
        """
        try:
            if not os.path.exists(file_path):
                logger.error(f"文件不存在: {file_path}")
                return False
            
            # 使用新文件名或原文件名
            filename = new_filename or os.path.basename(file_path)
            
            result = self.drive_client.upload_file(file_path, folder_id, filename)
            
            if result:
                logger.info(f"✅ 文件上传到Google Drive成功: {filename}")
                return True
            else:
                logger.error(f"❌ 文件上传到Google Drive失败: {filename}")
                return False
                
        except Exception as e:
            logger.error(f"Google Drive上传出错: {e}", exc_info=True)
            return False
    
    def update_sheet_data(self, sheet_id: str, data_rules: list, source_file: str = None):
        """
        更新Google Sheets数据
        
        Args:
            sheet_id: Google Sheets ID
            data_rules: 数据处理规则列表
            source_file: 源文件路径（可选）
        """
        try:
            for rule in data_rules:
                target_sheet_id = rule.get('target_sheet_id')
                filter_config = rule.get('filter_by')
                header_row = rule.get('header_row', 1)
                paste_start_row = rule.get('paste_start_row', 2)
                pre_paste_updates = rule.get('pre_paste_updates', [])
                
                if not target_sheet_id:
                    logger.warning("数据规则中缺少target_sheet_id，跳过")
                    continue
                
                logger.info(f"处理Google Sheets更新: {target_sheet_id}")
                
                # 执行预更新操作
                for update in pre_paste_updates:
                    cell = update.get('cell')
                    value_source = update.get('value_source')
                    source_details = update.get('source_details')
                    
                    if cell and value_source:
                        if value_source == 'csv_column' and source_details and source_file:
                            # 从CSV文件中提取值
                            value = self._extract_value_from_csv(source_file, source_details)
                        else:
                            value = source_details or ''
                        
                        # 更新单元格
                        self.sheets_client.update_cell(target_sheet_id, cell, value)
                        logger.info(f"更新单元格 {cell}: {value}")
                
                # 如果有数据文件和过滤配置，处理数据粘贴
                if source_file and filter_config:
                    self._process_filtered_data_paste(
                        source_file, target_sheet_id, filter_config, 
                        header_row, paste_start_row
                    )
                
            logger.info("✅ Google Sheets数据更新完成")
            return True
            
        except Exception as e:
            logger.error(f"Google Sheets数据更新出错: {e}", exc_info=True)
            return False
    
    def _extract_value_from_csv(self, csv_file: str, column_name: str):
        """从CSV文件中提取指定列的值"""
        try:
            import pandas as pd
            df = pd.read_csv(csv_file, encoding='cp932')
            
            if column_name in df.columns and len(df) > 0:
                return str(df[column_name].iloc[0])
            else:
                logger.warning(f"CSV文件中未找到列 '{column_name}' 或数据为空")
                return ''
                
        except Exception as e:
            logger.error(f"从CSV提取值失败: {e}")
            return ''
    
    def _process_filtered_data_paste(self, source_file: str, sheet_id: str, 
                                   filter_config: dict, header_row: int, paste_start_row: int):
        """处理过滤数据粘贴"""
        try:
            import pandas as pd
            
            # 读取源文件
            if source_file.endswith('.csv'):
                df = pd.read_csv(source_file, encoding='cp932')
            else:
                df = pd.read_excel(source_file)
            
            # 应用过滤器
            filter_column = filter_config.get('column')
            filter_value = filter_config.get('value')
            
            if filter_column and filter_value:
                filtered_df = df[df[filter_column] == filter_value]
                logger.info(f"数据过滤: {filter_column}={filter_value}, 结果行数: {len(filtered_df)}")
            else:
                filtered_df = df
            
            # 转换为Google Sheets格式并粘贴
            if not filtered_df.empty:
                # 这里需要调用sheets_client的方法来粘贴数据
                # 具体实现取决于SheetsClient的API
                logger.info(f"准备粘贴 {len(filtered_df)} 行数据到 {sheet_id}")
                # self.sheets_client.paste_data(sheet_id, filtered_df, paste_start_row)
            
        except Exception as e:
            logger.error(f"过滤数据粘贴失败: {e}")


class DriveUploadManager:
    """Drive上传管理器"""
    
    def __init__(self, default_folder_id: str = None):
        self.drive_client = DriveClient()
        self.default_folder_id = default_folder_id
    
    def upload_with_retry(self, file_path: str, folder_id: str = None, max_retries: int = 3):
        """带重试的文件上传"""
        folder_id = folder_id or self.default_folder_id
        
        for attempt in range(max_retries):
            try:
                result = self.drive_client.upload_file(file_path, folder_id)
                if result:
                    logger.info(f"✅ 文件上传成功 (尝试 {attempt + 1}): {os.path.basename(file_path)}")
                    return True
                else:
                    logger.warning(f"⚠️ 文件上传失败 (尝试 {attempt + 1}): {os.path.basename(file_path)}")
                    
            except Exception as e:
                logger.error(f"❌ 文件上传出错 (尝试 {attempt + 1}): {e}")
            
            if attempt < max_retries - 1:
                logger.info(f"等待后重试...")
                import time
                time.sleep(2 ** attempt)  # 指数退避
        
        logger.error(f"❌ 文件上传最终失败: {os.path.basename(file_path)}")
        return False
    
    def batch_upload(self, file_paths: list, folder_id: str = None):
        """批量上传文件"""
        folder_id = folder_id or self.default_folder_id
        results = []
        
        for file_path in file_paths:
            if os.path.exists(file_path):
                result = self.upload_with_retry(file_path, folder_id)
                results.append({
                    'file': file_path,
                    'success': result
                })
            else:
                logger.warning(f"文件不存在，跳过: {file_path}")
                results.append({
                    'file': file_path,
                    'success': False
                })
        
        success_count = sum(1 for r in results if r['success'])
        logger.info(f"批量上传完成: {success_count}/{len(results)} 成功")
        
        return results


class SheetsDataManager:
    """Sheets数据管理器"""
    
    def __init__(self):
        self.sheets_client = SheetsClient()
    
    def update_monthly_report_data(self, sheet_configs: list, source_data_file: str):
        """更新月度报告数据到多个表格"""
        results = []
        
        for config in sheet_configs:
            try:
                sheet_id = config.get('sheet_id')
                sheet_name = config.get('sheet_name', 'Sheet1')
                data_range = config.get('range', 'A1')
                
                if not sheet_id:
                    logger.warning("配置中缺少sheet_id，跳过")
                    continue
                
                # 读取源数据
                import pandas as pd
                if source_data_file.endswith('.csv'):
                    df = pd.read_csv(source_data_file, encoding='cp932')
                else:
                    df = pd.read_excel(source_data_file)
                
                # 更新表格数据
                # 这里需要根据实际的SheetsClient API来实现
                logger.info(f"更新表格 {sheet_id} 的数据")
                
                results.append({
                    'sheet_id': sheet_id,
                    'success': True
                })
                
            except Exception as e:
                logger.error(f"更新表格数据失败: {e}")
                results.append({
                    'sheet_id': config.get('sheet_id'),
                    'success': False,
                    'error': str(e)
                })
        
        return results