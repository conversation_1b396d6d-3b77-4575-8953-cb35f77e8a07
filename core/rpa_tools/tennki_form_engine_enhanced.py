"""
Tennki表单引擎增强方法
专门解决职员情报数据丢失和页面保护问题

核心增强：
1. 职员情报填写时的数据保护
2. 智能字段激活机制
3. 增强的错误恢复
4. 页面状态监控
"""

import asyncio
from logger_config import logger
from core.rpa_tools.tennki_form_data_protection import TennkiFormDataProtector


class TennkiFormEngineEnhanced:
    """Tennki表单引擎增强功能"""

    def __init__(self, form_engine):
        self.form_engine = form_engine
        self.selector_executor = form_engine.selector_executor
        self.page = form_engine.selector_executor.page
        self.data_protector = None

    async def initialize_data_protector(self):
        """初始化数据保护器"""
        if not self.data_protector:
            self.data_protector = TennkiFormDataProtector(self.page)
            logger.debug("🛡️ 数据保护器已初始化")

    async def enhanced_fill_staff_info(self, row):
        """🆕 增强版职员信息填写（带数据保护）"""
        try:
            logger.debug("👨‍⚕️ 开始增强版职员信息填写...")
            
            # 1. 初始化数据保护器
            await self.initialize_data_protector()
            
            # 2. 使用数据保护模式执行职员信息填写
            async def staff_fill_operation():
                return await self._protected_staff_info_fill(row)
            
            result = await self.data_protector.protect_during_field_activation(staff_fill_operation)
            
            # 3. 验证数据完整性
            integrity_ok = await self.data_protector.verify_data_integrity()
            if not integrity_ok:
                logger.warning("⚠️ 检测到数据丢失，尝试恢复...")
                await self.data_protector.restore_form_data()
            
            logger.debug("✅ 增强版职员信息填写完成")
            return result
            
        except Exception as e:
            logger.error(f"❌ 增强版职员信息填写失败: {e}")
            raise

    async def _protected_staff_info_fill(self, row):
        """受保护的职员信息填写核心逻辑"""
        page = self.page
        filled_count = 0
        
        try:
            # 1. 确保职员情报字段已激活
            await self._ensure_staff_fields_activated(page, row)
            
            # 2. 智能填写职员职种
            staff_qualification = self._extract_staff_qualification(row)
            if staff_qualification:
                job_label = self._map_staff_qualification_to_job(staff_qualification)
                if job_label:
                    logger.debug(f"👨‍⚕️ 填写职员职种: {staff_qualification} → {job_label}")
                    
                    # 使用智能填写方法
                    success = await self.data_protector.smart_field_fill(
                        '#chargeStaff1JobDivision1', 
                        job_label, 
                        'select'
                    )
                    
                    if success:
                        logger.debug(f"✅ 职员职种填写成功: {job_label}")
                        filled_count += 1
                    else:
                        logger.warning(f"⚠️ 职员职种填写失败: {job_label}")

            # 3. 智能填写职员姓名
            staff_name = self._extract_staff_name(row)
            if staff_name:
                logger.debug(f"👨‍⚕️ 填写职员姓名: {staff_name}")
                
                success = await self.data_protector.smart_field_fill(
                    '#chargeStaff1Id1', 
                    staff_name, 
                    'select'
                )
                
                if success:
                    logger.debug(f"✅ 职员姓名填写成功: {staff_name}")
                    filled_count += 1
                else:
                    logger.warning(f"⚠️ 职员姓名填写失败: {staff_name}")
                    # 尝试部分匹配
                    await self._try_partial_staff_name_match(staff_name)

            logger.debug(f"✅ 受保护的职员信息填写完成: 成功填写 {filled_count} 个字段")
            return True
            
        except Exception as e:
            logger.error(f"❌ 受保护的职员信息填写失败: {e}")
            raise

    async def _ensure_staff_fields_activated(self, page, row):
        """确保职员情报字段已激活"""
        try:
            logger.debug("🔧 确保职员情报字段已激活...")
            
            # 1. 检查职员字段是否已激活
            is_activated = await page.evaluate("""
                () => {
                    const staffButton = document.querySelector('#input_staff_on > input');
                    return staffButton && !staffButton.disabled;
                }
            """)
            
            if is_activated:
                logger.debug("✅ 职员情报字段已激活")
                return True
            
            # 2. 如果未激活，执行激活流程
            logger.debug("🔄 职员情报字段未激活，开始激活流程...")
            
            # 2.1 选择实绩以激活职员情报字段
            await page.click('#inPopupPlanAchievementsDivision02')
            await page.wait_for_timeout(1000)
            logger.debug("✅ 已选择实绩")
            
            # 2.2 选择实施日（如果需要）
            await self._smart_select_service_date(page, row)
            
            # 2.3 验证激活状态
            is_activated_after = await page.evaluate("""
                () => {
                    const staffButton = document.querySelector('#input_staff_on > input');
                    return staffButton && !staffButton.disabled;
                }
            """)
            
            if not is_activated_after:
                logger.warning("⚠️ 职员情报字段激活失败，尝试手动触发...")
                await page.evaluate("if (window.switchPlanAct) window.switchPlanAct();")
                await page.wait_for_timeout(500)
            
            # 2.4 点击職員情報入力
            await self._safe_click_staff_input_button(page)
            
            logger.debug("✅ 职员情报字段激活流程完成")
            return True
            
        except Exception as e:
            logger.error(f"❌ 职员情报字段激活失败: {e}")
            raise

    async def _smart_select_service_date(self, page, row):
        """智能选择实施日"""
        try:
            logger.debug("📅 智能选择实施日...")
            
            # 获取实施日数据
            date_data = self._extract_service_date(row)
            if not date_data:
                logger.debug("ℹ️ 没有实施日数据，跳过日期选择")
                return
            
            logger.debug(f"📅 实施日数据: {date_data}")
            
            # 解析日期
            if isinstance(date_data, str) and date_data.strip():
                try:
                    # 尝试解析日期格式
                    day = int(date_data.strip())
                    if 1 <= day <= 31:
                        logger.debug(f"📅 选择日期: {day}日")
                        
                        # 使用JavaScript选择日期
                        success = await page.evaluate(f"""
                            (day) => {{
                                try {{
                                    // 查找对应的日期元素
                                    const dayElements = document.querySelectorAll('#simple-select-days-range a');
                                    for (let element of dayElements) {{
                                        if (element.textContent.trim() === day.toString()) {{
                                            element.click();
                                            return true;
                                        }}
                                    }}
                                    return false;
                                }} catch (error) {{
                                    console.error('日期选择错误:', error);
                                    return false;
                                }}
                            }}
                        """, day)
                        
                        if success:
                            logger.debug(f"✅ 实施日选择成功: {day}日")
                            await page.wait_for_timeout(1000)  # 等待日期选择生效
                        else:
                            logger.warning(f"⚠️ 实施日选择失败: {day}日")
                    else:
                        logger.warning(f"⚠️ 无效的日期值: {day}")
                except ValueError:
                    logger.warning(f"⚠️ 无法解析日期: {date_data}")
            
        except Exception as e:
            logger.warning(f"⚠️ 智能选择实施日失败: {e}")

    async def _safe_click_staff_input_button(self, page):
        """安全点击職員情報入力按钮"""
        try:
            logger.debug("🖱️ 安全点击職員情報入力按钮...")
            
            # 1. 强制清除可能的弹窗干扰
            await page.evaluate("""
                () => {
                    // 移除Karte组件
                    const karteElements = document.querySelectorAll('[id*="karte"], [class*="karte"]');
                    karteElements.forEach(el => el.remove());
                    
                    // 移除可能的遮罩层
                    const overlays = document.querySelectorAll('.modal-backdrop, .overlay, [style*="z-index"]');
                    overlays.forEach(overlay => {
                        if (overlay.style.zIndex > 1000) {
                            overlay.remove();
                        }
                    });
                }
            """)
            
            # 2. 使用JavaScript直接点击
            click_success = await page.evaluate("""
                () => {
                    const staffButton = document.querySelector('#input_staff_on > input');
                    if (staffButton && !staffButton.disabled) {
                        staffButton.click();
                        return true;
                    }
                    return false;
                }
            """)
            
            if click_success:
                logger.debug("✅ 職員情報入力按钮点击成功")
                await page.wait_for_timeout(1000)  # 等待表单打开
            else:
                logger.warning("⚠️ 職員情報入力按钮点击失败")
                
        except Exception as e:
            logger.warning(f"⚠️ 安全点击職員情報入力按钮失败: {e}")

    async def _try_partial_staff_name_match(self, staff_name):
        """尝试部分匹配职员姓名"""
        try:
            logger.debug(f"🔍 尝试部分匹配职员姓名: {staff_name}")
            
            # 获取可用的职员选项
            staff_options = await self.page.evaluate("""
                () => {
                    const select = document.querySelector('#chargeStaff1Id1');
                    if (!select) return [];
                    return Array.from(select.options).map(option => ({
                        value: option.value,
                        text: option.text
                    })).filter(opt => opt.text && opt.text !== '-');
                }
            """)
            
            logger.debug(f"📋 可用职员选项: {[opt['text'] for opt in staff_options]}")
            
            # 尝试找到包含职员姓名的选项
            for option in staff_options:
                option_text = option['text']
                if staff_name in option_text or option_text in staff_name:
                    logger.debug(f"🎯 尝试部分匹配: {option_text}")
                    
                    success = await self.data_protector.smart_field_fill(
                        '#chargeStaff1Id1', 
                        option_text, 
                        'select'
                    )
                    
                    if success:
                        logger.debug(f"✅ 职员姓名部分匹配成功: {option_text}")
                        return True
            
            logger.warning(f"⚠️ 未找到匹配的职员: {staff_name}")
            return False
            
        except Exception as e:
            logger.warning(f"⚠️ 部分匹配职员姓名失败: {e}")
            return False

    def _extract_staff_qualification(self, row):
        """提取职员资格"""
        try:
            # 根据表格结构提取职员资格（通常在特定列）
            if isinstance(row, dict):
                return row.get('staff_qualification', '')
            elif isinstance(row, list) and len(row) > 20:
                # 假设职员资格在第21列（索引20）
                return row[20] if row[20] else ''
            return ''
        except:
            return ''

    def _extract_staff_name(self, row):
        """提取职员姓名"""
        try:
            # 根据表格结构提取职员姓名（X列，第24列，索引23）
            if isinstance(row, dict):
                return row.get('staff_name', '')
            elif isinstance(row, list) and len(row) > 23:
                return row[23] if row[23] else ''
            return ''
        except:
            return ''

    def _extract_service_date(self, row):
        """提取实施日"""
        try:
            if isinstance(row, dict):
                return row.get('service_date', '')
            elif isinstance(row, list) and len(row) > 0:
                # 实施日通常在第一列或特定列
                return row[0] if row[0] else ''
            return ''
        except:
            return ''

    def _map_staff_qualification_to_job(self, staff_qualification):
        """映射职员资格到职种"""
        mapping = {
            "正看護師": "看護師",
            "准看護師": "准看護師", 
            "理学療法士": "理学療法士",
            "作業療法士": "作業療法士",
            "言語聴覚士": "言語聴覚士",
            "社会福祉士": "社会福祉士",
            "介護福祉士": "介護福祉士",
            "ケアマネジャー": "ケアマネジャー"
        }
        return mapping.get(staff_qualification, staff_qualification)
