"""
Kaipoke Tennki 保险类型配置模块

定义三种保险类型的字段映射和处理配置：
- 介護保险 (Kaigo)
- 医疗保险 (Iryou) 
- 自费保险 (<PERSON>hi)

创建时间: 2025-07-24
作者: Aozora自动化工作流
"""

from enum import Enum
from typing import Dict, List, Optional
from dataclasses import dataclass


class InsuranceType(Enum):
    """保险类型枚举"""
    KAIGO = "介護"           # 介護保险
    IRYOU = "医療"           # 医疗保险
    SEISHIN_IRYOU = "精神医療"  # 精神医疗保险
    JIHI = "自費"            # 自费保险


@dataclass
class FieldMapping:
    """字段映射配置"""
    selector: str
    label: Optional[str] = None
    value: Optional[str] = None
    required: bool = True
    description: str = ""


@dataclass
class InsuranceConfig:
    """保险配置类"""
    insurance_type: InsuranceType
    selector: str
    display_name: str
    field_mappings: Dict[str, FieldMapping]
    required_fields: List[str]
    processing_order: List[str]


class InsuranceFieldMappings:
    """保险字段映射配置类"""
    
    @staticmethod
    def get_kaigo_config() -> InsuranceConfig:
        """获取介護保险配置"""
        return InsuranceConfig(
            insurance_type=InsuranceType.KAIGO,
            selector="#inPopupInsuranceDivision01",
            display_name="介護保险",
            field_mappings={
                "service_kind": FieldMapping(
                    selector="#inPopupServiceKindId",
                    description="サービス種類",
                    required=True
                ),
                "service_content": FieldMapping(
                    selector="#inPopupServiceContent_row > td:nth-child(2) > div",
                    description="サービス内容",
                    required=False
                ),
                "unit_count": FieldMapping(
                    selector="#inPopupUnit",
                    description="単位数",
                    required=False
                ),
                "estimate1": FieldMapping(
                    selector="#inPopupEstimate1",
                    description="基本算定",
                    required=True
                ),
                "estimate2": FieldMapping(
                    selector="#inPopupEstimate2",
                    description="職員資格等",
                    required=False
                ),
                "estimate3": FieldMapping(
                    selector="#inPopupEstimate3",
                    description="職員資格",
                    required=False
                ),
                "start_time": FieldMapping(
                    selector="#inPopupStartHour",
                    description="開始時間",
                    required=True
                ),
                "end_time": FieldMapping(
                    selector="#inPopupEndHour", 
                    description="終了時間",
                    required=True
                ),
                "plan_achievement": FieldMapping(
                    selector="#inPopupPlanAchievementsDivision02",
                    description="予定・実績",
                    required=True
                ),
                "service_date": FieldMapping(
                    selector="[data-date]",
                    description="実施日",
                    required=True
                ),
                "submit_button": FieldMapping(
                    selector="#btnRegisPop",
                    description="登録する",
                    required=True
                )
            },
            required_fields=["service_kind", "estimate1", "start_time", "end_time", "plan_achievement"],
            processing_order=["service_kind", "service_content", "unit_count", "estimate1", "estimate2", "estimate3", 
                            "start_time", "end_time", "plan_achievement", "service_date"]
        )
    
    @staticmethod
    def get_iryou_config() -> InsuranceConfig:
        """获取医疗保险配置"""
        return InsuranceConfig(
            insurance_type=InsuranceType.IRYOU,
            selector="#inPopupInsuranceDivision02",
            display_name="医療保险",
            field_mappings={
                "service_division": FieldMapping(
                    selector="#inPopupEstimate1",
                    description="サービス区分",
                    required=True
                ),
                "basic_medical_fee": FieldMapping(
                    selector="#inPopupEstimate2",
                    description="基本療養費",
                    required=True
                ),
                "staff_qualification": FieldMapping(
                    selector="#inPopupEstimate3",
                    description="職員資格",
                    required=True
                ),
                "same_day_visit_count": FieldMapping(
                    selector="#inPopupEstimate4",
                    description="同一日訪問人数",
                    required=False
                ),
                "start_time": FieldMapping(
                    selector="#inPopupStartHour",
                    description="開始時間",
                    required=True
                ),
                "end_time": FieldMapping(
                    selector="#inPopupEndHour",
                    description="終了時間", 
                    required=True
                ),
                "plan_achievement": FieldMapping(
                    selector="#inPopupPlanAchievementsDivision02",
                    description="予定・実績",
                    required=True
                ),
                "service_date": FieldMapping(
                    selector="[data-date]",
                    description="実施日",
                    required=True
                ),
                "submit_button": FieldMapping(
                    selector="#btnRegisPop",
                    description="登録する",
                    required=True
                )
            },
            required_fields=["service_division", "basic_medical_fee", "staff_qualification", "start_time", "end_time", "plan_achievement"],
            processing_order=["service_division", "basic_medical_fee", "staff_qualification", "same_day_visit_count",
                            "start_time", "end_time", "plan_achievement", "service_date"]
        )
    
    @staticmethod
    def get_jihi_config() -> InsuranceConfig:
        """获取自费保险配置"""
        return InsuranceConfig(
            insurance_type=InsuranceType.JIHI,
            selector="#inPopupInsuranceDivision03",
            display_name="自費保险",
            field_mappings={
                "category": FieldMapping(
                    selector="#inPopupInsuranceOtherCategoryName",
                    description="分類",
                    required=True
                ),
                "service_content": FieldMapping(
                    selector="#inPopupServiceContent_row > td:nth-child(2) > div",
                    description="サービス内容",
                    required=True
                ),
                "estimation_time": FieldMapping(
                    selector="#inPopupEstimationTime",
                    description="算定時間",
                    required=False
                ),
                "start_time": FieldMapping(
                    selector="#inPopupStartHour",
                    description="開始時間",
                    required=True
                ),
                "end_time": FieldMapping(
                    selector="#inPopupEndHour",
                    description="終了時間",
                    required=True
                ),
                "amount": FieldMapping(
                    selector="#inPopupAmount",
                    description="金額",
                    required=True
                ),
                "plan_achievement": FieldMapping(
                    selector="#inPopupPlanAchievementsDivision02",
                    description="予定・実績",
                    required=True
                ),
                "service_date": FieldMapping(
                    selector="[data-date]",
                    description="実施日",
                    required=True
                ),
                "submit_button": FieldMapping(
                    selector="#btnRegisPop",
                    description="登録する",
                    required=True
                )
            },
            required_fields=["category", "service_content", "amount", "start_time", "end_time", "plan_achievement"],
            processing_order=["category", "service_content", "estimation_time", "start_time", "end_time", 
                            "amount", "plan_achievement", "service_date"]
        )


class InsuranceConfigManager:
    """保险配置管理器"""
    
    def __init__(self):
        self._configs = {
            InsuranceType.KAIGO: InsuranceFieldMappings.get_kaigo_config(),
            InsuranceType.IRYOU: InsuranceFieldMappings.get_iryou_config(),
            InsuranceType.JIHI: InsuranceFieldMappings.get_jihi_config()
        }
    
    def get_config(self, insurance_type: InsuranceType) -> InsuranceConfig:
        """获取指定保险类型的配置"""
        return self._configs.get(insurance_type)
    
    def get_selector(self, insurance_type: InsuranceType) -> str:
        """获取保险类型选择器"""
        config = self.get_config(insurance_type)
        return config.selector if config else ""
    
    def get_field_selector(self, insurance_type: InsuranceType, field_name: str) -> str:
        """获取指定字段的选择器"""
        config = self.get_config(insurance_type)
        if config and field_name in config.field_mappings:
            return config.field_mappings[field_name].selector
        return ""
    
    def get_required_fields(self, insurance_type: InsuranceType) -> List[str]:
        """获取必填字段列表"""
        config = self.get_config(insurance_type)
        return config.required_fields if config else []
    
    def get_processing_order(self, insurance_type: InsuranceType) -> List[str]:
        """获取字段处理顺序"""
        config = self.get_config(insurance_type)
        return config.processing_order if config else []
    
    @staticmethod
    def parse_insurance_type(insurance_str: str) -> Optional[InsuranceType]:
        """解析保险类型字符串"""
        type_mapping = {
            "介護": InsuranceType.KAIGO,
            "医療": InsuranceType.IRYOU,
            "精神医療": InsuranceType.SEISHIN_IRYOU,
            "自費": InsuranceType.JIHI,
            "kaigo": InsuranceType.KAIGO,
            "iryou": InsuranceType.IRYOU,
            "seishin": InsuranceType.SEISHIN_IRYOU,
            "jihi": InsuranceType.JIHI
        }
        return type_mapping.get(insurance_str)
