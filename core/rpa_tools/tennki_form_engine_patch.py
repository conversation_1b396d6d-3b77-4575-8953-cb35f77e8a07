"""
Tennki表单引擎补丁
为现有的TennkiFormEngine添加增强的职员信息填写功能

使用方法：
在TennkiFormEngine中调用 enhanced_fill_staff_info_with_protection
"""

import asyncio
from logger_config import logger


async def enhanced_fill_staff_info_with_protection(form_engine, row):
    """🆕 增强版职员信息填写（带完整数据保护）"""
    try:
        logger.debug("👨‍⚕️ 开始增强版职员信息填写（带数据保护）...")
        
        # 1. 检查是否有增强功能可用
        if hasattr(form_engine, 'enhanced'):
            logger.debug("🚀 使用增强版职员信息填写...")
            return await form_engine.enhanced.enhanced_fill_staff_info(row)
        
        # 2. 如果没有增强功能，使用带保护的传统方法
        logger.debug("🔄 使用带保护的传统职员信息填写...")
        return await _protected_traditional_staff_fill(form_engine, row)
        
    except Exception as e:
        logger.error(f"❌ 增强版职员信息填写失败: {e}")
        # 最后的备用方案
        logger.debug("🔄 使用最基本的职员信息填写...")
        return await _basic_staff_fill(form_engine, row)


async def _protected_traditional_staff_fill(form_engine, row):
    """带保护的传统职员信息填写"""
    page = form_engine.selector_executor.page
    
    try:
        logger.debug("🛡️ 开始带保护的传统职员信息填写...")
        
        # 1. 备份当前表单数据
        backup_data = await _backup_current_form_data(page)
        
        # 2. 执行职员信息填写
        result = await _execute_staff_fill_with_monitoring(form_engine, row)
        
        # 3. 检查数据完整性
        integrity_ok = await _check_data_integrity(page, backup_data)
        if not integrity_ok:
            logger.warning("⚠️ 检测到数据可能丢失，尝试恢复...")
            await _restore_form_data(page, backup_data)
        
        logger.debug("✅ 带保护的传统职员信息填写完成")
        return result
        
    except Exception as e:
        logger.error(f"❌ 带保护的传统职员信息填写失败: {e}")
        raise


async def _execute_staff_fill_with_monitoring(form_engine, row):
    """执行职员信息填写并监控"""
    page = form_engine.selector_executor.page
    filled_count = 0
    
    try:
        # 1. 确保职员字段已激活
        await _ensure_staff_fields_ready(page, row)
        
        # 2. 填写职员职种
        staff_qualification = _extract_staff_qualification_safe(row)
        if staff_qualification:
            job_label = _map_qualification_to_job(staff_qualification)
            if job_label:
                logger.debug(f"👨‍⚕️ 填写职员职种: {staff_qualification} → {job_label}")
                
                success = await _safe_select_option(page, '#chargeStaff1JobDivision1', job_label)
                if success:
                    logger.debug(f"✅ 职员职种填写成功: {job_label}")
                    filled_count += 1
                else:
                    logger.warning(f"⚠️ 职员职种填写失败: {job_label}")
        
        # 3. 填写职员姓名（如果数据可用）
        staff_name = _extract_staff_name_safe(row)
        if staff_name:
            logger.debug(f"👨‍⚕️ 填写职员姓名: {staff_name}")
            
            success = await _safe_select_option(page, '#chargeStaff1Id1', staff_name)
            if success:
                logger.debug(f"✅ 职员姓名填写成功: {staff_name}")
                filled_count += 1
            else:
                logger.warning(f"⚠️ 职员姓名填写失败，尝试部分匹配...")
                partial_success = await _try_partial_name_match(page, staff_name)
                if partial_success:
                    filled_count += 1
        
        logger.debug(f"✅ 职员信息填写完成: 成功填写 {filled_count} 个字段")
        return True
        
    except Exception as e:
        logger.error(f"❌ 职员信息填写执行失败: {e}")
        raise


async def _ensure_staff_fields_ready(page, row):
    """确保职员字段已准备就绪"""
    try:
        logger.debug("🔧 确保职员字段已准备就绪...")
        
        # 1. 检查职员字段是否已激活
        is_ready = await page.evaluate("""
            () => {
                const staffButton = document.querySelector('#input_staff_on > input');
                return staffButton && !staffButton.disabled;
            }
        """)
        
        if is_ready:
            logger.debug("✅ 职员字段已激活")
            return True
        
        # 2. 如果未激活，执行激活流程
        logger.debug("🔄 职员字段未激活，开始激活...")
        
        # 选择实绩
        await page.click('#inPopupPlanAchievementsDivision02')
        await page.wait_for_timeout(1000)
        
        # 选择实施日（简化版）
        await _simple_date_selection(page, row)
        
        # 点击職員情報入力
        await _safe_click_staff_button(page)
        
        logger.debug("✅ 职员字段激活完成")
        return True
        
    except Exception as e:
        logger.error(f"❌ 职员字段准备失败: {e}")
        raise


async def _simple_date_selection(page, row):
    """简化的日期选择"""
    try:
        # 尝试从数据中提取日期
        date_value = None
        if isinstance(row, list) and len(row) > 0:
            date_value = str(row[0]).strip() if row[0] else None
        elif isinstance(row, dict):
            date_value = row.get('service_date', '')
        
        if date_value and date_value.isdigit():
            day = int(date_value)
            if 1 <= day <= 31:
                logger.debug(f"📅 选择日期: {day}日")
                
                success = await page.evaluate(f"""
                    (day) => {{
                        const dayElements = document.querySelectorAll('#simple-select-days-range a');
                        for (let element of dayElements) {{
                            if (element.textContent.trim() === day.toString()) {{
                                element.click();
                                return true;
                            }}
                        }}
                        return false;
                    }}
                """, day)
                
                if success:
                    logger.debug(f"✅ 日期选择成功: {day}日")
                    await page.wait_for_timeout(1000)
                else:
                    logger.debug(f"⚠️ 日期选择失败: {day}日")
        
    except Exception as e:
        logger.debug(f"⚠️ 简化日期选择失败: {e}")


async def _safe_click_staff_button(page):
    """安全点击職員情報入力按钮"""
    try:
        # 清除可能的干扰元素
        await page.evaluate("""
            () => {
                const karteElements = document.querySelectorAll('[id*="karte"], [class*="karte"]');
                karteElements.forEach(el => el.remove());
            }
        """)
        
        # 使用JavaScript点击
        success = await page.evaluate("""
            () => {
                const staffButton = document.querySelector('#input_staff_on > input');
                if (staffButton && !staffButton.disabled) {
                    staffButton.click();
                    return true;
                }
                return false;
            }
        """)
        
        if success:
            logger.debug("✅ 職員情報入力按钮点击成功")
            await page.wait_for_timeout(1000)
        else:
            logger.warning("⚠️ 職員情報入力按钮点击失败")
            
    except Exception as e:
        logger.warning(f"⚠️ 安全点击職員情報入力按钮失败: {e}")


async def _safe_select_option(page, selector, value):
    """安全选择选项"""
    try:
        # 方法1：标准选择
        await page.select_option(selector, label=value, timeout=3000)
        return True
    except:
        try:
            # 方法2：JavaScript选择
            success = await page.evaluate(f"""
                (value) => {{
                    const field = document.querySelector('{selector}');
                    if (!field) return false;
                    
                    for (let option of field.options) {{
                        if (option.text === value || option.label === value) {{
                            field.value = option.value;
                            field.selectedIndex = option.index;
                            field.dispatchEvent(new Event('change', {{ bubbles: true }}));
                            return true;
                        }}
                    }}
                    return false;
                }}
            """, value)
            return success
        except:
            return False


async def _try_partial_name_match(page, staff_name):
    """尝试部分匹配职员姓名"""
    try:
        options = await page.evaluate("""
            () => {
                const select = document.querySelector('#chargeStaff1Id1');
                if (!select) return [];
                return Array.from(select.options).map(opt => opt.text).filter(text => text && text !== '-');
            }
        """)
        
        for option in options:
            if staff_name in option or option in staff_name:
                success = await _safe_select_option(page, '#chargeStaff1Id1', option)
                if success:
                    logger.debug(f"✅ 职员姓名部分匹配成功: {option}")
                    return True
        
        return False
    except:
        return False


async def _backup_current_form_data(page):
    """备份当前表单数据"""
    try:
        return await page.evaluate("""
            () => {
                const backup = {};
                const inputs = document.querySelectorAll('#registModal input, #registModal select');
                inputs.forEach(input => {
                    if (input.value && input.value.trim() !== '') {
                        backup[input.id || input.name] = input.value;
                    }
                });
                return backup;
            }
        """)
    except:
        return {}


async def _restore_form_data(page, backup_data):
    """恢复表单数据"""
    if not backup_data:
        return
    
    try:
        await page.evaluate("""
            (backupData) => {
                for (const [fieldId, value] of Object.entries(backupData)) {
                    const field = document.getElementById(fieldId) || document.querySelector(`[name="${fieldId}"]`);
                    if (field && (!field.value || field.value.trim() === '')) {
                        field.value = value;
                        field.dispatchEvent(new Event('input', { bubbles: true }));
                    }
                }
            }
        """, backup_data)
    except Exception as e:
        logger.warning(f"⚠️ 表单数据恢复失败: {e}")


async def _check_data_integrity(page, backup_data):
    """检查数据完整性"""
    if not backup_data:
        return True
    
    try:
        current_data = await _backup_current_form_data(page)
        missing_count = 0
        
        for field_id in backup_data:
            if field_id not in current_data:
                missing_count += 1
        
        if missing_count > 0:
            logger.warning(f"⚠️ 检测到 {missing_count} 个字段数据丢失")
            return False
        
        return True
    except:
        return True


async def _basic_staff_fill(form_engine, row):
    """最基本的职员信息填写（备用方案）"""
    try:
        logger.debug("🔄 使用最基本的职员信息填写...")
        page = form_engine.selector_executor.page
        
        # 只尝试填写职种，不做复杂操作
        staff_qualification = _extract_staff_qualification_safe(row)
        if staff_qualification:
            job_label = _map_qualification_to_job(staff_qualification)
            if job_label:
                try:
                    await page.select_option('#chargeStaff1JobDivision1', label=job_label, timeout=3000)
                    logger.debug(f"✅ 基本职种填写成功: {job_label}")
                    return True
                except:
                    logger.debug(f"⚠️ 基本职种填写失败: {job_label}")
        
        return True  # 即使失败也返回True，避免阻塞流程
        
    except Exception as e:
        logger.warning(f"⚠️ 最基本的职员信息填写也失败: {e}")
        return True  # 最后的容错


def _extract_staff_qualification_safe(row):
    """安全提取职员资格"""
    try:
        if isinstance(row, dict):
            return row.get('staff_qualification', '')
        elif isinstance(row, list) and len(row) > 20:
            return row[20] if row[20] else ''
        return ''
    except:
        return ''


def _extract_staff_name_safe(row):
    """安全提取职员姓名"""
    try:
        if isinstance(row, dict):
            return row.get('staff_name', '')
        elif isinstance(row, list) and len(row) > 23:
            return row[23] if row[23] else ''
        return ''
    except:
        return ''


def _map_qualification_to_job(qualification):
    """映射职员资格到职种"""
    mapping = {
        "正看護師": "看護師",
        "准看護師": "准看護師", 
        "理学療法士": "理学療法士",
        "作業療法士": "作業療法士",
        "言語聴覚士": "言語聴覚士",
        "社会福祉士": "社会福祉士",
        "介護福祉士": "介護福祉士",
        "ケアマネジャー": "ケアマネジャー"
    }
    return mapping.get(qualification, qualification)
