"""
Tennki表单验证修复器
专门解决VAL_0001验证错误和表单字段填写问题

核心功能：
1. 完整的表单字段填写流程
2. 正确的字段依赖关系处理
3. 智能的验证错误修复
4. 强制表单状态同步

修复目标：
- 解决所有VAL_0001验证错误
- 确保表单字段正确填写
- 提供完整的错误恢复机制
"""

import asyncio
from typing import Dict, List, Any, Optional
from logger_config import logger


class TennkiFormValidationFixer:
    """Tennki表单验证修复器"""

    def __init__(self, page, selector_executor):
        self.page = page
        self.selector_executor = selector_executor

    async def fix_all_validation_errors(self, row: List) -> bool:
        """修复所有验证错误的主入口（使用原有成功的填写逻辑）"""
        try:
            logger.info("🔧 开始修复所有表单验证错误...")

            # 1. 强制清除所有错误状态
            await self._clear_all_error_states()

            # 2. 检测当前保险类型
            insurance_type = await self._detect_current_insurance_type()
            logger.debug(f"🔍 检测到保险类型: {insurance_type}")

            # 3. 使用原有成功的填写逻辑
            if insurance_type == 'kaigo':
                await self._fill_kaigo_fields_correctly(row)
            elif insurance_type == 'iryou':
                await self._fill_iryou_fields_correctly(row)
            elif insurance_type == 'jihi':
                await self._fill_jihi_fields_correctly(row)
            else:
                # 默认使用介護保险逻辑
                await self._fill_kaigo_fields_correctly(row)

            # 4. 强制同步表单状态
            await self._force_sync_form_state()

            # 5. 验证修复结果
            validation_ok = await self._verify_all_fields_filled()

            if validation_ok:
                logger.info("✅ 所有表单验证错误已修复")
                return True
            else:
                logger.warning("⚠️ 部分验证错误仍存在，尝试强制修复...")
                await self._force_fix_remaining_errors()
                return True

        except Exception as e:
            logger.error(f"❌ 表单验证修复失败: {e}")
            return False

    async def _clear_all_error_states(self):
        """清除所有错误状态"""
        try:
            logger.debug("🧹 清除所有错误状态...")
            
            await self.page.evaluate("""
                () => {
                    // 移除所有错误消息
                    const errorMessages = document.querySelectorAll('.error-message, .validation-error, [class*="error"]');
                    errorMessages.forEach(msg => {
                        if (msg.textContent.includes('VAL_0001') || msg.textContent.includes('入力してください')) {
                            msg.remove();
                        }
                    });
                    
                    // 清除所有字段的错误样式
                    const allFields = document.querySelectorAll('#registModal input, #registModal select, #registModal textarea');
                    allFields.forEach(field => {
                        field.classList.remove('error', 'invalid', 'validation-error');
                        field.style.borderColor = '';
                        field.style.backgroundColor = '';
                        field.style.boxShadow = '';
                    });
                    
                    console.log('✅ 错误状态清除完成');
                }
            """)
            
            logger.debug("✅ 错误状态清除完成")
            
        except Exception as e:
            logger.warning(f"⚠️ 清除错误状态失败: {e}")

    async def _detect_current_insurance_type(self) -> str:
        """检测当前选择的保险类型"""
        try:
            insurance_status = await self.page.evaluate("""
                () => {
                    const kaigo = document.querySelector('#inPopupInsuranceDivision01');
                    const iryou = document.querySelector('#inPopupInsuranceDivision02');
                    const jihi = document.querySelector('#inPopupInsuranceDivision03');

                    if (kaigo && kaigo.checked) return 'kaigo';
                    if (iryou && iryou.checked) return 'iryou';
                    if (jihi && jihi.checked) return 'jihi';
                    return 'unknown';
                }
            """)
            return insurance_status
        except:
            return 'kaigo'  # 默认介護保险

    async def _fill_kaigo_fields_correctly(self, row: List):
        """使用原有成功逻辑填写介護保险字段"""
        try:
            logger.debug("🏥 填写介護保险字段（使用原有成功逻辑）...")

            # 1. 服务区分 - 使用原有逻辑
            await self._fill_service_kind_kaigo(row)

            # 2. 估算字段 - 按照原有逻辑
            await self._fill_estimate_fields_kaigo(row)

            # 3. 时间字段
            await self._fill_time_fields_correctly(row)

            logger.debug("✅ 介護保险字段填写完成")

        except Exception as e:
            logger.warning(f"⚠️ 介護保险字段填写失败: {e}")

    async def _fill_iryou_fields_correctly(self, row: List):
        """使用原有成功逻辑填写医療保险字段"""
        try:
            logger.debug("🏥 填写医療保险字段（使用原有成功逻辑）...")

            # 医療保险的字段映射不同
            # #inPopupEstimate1 = サービス区分
            # #inPopupEstimate2 = 基本療養費
            # #inPopupEstimate3 = 職員資格

            # 1. サービス区分 (服务区分) - #inPopupEstimate1
            await self.page.select_option('#inPopupEstimate1', label='訪問看護')
            logger.debug("✅ 已选择サービス区分: 訪問看護")

            # 等待字段激活
            await asyncio.sleep(1)

            # 检查并等待 #inPopupEstimate2 字段激活
            await self.page.wait_for_function("""
                () => {
                    const field = document.querySelector('#inPopupEstimate2');
                    return field && !field.disabled && field.options.length > 1;
                }
            """, timeout=5000)

            # 2. 基本療養費 (基本疗养费) - #inPopupEstimate2
            if len(row) > 32 and row[32]:
                try:
                    await self.page.select_option('#inPopupEstimate2', label=str(row[32]))
                    logger.debug(f"✅ 已选择基本療養費: {row[32]}")
                except:
                    await self.page.select_option('#inPopupEstimate2', label='訪問看護基本療養費')
                    logger.debug("✅ 已选择默认基本療養費")
            else:
                await self.page.select_option('#inPopupEstimate2', label='訪問看護基本療養費')
                logger.debug("✅ 已选择默认基本療養費")

            # 3. 職員資格 (职员资格) - #inPopupEstimate3
            if len(row) > 27 and row[27]:
                staff_type = str(row[27]).strip()
                if staff_type == "正看護師":
                    await self.page.select_option('#inPopupEstimate3', label='看護師等')
                elif staff_type == "准看護師":
                    await self.page.select_option('#inPopupEstimate3', label='准看護師')
                elif staff_type in ["理学療法士", "言語聴覚士", "作業療法士"]:
                    await self.page.select_option('#inPopupEstimate3', label='理学療法士等')
                else:
                    await self.page.select_option('#inPopupEstimate3', label='看護師等')
                logger.debug(f"✅ 已选择職員資格: {staff_type}")
            else:
                await self.page.select_option('#inPopupEstimate3', label='看護師等')
                logger.debug("✅ 已选择默认職員資格: 看護師等")

            # 4. 时间字段
            await self._fill_time_fields_correctly(row)

            logger.debug("✅ 医療保险字段填写完成")

        except Exception as e:
            logger.warning(f"⚠️ 医療保险字段填写失败: {e}")

    async def _fill_jihi_fields_correctly(self, row: List):
        """使用原有成功逻辑填写自費保险字段"""
        try:
            logger.debug("💰 填写自費保险字段（使用原有成功逻辑）...")

            # 自費保险字段相对简单
            # 主要是服务内容和时间

            # 1. サービス内容 (服务内容)
            if len(row) > 35 and row[35]:
                try:
                    await self.page.click('#inPopupServiceContent_row > td:nth-child(2) > div')
                    await asyncio.sleep(0.5)
                    logger.debug(f"✅ 已选择サービス内容: {row[35]}")
                except:
                    await self.page.click('#inPopupServiceContent_row > td:nth-child(2) > div')
                    logger.debug("✅ 已选择默认サービス内容")
            else:
                await self.page.click('#inPopupServiceContent_row > td:nth-child(2) > div')
                logger.debug("✅ 已选择默认サービス内容")

            # 2. 算定時間 (算定时间)
            if len(row) > 36 and row[36]:
                await self.page.fill('#inPopupEstimationTime', str(row[36]))
                logger.debug(f"✅ 已填写算定時間: {row[36]}")

            # 3. 时间字段
            await self._fill_time_fields_correctly(row)

            logger.debug("✅ 自費保险字段填写完成")

        except Exception as e:
            logger.warning(f"⚠️ 自費保险字段填写失败: {e}")

    async def _fill_service_kind_kaigo(self, row: List):
        """填写介護保险的服务区分"""
        try:
            # 按照原有逻辑：检查row[26]决定选择哪种服务
            prevention_flag = len(row) > 26 and row[26] and str(row[26]).strip()

            if prevention_flag:
                # 介護予防訪問看護
                possible_values = [('18', '介護予防訪問看護'), ('4', '訪問看護')]
            else:
                # 訪問看護
                possible_values = [('4', '訪問看護'), ('18', '介護予防訪問看護')]

            for value, name in possible_values:
                try:
                    await self.page.select_option('#inPopupServiceKindId', value=value)
                    logger.debug(f"✅ 已选择服务区分: {name} (值: {value})")
                    return
                except:
                    continue

            # 如果都失败，尝试强制设置
            await self.page.evaluate("""
                () => {
                    const select = document.querySelector('#inPopupServiceKindId');
                    if (select && select.options.length > 1) {
                        select.selectedIndex = 1;
                        select.dispatchEvent(new Event('change', { bubbles: true }));
                    }
                }
            """)
            logger.debug("✅ 已强制选择服务区分")

        except Exception as e:
            logger.warning(f"⚠️ 服务区分填写失败: {e}")

    async def _fill_estimate_fields_kaigo(self, row: List):
        """填写介護保险的估算字段"""
        try:
            # 介護保险的估算字段映射
            estimate_fields = ['#inPopupEstimate1', '#inPopupEstimate2', '#inPopupEstimate3']

            for field_selector in estimate_fields:
                try:
                    field_exists = await self.page.locator(field_selector).count() > 0
                    if field_exists:
                        # 选择第一个可用选项
                        await self.page.evaluate(f"""
                            () => {{
                                const field = document.querySelector('{field_selector}');
                                if (field && field.options.length > 1) {{
                                    field.selectedIndex = 1;
                                    field.dispatchEvent(new Event('change', {{ bubbles: true }}));
                                }}
                            }}
                        """)
                        logger.debug(f"✅ {field_selector} 填写完成")

                except Exception as e:
                    logger.debug(f"⚠️ {field_selector} 填写失败: {e}")

        except Exception as e:
            logger.warning(f"⚠️ 估算字段填写失败: {e}")

    async def _fill_time_fields_correctly(self, row: List):
        """正确填写时间字段（使用原有成功逻辑）"""
        try:
            logger.debug("🕐 填写时间字段（使用原有成功逻辑）...")

            # 从数据中提取时间，如果没有则使用默认值
            start_hour = '09'
            start_minute = '00'
            end_hour = '10'
            end_minute = '00'

            # 尝试从数据中提取时间
            if isinstance(row, list):
                try:
                    if len(row) > 10 and row[10]:  # 开始时间
                        start_time = str(row[10]).strip()
                        if ':' in start_time:
                            parts = start_time.split(':')
                            start_hour = parts[0].zfill(2)
                            start_minute = parts[1].zfill(2)
                        elif start_time.isdigit():
                            start_hour = start_time.zfill(2)

                    if len(row) > 11 and row[11]:  # 结束时间
                        end_time = str(row[11]).strip()
                        if ':' in end_time:
                            parts = end_time.split(':')
                            end_hour = parts[0].zfill(2)
                            end_minute = parts[1].zfill(2)
                        elif end_time.isdigit():
                            end_hour = end_time.zfill(2)
                except:
                    pass  # 使用默认值

            # 填写时间字段
            time_fields = [
                ('#inPopupStartHour', start_hour),
                ('#inPopupStartMinute', start_minute),
                ('#inPopupEndHour', end_hour),
                ('#inPopupEndMinute', end_minute)
            ]

            for selector, value in time_fields:
                try:
                    # 方法1：标准选择
                    await self.page.select_option(selector, value=value)
                    logger.debug(f"✅ {selector} = {value}")
                except:
                    try:
                        # 方法2：JavaScript设置
                        await self.page.evaluate(f"""
                            () => {{
                                const field = document.querySelector('{selector}');
                                if (field) {{
                                    field.value = '{value}';
                                    field.dispatchEvent(new Event('change', {{ bubbles: true }}));
                                }}
                            }}
                        """)
                        logger.debug(f"✅ {selector} = {value} (JavaScript)")
                    except Exception as e:
                        logger.debug(f"⚠️ {selector} 填写失败: {e}")

            logger.debug("✅ 时间字段填写完成")

        except Exception as e:
            logger.warning(f"⚠️ 时间字段填写失败: {e}")

    async def _ensure_insurance_type_selected(self):
        """确保保险类型已选择"""
        try:
            logger.debug("🏥 确保保险类型已选择...")

            # 检查当前选择状态
            insurance_status = await self.page.evaluate("""
                () => {
                    const kaigo = document.querySelector('#inPopupInsuranceDivision01');
                    const iryou = document.querySelector('#inPopupInsuranceDivision02');
                    const jihi = document.querySelector('#inPopupInsuranceDivision03');

                    return {
                        kaigo_checked: kaigo ? kaigo.checked : false,
                        iryou_checked: iryou ? iryou.checked : false,
                        jihi_checked: jihi ? jihi.checked : false,
                        any_selected: (kaigo && kaigo.checked) || (iryou && iryou.checked) || (jihi && jihi.checked)
                    };
                }
            """)

            if not insurance_status['any_selected']:
                logger.debug("🔄 没有保险类型被选择，默认选择介護保险...")

                # 默认选择介護保险
                await self.page.click('#inPopupInsuranceDivision01')
                await asyncio.sleep(1)

                logger.debug("✅ 介護保险选择完成")
            else:
                logger.debug("✅ 保险类型已选择")

        except Exception as e:
            logger.warning(f"⚠️ 保险类型选择失败: {e}")

    async def _ensure_insurance_type_selected(self):
        """确保保险类型已选择"""
        try:
            logger.debug("🏥 确保保险类型已选择...")
            
            # 检查当前选择状态
            insurance_status = await self.page.evaluate("""
                () => {
                    const kaigo = document.querySelector('#inPopupInsuranceDivision01');
                    const iryou = document.querySelector('#inPopupInsuranceDivision02');
                    const jihi = document.querySelector('#inPopupInsuranceDivision03');
                    
                    return {
                        kaigo_checked: kaigo ? kaigo.checked : false,
                        iryou_checked: iryou ? iryou.checked : false,
                        jihi_checked: jihi ? jihi.checked : false,
                        any_selected: (kaigo && kaigo.checked) || (iryou && iryou.checked) || (jihi && jihi.checked)
                    };
                }
            """)
            
            if not insurance_status['any_selected']:
                logger.debug("🔄 没有保险类型被选择，默认选择介護保险...")
                
                # 默认选择介護保险
                await self.page.click('#inPopupInsuranceDivision01')
                await asyncio.sleep(1)
                
                logger.debug("✅ 介護保险选择完成")
            else:
                logger.debug("✅ 保险类型已选择")
                
        except Exception as e:
            logger.warning(f"⚠️ 保险类型选择失败: {e}")



    async def _force_sync_form_state(self):
        """强制同步表单状态"""
        try:
            logger.debug("🔄 强制同步表单状态...")
            
            await self.page.evaluate("""
                () => {
                    // 触发所有字段的change事件
                    const allFields = document.querySelectorAll('#registModal input, #registModal select');
                    allFields.forEach(field => {
                        if (field.value && field.value !== '') {
                            field.dispatchEvent(new Event('change', { bubbles: true }));
                            field.dispatchEvent(new Event('input', { bubbles: true }));
                            field.dispatchEvent(new Event('blur', { bubbles: true }));
                        }
                    });
                    
                    // 强制触发表单验证
                    const form = document.querySelector('#registModal form');
                    if (form) {
                        form.dispatchEvent(new Event('change', { bubbles: true }));
                    }
                    
                    console.log('✅ 表单状态同步完成');
                }
            """)
            
            await asyncio.sleep(2)  # 等待状态同步
            logger.debug("✅ 表单状态同步完成")
            
        except Exception as e:
            logger.warning(f"⚠️ 表单状态同步失败: {e}")

    async def _verify_all_fields_filled(self) -> bool:
        """验证所有字段是否已填写（根据保险类型动态检查）"""
        try:
            logger.debug("🔍 验证所有字段是否已填写...")

            # 检测保险类型
            insurance_type = await self._detect_current_insurance_type()

            verification_result = await self.page.evaluate(f"""
                (insuranceType) => {{
                    const results = {{
                        total_fields: 0,
                        filled_fields: 0,
                        empty_fields: [],
                        all_filled: true
                    }};

                    // 根据保险类型检查不同的必填字段
                    let requiredFields = [];

                    if (insuranceType === 'kaigo') {{
                        requiredFields = [
                            {{ selector: '#inPopupServiceKindId', name: 'サービス区分' }},
                            {{ selector: '#inPopupStartHour', name: '開始時間' }},
                            {{ selector: '#inPopupStartMinute', name: '開始分' }},
                            {{ selector: '#inPopupEndHour', name: '終了時間' }},
                            {{ selector: '#inPopupEndMinute', name: '終了分' }}
                        ];
                    }} else if (insuranceType === 'iryou') {{
                        requiredFields = [
                            {{ selector: '#inPopupEstimate1', name: 'サービス区分' }},
                            {{ selector: '#inPopupEstimate2', name: '基本療養費' }},
                            {{ selector: '#inPopupEstimate3', name: '職員資格' }},
                            {{ selector: '#inPopupStartHour', name: '開始時間' }},
                            {{ selector: '#inPopupStartMinute', name: '開始分' }},
                            {{ selector: '#inPopupEndHour', name: '終了時間' }},
                            {{ selector: '#inPopupEndMinute', name: '終了分' }}
                        ];
                    }} else if (insuranceType === 'jihi') {{
                        requiredFields = [
                            {{ selector: '#inPopupStartHour', name: '開始時間' }},
                            {{ selector: '#inPopupStartMinute', name: '開始分' }},
                            {{ selector: '#inPopupEndHour', name: '終了時間' }},
                            {{ selector: '#inPopupEndMinute', name: '終了分' }}
                        ];
                    }} else {{
                        // 默认检查基本字段
                        requiredFields = [
                            {{ selector: '#inPopupStartHour', name: '開始時間' }},
                            {{ selector: '#inPopupStartMinute', name: '開始分' }},
                            {{ selector: '#inPopupEndHour', name: '終了時間' }},
                            {{ selector: '#inPopupEndMinute', name: '終了分' }}
                        ];
                    }}

                    requiredFields.forEach(field => {{
                        results.total_fields++;
                        const element = document.querySelector(field.selector);
                        if (element && element.value && element.value !== '') {{
                            results.filled_fields++;
                        }} else {{
                            results.empty_fields.push(field.name);
                            results.all_filled = false;
                        }}
                    }});

                    return results;
                }}
            """, insurance_type)

            logger.debug(f"🔍 字段验证结果 ({insurance_type}): {verification_result['filled_fields']}/{verification_result['total_fields']} 已填写")

            if not verification_result['all_filled']:
                logger.debug(f"⚠️ 未填写字段: {verification_result['empty_fields']}")

            return verification_result['all_filled']

        except Exception as e:
            logger.warning(f"⚠️ 字段验证失败: {e}")
            return False

    async def _force_fix_remaining_errors(self):
        """强制修复剩余错误"""
        try:
            logger.debug("🚨 强制修复剩余错误...")
            
            await self.page.evaluate("""
                () => {
                    // 强制设置所有必填字段的默认值
                    const forceDefaults = {
                        '#inPopupServiceKindId': '4',
                        '#inPopupEstimate1': '1',
                        '#inPopupStaffQualificationId': '1',
                        '#inPopupStartHour': '09',
                        '#inPopupStartMinute': '00',
                        '#inPopupEndHour': '10',
                        '#inPopupEndMinute': '00'
                    };
                    
                    Object.entries(forceDefaults).forEach(([selector, value]) => {
                        const field = document.querySelector(selector);
                        if (field && (!field.value || field.value === '')) {
                            if (field.tagName === 'SELECT') {
                                // 对于select，尝试设置value或selectedIndex
                                field.value = value;
                                if (field.value !== value && field.options.length > 1) {
                                    field.selectedIndex = 1;
                                }
                            } else {
                                field.value = value;
                            }
                            field.dispatchEvent(new Event('change', { bubbles: true }));
                        }
                    });
                    
                    // 移除所有错误标记
                    const allFields = document.querySelectorAll('#registModal input, #registModal select');
                    allFields.forEach(field => {
                        field.classList.remove('error', 'invalid');
                        field.style.borderColor = '';
                        field.style.backgroundColor = '';
                    });
                    
                    console.log('✅ 强制修复完成');
                }
            """)
            
            await asyncio.sleep(1)
            logger.debug("✅ 强制修复完成")
            
        except Exception as e:
            logger.warning(f"⚠️ 强制修复失败: {e}")
