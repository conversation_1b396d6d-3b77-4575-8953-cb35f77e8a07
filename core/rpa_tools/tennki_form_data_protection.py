"""
Tennki表单数据保护模块
解决职员情报输入时数据丢失问题

核心功能：
1. 表单数据备份和恢复
2. 字段激活时的数据保护
3. 智能数据验证和恢复
4. 页面状态监控和保护

修复目标：
- 职员情报字段激活时不再丢失已填写数据
- 页面跳转时自动恢复表单状态
- 提供完整的数据保护机制
"""

import asyncio
from typing import Dict, Any, Optional
from logger_config import logger


class TennkiFormDataProtector:
    """Tennki表单数据保护器"""

    def __init__(self, page):
        self.page = page
        self.backup_data = {}
        self.protection_enabled = True

    async def backup_form_data(self) -> Dict[str, Any]:
        """🆕 备份表单数据，防止字段激活时数据丢失"""
        try:
            logger.debug("💾 备份表单数据...")
            
            form_data = await self.page.evaluate("""
                () => {
                    const backup = {};
                    
                    // 备份所有输入字段
                    const inputs = document.querySelectorAll('#registModal input[type="text"], #registModal input[type="number"], #registModal textarea');
                    inputs.forEach(input => {
                        if (input.value && input.value.trim() !== '') {
                            backup[input.id || input.name] = {
                                type: 'input',
                                value: input.value,
                                selector: input.id ? `#${input.id}` : `[name="${input.name}"]`
                            };
                        }
                    });
                    
                    // 备份所有选择字段
                    const selects = document.querySelectorAll('#registModal select');
                    selects.forEach(select => {
                        if (select.selectedIndex > 0) {
                            backup[select.id || select.name] = {
                                type: 'select',
                                value: select.value,
                                selectedIndex: select.selectedIndex,
                                selectedText: select.options[select.selectedIndex].text,
                                selector: select.id ? `#${select.id}` : `[name="${select.name}"]`
                            };
                        }
                    });
                    
                    // 备份复选框和单选框
                    const checkboxes = document.querySelectorAll('#registModal input[type="checkbox"], #registModal input[type="radio"]');
                    checkboxes.forEach(checkbox => {
                        if (checkbox.checked) {
                            backup[checkbox.id || checkbox.name] = {
                                type: 'checkbox',
                                checked: checkbox.checked,
                                selector: checkbox.id ? `#${checkbox.id}` : `[name="${checkbox.name}"]`
                            };
                        }
                    });
                    
                    return backup;
                }
            """)
            
            self.backup_data = form_data
            logger.debug(f"💾 已备份 {len(form_data)} 个字段的数据")
            return form_data
            
        except Exception as e:
            logger.warning(f"⚠️ 表单数据备份失败: {e}")
            return {}

    async def restore_form_data(self, backup_data: Optional[Dict] = None) -> bool:
        """🆕 恢复表单数据"""
        restore_data = backup_data or self.backup_data
        
        if not restore_data:
            logger.debug("ℹ️ 没有备份数据需要恢复")
            return True
            
        try:
            logger.debug(f"🔄 恢复 {len(restore_data)} 个字段的数据...")
            
            restore_result = await self.page.evaluate("""
                (backupData) => {
                    const results = {
                        restored: 0,
                        failed: [],
                        skipped: 0,
                        details: []
                    };
                    
                    for (const [fieldId, fieldData] of Object.entries(backupData)) {
                        try {
                            const field = document.querySelector(fieldData.selector);
                            if (!field) {
                                results.failed.push(`${fieldId}: 字段不存在`);
                                continue;
                            }
                            
                            // 根据字段类型恢复数据
                            if (fieldData.type === 'input') {
                                // 检查字段当前是否已有值，避免覆盖新填写的数据
                                if (!field.value || field.value.trim() === '') {
                                    field.value = fieldData.value;
                                    // 触发input事件确保表单验证
                                    field.dispatchEvent(new Event('input', { bubbles: true }));
                                    field.dispatchEvent(new Event('change', { bubbles: true }));
                                    results.restored++;
                                    results.details.push(`${fieldId}: 恢复输入值 "${fieldData.value}"`);
                                } else {
                                    results.skipped++;
                                    results.details.push(`${fieldId}: 跳过（已有值）`);
                                }
                            } else if (fieldData.type === 'select') {
                                if (field.selectedIndex <= 0) {
                                    // 尝试按索引恢复
                                    if (fieldData.selectedIndex < field.options.length) {
                                        field.selectedIndex = fieldData.selectedIndex;
                                        field.dispatchEvent(new Event('change', { bubbles: true }));
                                        results.restored++;
                                        results.details.push(`${fieldId}: 恢复选择 "${fieldData.selectedText}"`);
                                    } else {
                                        // 按文本匹配
                                        let found = false;
                                        for (let i = 0; i < field.options.length; i++) {
                                            if (field.options[i].text === fieldData.selectedText) {
                                                field.selectedIndex = i;
                                                field.dispatchEvent(new Event('change', { bubbles: true }));
                                                results.restored++;
                                                results.details.push(`${fieldId}: 按文本恢复选择 "${fieldData.selectedText}"`);
                                                found = true;
                                                break;
                                            }
                                        }
                                        if (!found) {
                                            results.failed.push(`${fieldId}: 无法找到匹配选项 "${fieldData.selectedText}"`);
                                        }
                                    }
                                } else {
                                    results.skipped++;
                                    results.details.push(`${fieldId}: 跳过（已有选择）`);
                                }
                            } else if (fieldData.type === 'checkbox') {
                                if (!field.checked && fieldData.checked) {
                                    field.checked = true;
                                    field.dispatchEvent(new Event('change', { bubbles: true }));
                                    results.restored++;
                                    results.details.push(`${fieldId}: 恢复选中状态`);
                                } else {
                                    results.skipped++;
                                    results.details.push(`${fieldId}: 跳过（状态未变）`);
                                }
                            }
                        } catch (error) {
                            results.failed.push(`${fieldId}: ${error.message}`);
                        }
                    }
                    
                    return results;
                }
            """, restore_data)
            
            logger.debug(f"🔄 数据恢复完成: 恢复 {restore_result.get('restored', 0)} 个, 跳过 {restore_result.get('skipped', 0)} 个, 失败 {len(restore_result.get('failed', []))} 个")
            
            if restore_result.get('failed'):
                logger.debug(f"⚠️ 恢复失败的字段: {restore_result['failed']}")
                
            if restore_result.get('details'):
                logger.debug(f"📋 恢复详情: {restore_result['details'][:5]}...")  # 只显示前5个详情
                
            return restore_result.get('restored', 0) > 0
                
        except Exception as e:
            logger.warning(f"⚠️ 表单数据恢复失败: {e}")
            return False

    async def protect_during_field_activation(self, activation_func):
        """🆕 在字段激活过程中保护数据"""
        try:
            logger.debug("🛡️ 开始数据保护模式...")
            
            # 1. 备份当前数据
            await self.backup_form_data()
            
            # 2. 执行字段激活操作
            result = await activation_func()
            
            # 3. 等待激活完成
            await asyncio.sleep(1)
            
            # 4. 恢复数据
            restore_success = await self.restore_form_data()
            
            if restore_success:
                logger.debug("🛡️ 数据保护完成，数据已恢复")
            else:
                logger.warning("⚠️ 数据保护完成，但恢复过程中有问题")
                
            return result
            
        except Exception as e:
            logger.error(f"❌ 数据保护过程中出错: {e}")
            # 尝试恢复数据
            try:
                await self.restore_form_data()
            except:
                pass
            raise

    async def verify_data_integrity(self) -> bool:
        """🆕 验证数据完整性"""
        try:
            current_data = await self.backup_form_data()
            
            # 比较当前数据与备份数据
            if not self.backup_data:
                return True  # 没有备份数据，认为是正常的
                
            missing_fields = []
            for field_id, field_data in self.backup_data.items():
                if field_id not in current_data:
                    missing_fields.append(field_id)
                    
            if missing_fields:
                logger.warning(f"⚠️ 检测到数据丢失: {missing_fields}")
                return False
            else:
                logger.debug("✅ 数据完整性验证通过")
                return True
                
        except Exception as e:
            logger.warning(f"⚠️ 数据完整性验证失败: {e}")
            return False

    async def smart_field_fill(self, selector: str, value: str, field_type: str = 'auto') -> bool:
        """🆕 智能字段填写（带数据保护）"""
        try:
            logger.debug(f"🎯 智能填写字段: {selector} = {value}")
            
            # 1. 检查字段是否存在
            field_exists = await self.page.evaluate(f"""
                () => {{
                    const field = document.querySelector('{selector}');
                    return !!field;
                }}
            """)
            
            if not field_exists:
                logger.warning(f"⚠️ 字段不存在: {selector}")
                return False
                
            # 2. 备份当前状态
            await self.backup_form_data()
            
            # 3. 尝试填写
            if field_type == 'select' or field_type == 'auto':
                try:
                    await self.page.select_option(selector, label=value, timeout=3000)
                    logger.debug(f"✅ 选择字段填写成功: {selector}")
                    return True
                except Exception as e:
                    logger.debug(f"⚠️ 选择字段填写失败，尝试JavaScript方法: {e}")
                    
                    # 使用JavaScript备用方法
                    success = await self.page.evaluate(f"""
                        (value) => {{
                            const field = document.querySelector('{selector}');
                            if (!field) return false;

                            // 查找匹配的选项
                            for (let option of field.options) {{
                                if (option.text === value || option.label === value) {{
                                    field.value = option.value;
                                    field.selectedIndex = option.index;

                                    // 触发change事件
                                    const event = new Event('change', {{ bubbles: true }});
                                    field.dispatchEvent(event);

                                    return true;
                                }}
                            }}
                            return false;
                        }}
                    """, value)
                    
                    if success:
                        logger.debug(f"✅ JavaScript选择字段填写成功: {selector}")
                        return True
                    else:
                        logger.warning(f"⚠️ JavaScript选择字段填写也失败: {selector}")
                        return False
            else:
                # 输入字段
                await self.page.fill(selector, value, timeout=3000)
                logger.debug(f"✅ 输入字段填写成功: {selector}")
                return True
                
        except Exception as e:
            logger.warning(f"⚠️ 智能字段填写失败: {selector} = {value}, 错误: {e}")
            return False
