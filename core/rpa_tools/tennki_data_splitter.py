"""
Tennki数据分割器 - 按利用者分割数据
支持智能浏览器数量计算和负载均衡分配

修复日期：2025-08-01
修复内容：
- 按利用者分割数据功能
- 智能浏览器数量计算
- 负载均衡分配算法
- 批次信息创建
"""

import math
from typing import List, Dict, Any, Tuple
from logger_config import logger


class TennkiDataSplitter:
    """Tennki数据分割器 - 按利用者分割"""

    def __init__(self, max_records_per_batch: int = 30, max_users_per_batch: int = 10):
        self.max_records_per_batch = max_records_per_batch
        self.max_users_per_batch = max_users_per_batch
        
        # 数据量阈值配置
        self.data_volume_threshold = {
            'small': 50,     # 小数据量：1个浏览器
            'medium': 150,   # 中等数据量：2个浏览器
            'large': 300     # 大数据量：3个浏览器
        }

    def calculate_optimal_browser_count(self, total_records: int, total_users: int) -> int:
        """计算最优浏览器数量（按利用者分割）"""
        logger.info(f"📊 计算最优浏览器数量: {total_records}条记录, {total_users}个用户")

        # 优先按用户数量计算（按利用者分割）
        if total_users <= 1:
            optimal_count = 1
            reason = f"只有{total_users}个用户，使用1个浏览器"
        elif total_users <= 2:
            optimal_count = 2
            reason = f"有{total_users}个用户，按利用者分割使用2个浏览器"
        elif total_users <= 3:
            optimal_count = 3
            reason = f"有{total_users}个用户，按利用者分割使用3个浏览器"
        elif total_users <= 4:
            optimal_count = 4
            reason = f"有{total_users}个用户，按利用者分割使用4个浏览器"
        else:
            # 大数据量情况
            if total_records <= self.data_volume_threshold['small']:
                optimal_count = 1
                reason = f"小数据量({total_records}条) <= {self.data_volume_threshold['small']}"
            elif total_records <= self.data_volume_threshold['medium']:
                optimal_count = 2
                reason = f"中等数据量({total_records}条) <= {self.data_volume_threshold['medium']}"
            elif total_records <= self.data_volume_threshold['large']:
                optimal_count = 3
                reason = f"大数据量({total_records}条) <= {self.data_volume_threshold['large']}"
            else:
                optimal_count = 4  # 最大4个浏览器
                reason = f"超大数据量({total_records}条) > {self.data_volume_threshold['large']}"

        # 用户数量限制：确保每个浏览器至少有1个用户处理
        min_users_per_browser = 1
        max_browsers_by_users = max(1, total_users // min_users_per_browser)

        # 取较小值，避免浏览器过多
        final_count = min(optimal_count, max_browsers_by_users)

        logger.info(f"✅ 最优浏览器数量: {final_count} ({reason})")
        logger.info(f"   - 按数据量计算: {optimal_count}")
        logger.info(f"   - 按用户数限制: {max_browsers_by_users}")
        logger.info(f"   - 最终决定: {final_count}")

        return final_count

    def split_data_by_users(self, processed_data: List[Dict], target_browser_count: int) -> List[List[Dict]]:
        """按利用者分割数据"""
        if not processed_data:
            return []
        
        if target_browser_count == 1:
            logger.info("📊 使用单浏览器模式，无需分割")
            return [processed_data]
        
        logger.info(f"🧠 开始按利用者分割数据: {len(processed_data)}个用户 → {target_browser_count}个批次")
        
        # 按记录数排序，实现负载均衡
        sorted_data = sorted(processed_data, key=lambda x: x.get('total_records', 0), reverse=True)
        
        # 使用贪心算法分配到各个批次
        batches = [[] for _ in range(target_browser_count)]
        batch_loads = [0] * target_browser_count  # 记录每个批次的负载
        
        for user_data in sorted_data:
            # 找到当前负载最小的批次
            min_load_idx = batch_loads.index(min(batch_loads))
            
            # 检查是否会超过批次限制
            current_batch = batches[min_load_idx]
            current_records = sum(u.get('total_records', 0) for u in current_batch)
            current_users = len(current_batch)
            
            user_records = user_data.get('total_records', 0)
            
            # 如果添加这个用户会超过限制，尝试其他批次
            if (current_records + user_records > self.max_records_per_batch or 
                current_users >= self.max_users_per_batch):
                
                # 寻找可以容纳的批次
                suitable_batch_idx = None
                for i, batch in enumerate(batches):
                    batch_records = sum(u.get('total_records', 0) for u in batch)
                    batch_users = len(batch)
                    
                    if (batch_records + user_records <= self.max_records_per_batch and 
                        batch_users < self.max_users_per_batch):
                        suitable_batch_idx = i
                        break
                
                if suitable_batch_idx is not None:
                    min_load_idx = suitable_batch_idx
            
            # 分配用户到选定的批次
            batches[min_load_idx].append(user_data)
            batch_loads[min_load_idx] += user_records
        
        # 移除空批次
        non_empty_batches = [batch for batch in batches if batch]
        
        # 输出分割结果统计
        logger.info(f"📊 按利用者分割结果:")
        for i, batch in enumerate(non_empty_batches):
            batch_records = sum(u.get('total_records', 0) for u in batch)
            batch_users = len(batch)
            user_names = [u.get('user_name', 'unknown') for u in batch]
            logger.info(f"   批次 {i+1}: {batch_users}个用户({', '.join(user_names)}), {batch_records}条记录")
        
        logger.info(f"✅ 数据分割完成: {len(non_empty_batches)} 个有效批次")
        return non_empty_batches

    def validate_split_integrity(self, original_data: List[Dict], split_batches: List[List[Dict]]) -> bool:
        """验证分割完整性"""
        try:
            # 基本数量验证
            original_users = len(original_data)
            split_users = sum(len(batch) for batch in split_batches)
            
            if original_users != split_users:
                logger.error(f"❌ 用户数量不匹配: 原始{original_users} vs 分割{split_users}")
                return False
            
            # 记录数量验证
            original_records = sum(user.get('total_records', 0) for user in original_data)
            split_records = sum(
                sum(user.get('total_records', 0) for user in batch) 
                for batch in split_batches
            )
            
            if original_records != split_records:
                logger.error(f"❌ 记录数量不匹配: 原始{original_records} vs 分割{split_records}")
                return False
            
            # 用户唯一性验证
            original_user_ids = set(user.get('user_id') or user.get('user_name') for user in original_data)
            split_user_ids = set()
            
            for batch in split_batches:
                for user in batch:
                    user_id = user.get('user_id') or user.get('user_name')
                    if user_id in split_user_ids:
                        logger.error(f"❌ 发现重复用户: {user_id}")
                        return False
                    split_user_ids.add(user_id)
            
            if original_user_ids != split_user_ids:
                logger.error(f"❌ 用户ID不匹配")
                return False
            
            logger.info("✅ 分割完整性验证通过")
            return True
            
        except Exception as e:
            logger.error(f"❌ 分割完整性验证失败: {e}")
            return False


def create_batch_info_list(split_batches: List[List[Dict]]) -> List[Dict]:
    """创建批次信息列表"""
    batch_info_list = []
    
    for batch_index, batch_data in enumerate(split_batches):
        if not batch_data:  # 跳过空批次
            continue
            
        batch_records = sum(user.get('total_records', 0) for user in batch_data)
        batch_users = len(batch_data)
        
        # 计算批次优先级（记录数多的优先处理）
        priority = batch_records
        
        # 估算处理时间（基于记录数和用户数）
        estimated_time = batch_records * 2 + batch_users * 10  # 秒
        
        batch_info = {
            'batch_index': batch_index,
            'batch_data': batch_data,
            'total_records': batch_records,
            'total_users': batch_users,
            'priority': priority,
            'estimated_time': estimated_time,
            'status': 'pending'
        }
        
        batch_info_list.append(batch_info)
        
        user_names = [u.get('user_name', 'unknown') for u in batch_data]
        logger.debug(f"📦 批次 {batch_index + 1}: {batch_users}用户({', '.join(user_names)}), {batch_records}记录, 预计{estimated_time}秒")
    
    # 按优先级排序（记录数多的先处理）
    batch_info_list.sort(key=lambda x: x['priority'], reverse=True)
    
    logger.info(f"📋 创建了 {len(batch_info_list)} 个批次信息")
    return batch_info_list


# 便捷函数：一键智能分割
def smart_split_tennki_data(processed_data: List[Dict], 
                           max_records_per_batch: int = 30, 
                           max_users_per_batch: int = 10) -> Tuple[List[List[Dict]], int, str]:
    """
    一键智能分割天気数据
    
    Returns:
        Tuple[分割后的批次, 最优浏览器数量, 分割原因]
    """
    splitter = TennkiDataSplitter(max_records_per_batch, max_users_per_batch)
    
    # 计算最优浏览器数量
    total_records = sum(user.get('total_records', 0) for user in processed_data)
    total_users = len(processed_data)
    optimal_browser_count = splitter.calculate_optimal_browser_count(total_records, total_users)
    
    # 智能分割数据
    split_batches = splitter.split_data_by_users(processed_data, optimal_browser_count)
    
    # 验证分割完整性
    if not splitter.validate_split_integrity(processed_data, split_batches):
        logger.error("❌ 智能分割验证失败，回退到原始数据")
        return [processed_data], 1, "分割失败，使用单浏览器模式"
    
    reason = f"按利用者分割，{optimal_browser_count}个浏览器处理{total_users}个用户"
    return split_batches, optimal_browser_count, reason