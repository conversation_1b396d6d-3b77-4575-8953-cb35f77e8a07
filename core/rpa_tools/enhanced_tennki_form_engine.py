"""
🆕 增强天気表单引擎 - 数据保护与字段清空修复
修复问题：数据登录流程中字段被意外清空，导致提交失败

修复日期：2025-08-01
修复内容：
- 字段清空根因分析与修复
- 数据完整性保护机制
- 事件监听器优化
- 表单状态管理增强
- 提交前数据验证
"""

import asyncio
import time
from typing import Dict, List, Any, Optional, Set
from playwright.async_api import Page
from logger_config import logger


class EnhancedTennkiFormEngine:
    """🆕 增强表单引擎 - 专注数据保护"""

    def __init__(self, selector_executor, performance_monitor, failed_data_collector=None):
        self.selector_executor = selector_executor
        self.performance_monitor = performance_monitor
        self.failed_data_collector = failed_data_collector
        
        # 🆕 数据保护配置
        self.data_protection = {
            'enabled': True,
            'backup_interval': 5,  # 每5秒备份一次数据
            'validation_before_submit': True,
            'auto_recovery': True,
            'field_change_monitoring': True
        }
        
        # 🆕 字段状态管理
        self.field_states = {
            'protected_fields': {},  # 受保护字段的值
            'field_history': {},     # 字段变更历史
            'last_backup_time': 0,   # 最后备份时间
            'validation_errors': []  # 验证错误列表
        }
        
        # 🆕 问题字段识别
        self.problematic_fields = {
            'time_fields': ['estimationStartTime', 'estimationEndTime', 'serviceStartTime', 'serviceEndTime'],
            'auto_populate_fields': ['estimationEndTime'],  # 容易被自动填充覆盖的字段
            'validation_sensitive_fields': ['userId', 'serviceDate'],  # 验证敏感字段
            'clear_on_change_fields': ['insuranceType']  # 改变时会清空其他字段的字段
        }

    async def process_single_record_with_protection(self, record: Dict, insurance_type: str):
        """🆕 带数据保护的单记录处理"""
        row_index = record.get('row_index', 'unknown')
        
        try:
            logger.info(f"📝 开始处理记录 (行 {row_index}) - 启用增强数据保护")
            
            # 1. 🆕 初始化数据保护
            await self._initialize_record_protection(record)
            
            # 2. 🆕 设置字段监控
            await self._setup_field_monitoring()
            
            # 3. 🆕 执行数据填充（带保护）
            await self._fill_record_data_protected(record, insurance_type)
            
            # 4. 🆕 提交前完整性验证
            await self._validate_before_submit()
            
            # 5. 🆕 执行提交（带重试机制）
            await self._submit_with_retry()
            
            logger.info(f"✅ 记录 (行 {row_index}) 处理成功")
            
        except Exception as e:
            logger.error(f"❌ 记录 (行 {row_index}) 处理失败: {e}")
            
            # 🆕 失败时尝试数据恢复
            await self._attempt_data_recovery()
            raise
        finally:
            # 🆕 清理保护状态
            await self._cleanup_record_protection()

    async def _initialize_record_protection(self, record: Dict):
        """🆕 初始化记录保护机制"""
        try:
            page = self.selector_executor.page
            
            # 清空之前的保护状态
            self.field_states['protected_fields'].clear()
            self.field_states['field_history'].clear()
            self.field_states['validation_errors'].clear()
            
            # 🆕 注入增强数据保护脚本
            await page.evaluate("""
                () => {
                    // 🆕 全局数据保护对象
                    window.TENNKI_DATA_PROTECTION = {
                        enabled: true,
                        protectedData: new Map(),
                        fieldHistory: new Map(),
                        changeListeners: new Map(),
                        backupData: new Map(),
                        lastBackupTime: Date.now(),
                        
                        // 🆕 备份当前表单数据
                        backupFormData: function() {
                            const formData = new Map();
                            const fields = document.querySelectorAll('#registModal input, #registModal select, #registModal textarea');
                            
                            fields.forEach(field => {
                                const fieldId = field.id || field.name || field.getAttribute('data-field-id');
                                if (fieldId && field.value) {
                                    formData.set(fieldId, {
                                        value: field.value,
                                        timestamp: Date.now(),
                                        type: field.type || field.tagName.toLowerCase()
                                    });
                                }
                            });
                            
                            this.backupData = formData;
                            this.lastBackupTime = Date.now();
                            console.log('🛡️ 表单数据已备份:', formData.size, '个字段');
                            return formData;
                        },
                        
                        // 🆕 恢复表单数据
                        restoreFormData: function() {
                            let restoredCount = 0;
                            
                            this.backupData.forEach((data, fieldId) => {
                                const field = document.querySelector(`#${fieldId}, [name="${fieldId}"], [data-field-id="${fieldId}"]`);
                                if (field && (!field.value || field.value.trim() === '')) {
                                    field.value = data.value;
                                    
                                    // 触发必要的事件
                                    field.dispatchEvent(new Event('input', { bubbles: true }));
                                    field.dispatchEvent(new Event('change', { bubbles: true }));
                                    
                                    restoredCount++;
                                    console.log('🔄 恢复字段数据:', fieldId, '=', data.value);
                                }
                            });
                            
                            console.log('✅ 恢复了', restoredCount, '个字段的数据');
                            return restoredCount;
                        },
                        
                        // 🆕 监控字段变化
                        monitorFieldChanges: function() {
                            const fields = document.querySelectorAll('#registModal input, #registModal select, #registModal textarea');
                            
                            fields.forEach(field => {
                                const fieldId = field.id || field.name || field.getAttribute('data-field-id');
                                if (!fieldId) return;
                                
                                // 移除旧的监听器
                                if (this.changeListeners.has(fieldId)) {
                                    const oldListener = this.changeListeners.get(fieldId);
                                    field.removeEventListener('input', oldListener);
                                    field.removeEventListener('change', oldListener);
                                }
                                
                                // 🆕 创建新的监听器
                                const changeListener = (event) => {
                                    const newValue = event.target.value;
                                    const oldValue = this.protectedData.get(fieldId);
                                    
                                    // 记录变化历史
                                    if (!this.fieldHistory.has(fieldId)) {
                                        this.fieldHistory.set(fieldId, []);
                                    }
                                    
                                    this.fieldHistory.get(fieldId).push({
                                        oldValue: oldValue,
                                        newValue: newValue,
                                        timestamp: Date.now(),
                                        eventType: event.type
                                    });
                                    
                                    // 🆕 检测意外清空
                                    if (oldValue && oldValue.trim() !== '' && (!newValue || newValue.trim() === '')) {
                                        console.warn('⚠️ 检测到字段被意外清空:', fieldId, '原值:', oldValue);
                                        
                                        // 🆕 自动恢复机制
                                        if (window.TENNKI_DATA_PROTECTION.enabled) {
                                            setTimeout(() => {
                                                if (!event.target.value || event.target.value.trim() === '') {
                                                    event.target.value = oldValue;
                                                    event.target.dispatchEvent(new Event('input', { bubbles: true }));
                                                    console.log('🔄 自动恢复被清空的字段:', fieldId);
                                                }
                                            }, 100);
                                        }
                                    }
                                    
                                    // 更新保护数据
                                    this.protectedData.set(fieldId, newValue);
                                };
                                
                                // 添加监听器
                                field.addEventListener('input', changeListener);
                                field.addEventListener('change', changeListener);
                                this.changeListeners.set(fieldId, changeListener);
                                
                                // 初始化保护数据
                                if (field.value) {
                                    this.protectedData.set(fieldId, field.value);
                                }
                            });
                            
                            console.log('🛡️ 字段变化监控已启动:', fields.length, '个字段');
                        },
                        
                        // 🆕 拦截可能导致字段清空的函数
                        interceptClearingFunctions: function() {
                            // 拦截 populateEstimationEndTime 函数
                            if (window.populateEstimationEndTime) {
                                const originalFunction = window.populateEstimationEndTime;
                                window.populateEstimationEndTime = function() {
                                    const endTimeField = document.querySelector('#estimationEndTime');
                                    const hasUserInput = endTimeField && endTimeField.value && endTimeField.value.trim() !== '';
                                    
                                    if (hasUserInput) {
                                        console.log('🛡️ 拦截自动时间填充，保护用户输入');
                                        return;
                                    }
                                    
                                    return originalFunction.apply(this, arguments);
                                };
                            }
                            
                            // 🆕 拦截表单重置函数
                            const originalReset = HTMLFormElement.prototype.reset;
                            HTMLFormElement.prototype.reset = function() {
                                const registModal = document.querySelector('#registModal');
                                if (registModal && registModal.contains(this)) {
                                    console.warn('🛡️ 拦截表单重置，保护数据');
                                    return false;
                                }
                                return originalReset.apply(this, arguments);
                            };
                            
                            // 🆕 拦截 jQuery 的 val('') 调用
                            if (window.jQuery) {
                                const originalVal = window.jQuery.fn.val;
                                window.jQuery.fn.val = function(value) {
                                    if (arguments.length > 0 && value === '') {
                                        const element = this[0];
                                        if (element && document.querySelector('#registModal').contains(element)) {
                                            console.warn('🛡️ 拦截 jQuery val(\'\') 调用，保护字段数据');
                                            return this;
                                        }
                                    }
                                    return originalVal.apply(this, arguments);
                                };
                            }
                            
                            console.log('🛡️ 字段清空函数拦截已启动');
                        }
                    };
                    
                    // 🆕 启动保护机制
                    window.TENNKI_DATA_PROTECTION.monitorFieldChanges();
                    window.TENNKI_DATA_PROTECTION.interceptClearingFunctions();
                    window.TENNKI_DATA_PROTECTION.backupFormData();
                    
                    console.log('🛡️ 增强数据保护机制已初始化');
                }
            """)
            
            logger.debug("🛡️ 记录保护机制初始化完成")
            
        except Exception as e:
            logger.warning(f"⚠️ 初始化记录保护失败: {e}")

    async def _setup_field_monitoring(self):
        """🆕 设置字段监控"""
        try:
            page = self.selector_executor.page
            
            # 🆕 启动定期备份任务
            await page.evaluate(f"""
                () => {{
                    if (window.TENNKI_DATA_PROTECTION) {{
                        // 每{self.data_protection['backup_interval']}秒备份一次
                        setInterval(() => {{
                            window.TENNKI_DATA_PROTECTION.backupFormData();
                        }}, {self.data_protection['backup_interval'] * 1000});
                        
                        console.log('🛡️ 定期备份任务已启动 (间隔: {self.data_protection['backup_interval']}秒)');
                    }}
                }}
            """)
            
        except Exception as e:
            logger.warning(f"⚠️ 设置字段监控失败: {e}")

    async def _fill_record_data_protected(self, record: Dict, insurance_type: str):
        """🆕 受保护的数据填充"""
        try:
            page = self.selector_executor.page
            
            # 1. 🆕 填充前备份
            await page.evaluate("window.TENNKI_DATA_PROTECTION && window.TENNKI_DATA_PROTECTION.backupFormData()")
            
            # 2. 按字段类型分组填充，减少相互干扰
            await self._fill_basic_fields(record)
            await asyncio.sleep(0.5)  # 短暂等待，让页面稳定
            
            await self._fill_time_fields(record)
            await asyncio.sleep(0.5)
            
            await self._fill_insurance_fields(record, insurance_type)
            await asyncio.sleep(0.5)
            
            await self._fill_service_fields(record)
            
            # 3. 🆕 填充后验证
            await self._verify_filled_data(record)
            
        except Exception as e:
            logger.error(f"❌ 受保护数据填充失败: {e}")
            
            # 🆕 尝试恢复数据
            await page.evaluate("window.TENNKI_DATA_PROTECTION && window.TENNKI_DATA_PROTECTION.restoreFormData()")
            raise

    async def _fill_basic_fields(self, record: Dict):
        """填充基础字段"""
        try:
            page = self.selector_executor.page
            
            # 用户选择
            if 'user_name' in record:
                await self._safe_fill_field('#userId', record['user_name'], '用户')
            
            # 服务日期
            if 'service_date' in record:
                await self._safe_fill_field('#serviceDate', record['service_date'], '服务日期')
                
        except Exception as e:
            logger.warning(f"⚠️ 填充基础字段失败: {e}")

    async def _fill_time_fields(self, record: Dict):
        """🆕 安全填充时间字段"""
        try:
            # 时间字段需要特别小心，容易被自动函数覆盖
            time_fields = [
                ('estimation_start_time', '#estimationStartTime', '预计开始时间'),
                ('estimation_end_time', '#estimationEndTime', '预计结束时间'),
                ('service_start_time', '#serviceStartTime', '实际开始时间'),
                ('service_end_time', '#serviceEndTime', '实际结束时间')
            ]
            
            for field_key, selector, field_name in time_fields:
                if field_key in record:
                    await self._safe_fill_time_field(selector, record[field_key], field_name)
                    await asyncio.sleep(0.2)  # 时间字段间稍作等待
                    
        except Exception as e:
            logger.warning(f"⚠️ 填充时间字段失败: {e}")

    async def _safe_fill_time_field(self, selector: str, value: str, field_name: str):
        """🆕 安全填充时间字段"""
        try:
            page = self.selector_executor.page
            
            # 🆕 特殊处理时间字段，防止被自动函数覆盖
            await page.evaluate(f"""
                (selector, value, fieldName) => {{
                    const field = document.querySelector(selector);
                    if (field) {{
                        // 🆕 临时禁用自动填充函数
                        const originalPopulate = window.populateEstimationEndTime;
                        window.populateEstimationEndTime = () => {{}};
                        
                        // 填充值
                        field.value = value;
                        field.dispatchEvent(new Event('input', {{ bubbles: true }}));
                        field.dispatchEvent(new Event('change', {{ bubbles: true }}));
                        
                        // 🆕 标记为用户输入，防止被覆盖
                        field.setAttribute('data-user-input', 'true');
                        field.setAttribute('data-protected', 'true');
                        
                        // 延迟恢复自动填充函数
                        setTimeout(() => {{
                            window.populateEstimationEndTime = originalPopulate;
                        }}, 1000);
                        
                        console.log('🛡️ 安全填充时间字段:', fieldName, '=', value);
                        return true;
                    }}
                    return false;
                }}
            """, selector, value, field_name)
            
            logger.debug(f"🛡️ 安全填充时间字段: {field_name} = {value}")
            
        except Exception as e:
            logger.warning(f"⚠️ 安全填充时间字段失败 {field_name}: {e}")

    async def _safe_fill_field(self, selector: str, value: str, field_name: str):
        """🆕 安全填充字段（通用）"""
        try:
            page = self.selector_executor.page
            
            # 🆕 安全填充策略
            success = await page.evaluate(f"""
                (selector, value, fieldName) => {{
                    const field = document.querySelector(selector);
                    if (!field) return false;
                    
                    // 🆕 备份原值
                    const originalValue = field.value;
                    
                    // 填充新值
                    field.value = value;
                    
                    // 🆕 触发事件，但监控是否被意外清空
                    field.dispatchEvent(new Event('input', {{ bubbles: true }}));
                    field.dispatchEvent(new Event('change', {{ bubbles: true }}));
                    
                    // 🆕 验证填充是否成功
                    setTimeout(() => {{
                        if (field.value !== value) {{
                            console.warn('⚠️ 字段值被意外修改:', fieldName, '期望:', value, '实际:', field.value);
                            // 尝试恢复
                            field.value = value;
                            field.dispatchEvent(new Event('input', {{ bubbles: true }}));
                        }}
                    }}, 100);
                    
                    console.log('✅ 安全填充字段:', fieldName, '=', value);
                    return true;
                }}
            """, selector, value, field_name)
            
            if success:
                logger.debug(f"✅ 安全填充字段: {field_name} = {value}")
            else:
                logger.warning(f"⚠️ 字段填充失败: {field_name}")
                
        except Exception as e:
            logger.warning(f"⚠️ 安全填充字段失败 {field_name}: {e}")

    async def _fill_insurance_fields(self, record: Dict, insurance_type: str):
        """填充保险相关字段"""
        try:
            # 保险类型选择（这个字段变化可能会清空其他字段）
            if insurance_type:
                await self._safe_select_insurance_type(insurance_type)
                await asyncio.sleep(1)  # 等待页面响应保险类型变化
                
        except Exception as e:
            logger.warning(f"⚠️ 填充保险字段失败: {e}")

    async def _safe_select_insurance_type(self, insurance_type: str):
        """🆕 安全选择保险类型"""
        try:
            page = self.selector_executor.page
            
            # 🆕 保险类型选择前备份所有数据
            await page.evaluate("window.TENNKI_DATA_PROTECTION && window.TENNKI_DATA_PROTECTION.backupFormData()")
            
            # 选择保险类型
            await page.evaluate(f"""
                (insuranceType) => {{
                    const insuranceSelect = document.querySelector('#insuranceType');
                    if (insuranceSelect) {{
                        insuranceSelect.value = insuranceType;
                        insuranceSelect.dispatchEvent(new Event('change', {{ bubbles: true }}));
                        console.log('✅ 选择保险类型:', insuranceType);
                        return true;
                    }}
                    return false;
                }}
            """, insurance_type)
            
            # 🆕 选择后检查是否有字段被清空，如有则恢复
            await asyncio.sleep(0.5)
            restored_count = await page.evaluate("""
                () => {
                    return window.TENNKI_DATA_PROTECTION ? window.TENNKI_DATA_PROTECTION.restoreFormData() : 0;
                }
            """)
            
            if restored_count > 0:
                logger.info(f"🔄 保险类型选择后恢复了 {restored_count} 个被清空的字段")
                
        except Exception as e:
            logger.warning(f"⚠️ 安全选择保险类型失败: {e}")

    async def _fill_service_fields(self, record: Dict):
        """填充服务相关字段"""
        try:
            # 服务内容等其他字段
            service_fields = [
                ('service_content', '#serviceContent', '服务内容'),
                ('remarks', '#remarks', '备注')
            ]
            
            for field_key, selector, field_name in service_fields:
                if field_key in record:
                    await self._safe_fill_field(selector, record[field_key], field_name)
                    await asyncio.sleep(0.1)
                    
        except Exception as e:
            logger.warning(f"⚠️ 填充服务字段失败: {e}")

    async def _verify_filled_data(self, record: Dict):
        """🆕 验证填充的数据"""
        try:
            page = self.selector_executor.page
            
            # 获取当前表单数据
            current_data = await page.evaluate("""
                () => {
                    const data = {};
                    const fields = document.querySelectorAll('#registModal input, #registModal select, #registModal textarea');
                    
                    fields.forEach(field => {
                        const fieldId = field.id || field.name;
                        if (fieldId) {
                            data[fieldId] = field.value || '';
                        }
                    });
                    
                    return data;
                }
            """)
            
            # 🆕 验证关键字段
            missing_fields = []
            for key, expected_value in record.items():
                if key in ['user_name', 'service_date', 'estimation_start_time', 'estimation_end_time']:
                    field_id = self._get_field_id_for_key(key)
                    if field_id and current_data.get(field_id, '') != expected_value:
                        missing_fields.append(f"{key}({field_id}): 期望'{expected_value}', 实际'{current_data.get(field_id, '')}'")
            
            if missing_fields:
                logger.warning(f"⚠️ 数据验证发现问题: {missing_fields}")
                
                # 🆕 尝试修复缺失的数据
                await self._fix_missing_data(record, missing_fields)
            else:
                logger.debug("✅ 数据填充验证通过")
                
        except Exception as e:
            logger.warning(f"⚠️ 验证填充数据失败: {e}")

    def _get_field_id_for_key(self, key: str) -> str:
        """获取字段对应的ID"""
        field_mapping = {
            'user_name': 'userId',
            'service_date': 'serviceDate',
            'estimation_start_time': 'estimationStartTime',
            'estimation_end_time': 'estimationEndTime',
            'service_start_time': 'serviceStartTime',
            'service_end_time': 'serviceEndTime'
        }
        return field_mapping.get(key, key)

    async def _fix_missing_data(self, record: Dict, missing_fields: List[str]):
        """🆕 修复缺失的数据"""
        try:
            logger.info(f"🔧 尝试修复缺失数据: {len(missing_fields)} 个字段")
            
            for missing_field in missing_fields:
                # 解析缺失字段信息
                if '(' in missing_field and ')' in missing_field:
                    key = missing_field.split('(')[0]
                    field_id = missing_field.split('(')[1].split(')')[0]
                    
                    if key in record:
                        await self._safe_fill_field(f'#{field_id}', record[key], key)
                        await asyncio.sleep(0.2)
            
            logger.info("🔧 数据修复尝试完成")
            
        except Exception as e:
            logger.warning(f"⚠️ 修复缺失数据失败: {e}")

    async def _validate_before_submit(self):
        """🆕 提交前完整性验证"""
        try:
            page = self.selector_executor.page
            
            # 🆕 最终数据完整性检查
            validation_result = await page.evaluate("""
                () => {
                    const result = {
                        isValid: true,
                        errors: [],
                        warnings: [],
                        fieldCount: 0,
                        filledCount: 0
                    };
                    
                    const requiredFields = [
                        { id: 'userId', name: '用户' },
                        { id: 'serviceDate', name: '服务日期' },
                        { id: 'estimationStartTime', name: '预计开始时间' },
                        { id: 'estimationEndTime', name: '预计结束时间' }
                    ];
                    
                    requiredFields.forEach(fieldInfo => {
                        const field = document.querySelector('#' + fieldInfo.id);
                        result.fieldCount++;
                        
                        if (field) {
                            if (field.value && field.value.trim() !== '') {
                                result.filledCount++;
                            } else {
                                result.errors.push(fieldInfo.name + '未填写');
                                result.isValid = false;
                            }
                        } else {
                            result.errors.push(fieldInfo.name + '字段不存在');
                            result.isValid = false;
                        }
                    });
                    
                    // 🆕 检查时间逻辑
                    const startTime = document.querySelector('#estimationStartTime')?.value;
                    const endTime = document.querySelector('#estimationEndTime')?.value;
                    
                    if (startTime && endTime && startTime >= endTime) {
                        result.warnings.push('开始时间不应晚于结束时间');
                    }
                    
                    return result;
                }
            """)
            
            if not validation_result['isValid']:
                error_msg = f"提交前验证失败: {validation_result['errors']}"
                logger.error(f"❌ {error_msg}")
                raise Exception(error_msg)
            
            if validation_result['warnings']:
                logger.warning(f"⚠️ 提交前警告: {validation_result['warnings']}")
            
            logger.info(f"✅ 提交前验证通过 ({validation_result['filledCount']}/{validation_result['fieldCount']} 字段已填写)")
            
        except Exception as e:
            logger.error(f"❌ 提交前验证失败: {e}")
            raise

    async def _submit_with_retry(self):
        """🆕 带重试机制的提交"""
        max_retries = 3
        retry_count = 0
        
        while retry_count < max_retries:
            try:
                logger.info(f"📤 尝试提交数据 (第 {retry_count + 1} 次)")
                
                # 🆕 提交前最后一次数据备份
                page = self.selector_executor.page
                await page.evaluate("window.TENNKI_DATA_PROTECTION && window.TENNKI_DATA_PROTECTION.backupFormData()")
                
                # 执行提交
                submit_button = await page.query_selector('#submitButton, .btn-submit, [type="submit"]')
                if submit_button:
                    await submit_button.click()
                    
                    # 等待提交结果
                    await asyncio.sleep(2)
                    
                    # 🆕 检查提交是否成功
                    success = await self._check_submit_success()
                    
                    if success:
                        logger.info("✅ 数据提交成功")
                        return True
                    else:
                        logger.warning(f"⚠️ 提交失败，准备重试 (第 {retry_count + 1} 次)")
                        retry_count += 1
                        
                        if retry_count < max_retries:
                            # 🆕 重试前恢复数据
                            await page.evaluate("window.TENNKI_DATA_PROTECTION && window.TENNKI_DATA_PROTECTION.restoreFormData()")
                            await asyncio.sleep(1)
                        
                else:
                    raise Exception("找不到提交按钮")
                    
            except Exception as e:
                retry_count += 1
                logger.warning(f"⚠️ 提交失败 (第 {retry_count} 次): {e}")
                
                if retry_count >= max_retries:
                    logger.error(f"❌ 提交失败，已达到最大重试次数 ({max_retries})")
                    raise
                
                # 重试前等待
                await asyncio.sleep(2)
        
        return False

    async def _check_submit_success(self) -> bool:
        """🆕 检查提交是否成功"""
        try:
            page = self.selector_executor.page
            
            # 检查成功指示器
            success_indicators = await page.evaluate("""
                () => {
                    // 检查成功消息
                    const successMessages = document.querySelectorAll('.success, .alert-success, .toast-success');
                    const hasSuccessMessage = Array.from(successMessages).some(el =>
                        el.offsetParent !== null && el.textContent.includes('成功')
                    );
                    
                    // 检查模态框是否关闭
                    const modal = document.querySelector('#registModal');
                    const modalClosed = !modal || modal.style.display === 'none' || modal.offsetParent === null;
                    
                    // 检查错误消息
                    const errorMessages = document.querySelectorAll('.error, .alert-danger, .toast-error');
                    const hasErrorMessage = Array.from(errorMessages).some(el =>
                        el.offsetParent !== null && el.textContent.trim() !== ''
                    );
                    
                    return {
                        hasSuccessMessage: hasSuccessMessage,
                        modalClosed: modalClosed,
                        hasErrorMessage: hasErrorMessage,
                        success: hasSuccessMessage || (modalClosed && !hasErrorMessage)
                    };
                }
            """)
            
            if success_indicators['success']:
                logger.debug("✅ 提交成功确认")
                return True
            elif success_indicators['hasErrorMessage']:
                logger.warning("⚠️ 检测到错误消息，提交可能失败")
                return False
            else:
                logger.debug("🤔 提交状态不明确，需要进一步检查")
                return False
                
        except Exception as e:
            logger.warning(f"⚠️ 检查提交状态失败: {e}")
            return False

    async def _attempt_data_recovery(self):
        """🆕 尝试数据恢复"""
        try:
            page = self.selector_executor.page
            
            logger.info("🔄 尝试数据恢复...")
            
            # 尝试从备份恢复数据
            restored_count = await page.evaluate("""
                () => {
                    if (window.TENNKI_DATA_PROTECTION) {
                        return window.TENNKI_DATA_PROTECTION.restoreFormData();
                    }
                    return 0;
                }
            """)
            
            if restored_count > 0:
                logger.info(f"🔄 数据恢复成功，恢复了 {restored_count} 个字段")
            else:
                logger.warning("⚠️ 无法恢复数据，可能没有备份")
                
        except Exception as e:
            logger.warning(f"⚠️ 数据恢复失败: {e}")

    async def _cleanup_record_protection(self):
        """🆕 清理记录保护状态"""
        try:
            page = self.selector_executor.page
            
            # 清理保护状态
            await page.evaluate("""
                () => {
                    if (window.TENNKI_DATA_PROTECTION) {
                        window.TENNKI_DATA_PROTECTION.enabled = false;
                        window.TENNKI_DATA_PROTECTION.protectedData.clear();
                        window.TENNKI_DATA_PROTECTION.fieldHistory.clear();
                        window.TENNKI_DATA_PROTECTION.backupData.clear();
                        console.log('🔒 数据保护状态已清理');
                    }
                }
            """)
            
            # 清理本地状态
            self.field_states['protected_fields'].clear()
            self.field_states['field_history'].clear()
            self.field_states['validation_errors'].clear()
            
            logger.debug("🔒 记录保护状态清理完成")
            
        except Exception as e:
            logger.warning(f"⚠️ 清理保护状态失败: {e}")


class FieldClearingAnalyzer:
    """🆕 字段清空分析器 - 诊断字段清空的根本原因"""

    def __init__(self, page: Page):
        self.page = page
        self.clearing_events = []
        self.monitoring_active = False

    async def start_monitoring(self):
        """开始监控字段清空事件"""
        try:
            self.monitoring_active = True
            
            await self.page.evaluate("""
                () => {
                    window.FIELD_CLEARING_MONITOR = {
                        events: [],
                        
                        // 监控所有可能导致字段清空的事件
                        startMonitoring: function() {
                            // 1. 监控直接的值变化
                            const fields = document.querySelectorAll('#registModal input, #registModal select, #registModal textarea');
                            
                            fields.forEach(field => {
                                const fieldId = field.id || field.name || 'unknown';
                                let lastValue = field.value;
                                
                                // 监控值变化
                                const observer = new MutationObserver((mutations) => {
                                    mutations.forEach((mutation) => {
                                        if (mutation.type === 'attributes' && mutation.attributeName === 'value') {
                                            const newValue = field.value;
                                            if (lastValue && !newValue) {
                                                this.events.push({
                                                    type: 'field_cleared',
                                                    fieldId: fieldId,
                                                    oldValue: lastValue,
                                                    newValue: newValue,
                                                    timestamp: Date.now(),
                                                    stackTrace: new Error().stack
                                                });
                                                console.warn('🔍 字段被清空:', fieldId, '原值:', lastValue);
                                            }
                                            lastValue = newValue;
                                        }
                                    });
                                });
                                
                                observer.observe(field, {
                                    attributes: true,
                                    attributeFilter: ['value']
                                });
                                
                                // 监控事件
                                ['input', 'change', 'blur', 'focus'].forEach(eventType => {
                                    field.addEventListener(eventType, (e) => {
                                        if (lastValue && !e.target.value) {
                                            this.events.push({
                                                type: 'event_clearing',
                                                fieldId: fieldId,
                                                eventType: eventType,
                                                oldValue: lastValue,
                                                timestamp: Date.now(),
                                                target: e.target.tagName + '#' + e.target.id
                                            });
                                        }
                                    });
                                });
                            });
                            
                            // 2. 监控函数调用
                            this.monitorFunctionCalls();
                            
                            // 3. 监控DOM操作
                            this.monitorDOMOperations();
                            
                            console.log('🔍 字段清空监控已启动');
                        },
                        
                        monitorFunctionCalls: function() {
                            // 监控可能清空字段的函数
                            const suspiciousFunctions = [
                                'populateEstimationEndTime',
                                'clearForm',
                                'resetForm',
                                'validateForm'
                            ];
                            
                            suspiciousFunctions.forEach(funcName => {
                                if (window[funcName]) {
                                    const originalFunc = window[funcName];
                                    window[funcName] = (...args) => {
                                        this.events.push({
                                            type: 'function_call',
                                            functionName: funcName,
                                            arguments: args,
                                            timestamp: Date.now(),
                                            stackTrace: new Error().stack
                                        });
                                        console.log('🔍 监控到函数调用:', funcName);
                                        return originalFunc.apply(this, args);
                                    };
                                }
                            });
                        },
                        
                        monitorDOMOperations: function() {
                            // 监控可能影响表单的DOM操作
                            const originalInnerHTML = Object.getOwnPropertyDescriptor(Element.prototype, 'innerHTML');
                            
                            Object.defineProperty(Element.prototype, 'innerHTML', {
                                set: function(value) {
                                    if (this.closest('#registModal')) {
                                        window.FIELD_CLEARING_MONITOR.events.push({
                                            type: 'innerHTML_change',
                                            element: this.tagName + '#' + this.id,
                                            timestamp: Date.now(),
                                            stackTrace: new Error().stack
                                        });
                                        console.log('🔍 监控到innerHTML变化:', this.tagName);
                                    }
                                    return originalInnerHTML.set.call(this, value);
                                },
                                get: originalInnerHTML.get
                            });
                        },
                        
                        getEvents: function() {
                            return this.events;
                        },
                        
                        clearEvents: function() {
                            this.events = [];
                        }
                    };
                    
                    window.FIELD_CLEARING_MONITOR.startMonitoring();
                }
            """)
            
            logger.info("🔍 字段清空监控已启动")
            
        except Exception as e:
            logger.error(f"❌ 启动字段清空监控失败: {e}")

    async def get_clearing_events(self) -> List[Dict]:
        """获取字段清空事件"""
        try:
            if not self.monitoring_active:
                return []
            
            events = await self.page.evaluate("""
                () => {
                    return window.FIELD_CLEARING_MONITOR ? window.FIELD_CLEARING_MONITOR.getEvents() : [];
                }
            """)
            
            return events or []
            
        except Exception as e:
            logger.warning(f"⚠️ 获取清空事件失败: {e}")
            return []

    async def analyze_clearing_patterns(self) -> Dict:
        """分析字段清空模式"""
        try:
            events = await self.get_clearing_events()
            
            if not events:
                return {'status': 'no_events', 'analysis': '未检测到字段清空事件'}
            
            # 分析事件模式
            analysis = {
                'total_events': len(events),
                'event_types': {},
                'affected_fields': {},
                'time_patterns': [],
                'suspected_causes': []
            }
            
            for event in events:
                # 统计事件类型
                event_type = event.get('type', 'unknown')
                analysis['event_types'][event_type] = analysis['event_types'].get(event_type, 0) + 1
                
                # 统计受影响的字段
                field_id = event.get('fieldId', 'unknown')
                if field_id != 'unknown':
                    analysis['affected_fields'][field_id] = analysis['affected_fields'].get(field_id, 0) + 1
                
                # 分析可疑原因
                if event_type == 'function_call':
                    func_name = event.get('functionName', '')
                    if func_name not in analysis['suspected_causes']:
                        analysis['suspected_causes'].append(f"函数调用: {func_name}")
                
                elif event_type == 'event_clearing':
                    event_name = event.get('eventType', '')
                    cause = f"事件触发: {event_name}"
                    if cause not in analysis['suspected_causes']:
                        analysis['suspected_causes'].append(cause)
            
            # 生成分析报告
            analysis['status'] = 'analyzed'
            analysis['summary'] = f"检测到 {len(events)} 个字段清空事件，涉及 {len(analysis['affected_fields'])} 个字段"
            
            logger.info(f"🔍 字段清空分析完成: {analysis['summary']}")
            return analysis
            
        except Exception as e:
            logger.error(f"❌ 分析字段清空模式失败: {e}")
            return {'status': 'error', 'error': str(e)}

    async def stop_monitoring(self):
        """停止监控"""
        try:
            self.monitoring_active = False
            
            await self.page.evaluate("""
                () => {
                    if (window.FIELD_CLEARING_MONITOR) {
                        window.FIELD_CLEARING_MONITOR = null;
                        console.log('🔒 字段清空监控已停止');
                    }
                }
            """)
            
            logger.info("🔒 字段清空监控已停止")
            
        except Exception as e:
            logger.warning(f"⚠️ 停止字段清空监控失败: {e}")


# 🆕 便捷函数
async def analyze_field_clearing_issues(page: Page, duration_seconds: int = 30) -> Dict:
    """
    🆕 分析字段清空问题的便捷函数
    
    Args:
        page: Playwright页面对象
        duration_seconds: 监控持续时间（秒）
    
    Returns:
        分析结果字典
    """
    analyzer = FieldClearingAnalyzer(page)
    
    try:
        # 启动监控
        await analyzer.start_monitoring()
        
        # 监控指定时间
        logger.info(f"🔍 开始监控字段清空问题，持续 {duration_seconds} 秒...")
        await asyncio.sleep(duration_seconds)
        
        # 分析结果
        analysis = await analyzer.analyze_clearing_patterns()
        
        return analysis
        
    finally:
        # 停止监控
        await analyzer.stop_monitoring()