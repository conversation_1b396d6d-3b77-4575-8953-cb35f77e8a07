"""
工作流管理器
提供配置驱动的多据点RPA工作流支持
"""
import os
import asyncio
from logger_config import logger
from .kaipoke_common import KaipokeRPATools, KaipokeAccountManager
from .data_processor import DataProcessor, FileManager
from .gsuite_integration import GoogleWorkspaceManager


class MultiSiteWorkflowManager:
    """多据点工作流管理器"""
    
    def __init__(self, page, config: dict):
        self.page = page
        self.config = config
        self.workflow_config = config.get('config', {})
        self.tasks = config.get('tasks', [])
        
        # 初始化工具
        self.kaipoke_tools = KaipokeRPATools(page)
        self.account_manager = KaipokeAccountManager()
        self.gsuite_manager = GoogleWorkspaceManager()
        self.data_processor = DataProcessor()
        
        # 工作流设置
        self.login_url = self.workflow_config.get('login_url')
        self.download_path = self.workflow_config.get('download_path', '/tmp/kaipoke_downloads')
        self.gdrive_folder_id = self.workflow_config.get('gdrive_folder_id')
        
        # 确保下载目录存在
        FileManager.ensure_download_directory(self.download_path)
    
    async def execute_workflow(self):
        """执行完整的工作流"""
        logger.info("开始执行多据点RPA工作流")
        
        try:
            # 获取默认账号信息
            default_account = self.account_manager.get_account('default')
            if not default_account:
                logger.error("未找到默认账号配置")
                return False
            
            # 按账号分组任务
            tasks_by_account = KaipokeRPATools.group_tasks_by_account(
                self.tasks,
                default_account['corporation_id'],
                default_account['member_login_id'],
                default_account['password']
            )
            
            # 执行各账号组的任务
            for account_key, account_info in tasks_by_account.items():
                await self._execute_account_tasks(account_key, account_info)
            
            logger.info("✅ 多据点RPA工作流执行完成")
            return True
            
        except Exception as e:
            logger.error(f"❌ 工作流执行失败: {e}", exc_info=True)
            return False
    
    async def _execute_account_tasks(self, account_key: str, account_info: dict):
        """执行特定账号的任务组"""
        corporation_id = account_info['corporation_id']
        member_login_id = account_info['member_login_id']
        password = account_info['password']
        tasks = account_info['tasks']
        
        logger.info(f"=== 执行账号 {corporation_id}/{member_login_id} 的任务 ({len(tasks)}个) ===")
        
        try:
            # 登录到指定账号
            login_success = await self.kaipoke_tools.login_with_account_switch(
                corporation_id, member_login_id, password, self.login_url
            )
            
            if not login_success:
                logger.error(f"账号 {account_key} 登录失败，跳过该组任务")
                return
            
            # 执行该账号的所有任务
            for task_config in tasks:
                await self._execute_single_task(task_config)
                
        except Exception as e:
            logger.error(f"账号 {account_key} 任务执行出错: {e}", exc_info=True)
    
    async def _execute_single_task(self, task_config: dict):
        """执行单个任务"""
        task_id = task_config.get('task_id')
        target = task_config.get('target')
        params = task_config.get('params', {})
        
        if not target:
            logger.warning(f"任务ID '{task_id}' 缺少target定义，跳过")
            return
        
        logger.info(f"--- 开始任务 [{task_id}] ---")
        logger.info(f"目标: {target}")
        
        try:
            # 使用选择器优先下载Kaipoke月度报告
            downloaded_file = await self.kaipoke_tools.download_monthly_report_with_selectors(
                params, self.download_path
            )
            
            if downloaded_file and os.path.exists(downloaded_file):
                # 处理下载的文件
                processed_file = await self._process_downloaded_file(downloaded_file, params)
                
                # 上传到Google Drive
                if self.gdrive_folder_id:
                    upload_success = self.gsuite_manager.upload_to_drive(
                        processed_file or downloaded_file, 
                        self.gdrive_folder_id
                    )
                    
                    if upload_success:
                        logger.info(f"✅ 任务 '{task_id}' 完成: 文件已上传到Google Drive")
                    else:
                        logger.error(f"❌ 任务 '{task_id}' 失败: Google Drive上传失败")
                else:
                    logger.info(f"✅ 任务 '{task_id}' 完成: 文件已下载: {downloaded_file}")
                    
                # 处理Google Sheets更新
                await self._process_sheets_updates(params, processed_file or downloaded_file)
                
            else:
                logger.warning(f"⚠️ 任务 '{task_id}': 选择器下载失败，执行Agent后备方案")
                await self._call_agent_fallback(task_config)
                
        except Exception as e:
            logger.error(f"❌ 任务 '{task_id}' 执行出错: {e}", exc_info=True)
            # 执行Agent后备方案
            logger.info(f"🤖 任务 '{task_id}': 错误后执行Agent后备方案")
            await self._call_agent_fallback(task_config)
    
    async def _process_downloaded_file(self, file_path: str, params: dict):
        """处理下载的文件"""
        try:
            # 检查是否需要文件名处理
            output_filename_pattern = params.get('output_filename_pattern')
            service_center_name = params.get('service_center_name')
            
            if output_filename_pattern:
                # 如果是CSV文件，转换为Excel
                if file_path.endswith('.csv'):
                    processed_file = self.data_processor.process_csv_to_excel(
                        os.path.dirname(file_path),
                        output_filename_pattern
                    )
                    return processed_file
                else:
                    # 重命名Excel文件
                    new_filename = self.data_processor.generate_filename_with_month(
                        output_filename_pattern, service_center_name
                    )
                    new_path = os.path.join(os.path.dirname(file_path), new_filename)
                    os.rename(file_path, new_path)
                    logger.info(f"文件重命名: {new_filename}")
                    return new_path
            
            return file_path
            
        except Exception as e:
            logger.error(f"文件处理失败: {e}")
            return file_path
    
    async def _process_sheets_updates(self, params: dict, source_file: str):
        """处理Google Sheets更新"""
        try:
            data_processing_rules = params.get('data_processing_rules', [])
            
            if data_processing_rules:
                logger.info("开始处理Google Sheets数据更新")
                self.gsuite_manager.update_sheet_data(
                    None,  # sheet_id将从规则中获取
                    data_processing_rules,
                    source_file
                )
                
        except Exception as e:
            logger.error(f"Google Sheets更新失败: {e}")
    
    async def _call_agent_fallback(self, task_config: dict):
        """调用Agent后备方案"""
        logger.info("执行Agent后备方案")
        
        # 动态导入避免循环依赖
        from agents.web_operator_agent import website_operator
        from crewai import Task, Crew
        
        task_id = task_config.get('task_id')
        target = task_config.get('target')
        params = task_config.get('params', {})
        service_center_name = params.get('service_center_name')
        element_text = params.get('element_text')
        
        task = Task(
            description=f"""
            在Kaipoke系统中执行以下步骤下载月度实绩数据：
            1. 点击实绩管理菜单
            2. 点击月度实绩一览
            3. 选择目标事业所「{service_center_name}」（{element_text}）
            4. 选择前月
            5. 点击检索按钮
            6. 点击Excel输出按钮下载文件
            7. 将下载的文件上传到Google Drive
            
            任务ID: {task_id}
            目标: {target}
            """,
            agent=website_operator,
            expected_output="月度实绩数据（Excel文件）正常下载并上传到Google Drive的指定文件夹。"
        )
        
        crew = Crew(
            agents=[website_operator],
            tasks=[task],
            verbose=True
        )
        
        try:
            result = crew.kickoff()
            logger.info(f"Agent后备方案执行完成: {result}")
        except Exception as e:
            logger.error(f"Agent后备方案执行失败: {e}")


class ConfigDrivenWorkflow:
    """配置驱动的工作流"""
    
    @staticmethod
    def create_workflow_from_config(page, config_file_path: str):
        """从配置文件创建工作流"""
        try:
            import yaml
            
            with open(config_file_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            return MultiSiteWorkflowManager(page, config)
            
        except Exception as e:
            logger.error(f"从配置文件创建工作流失败: {e}")
            return None
    
    @staticmethod
    def validate_config(config: dict):
        """验证配置文件"""
        required_fields = ['config', 'tasks']
        
        for field in required_fields:
            if field not in config:
                logger.error(f"配置文件缺少必需字段: {field}")
                return False
        
        workflow_config = config.get('config', {})
        required_workflow_fields = ['login_url']
        
        for field in required_workflow_fields:
            if field not in workflow_config:
                logger.error(f"工作流配置缺少必需字段: {field}")
                return False
        
        tasks = config.get('tasks', [])
        if not tasks:
            logger.warning("配置文件中没有定义任务")
        
        for i, task in enumerate(tasks):
            if 'task_id' not in task:
                logger.error(f"任务 {i} 缺少task_id")
                return False
            if 'target' not in task:
                logger.error(f"任务 {task.get('task_id')} 缺少target")
                return False
        
        logger.info("✅ 配置文件验证通过")
        return True


class TaskExecutionMonitor:
    """任务执行监控器"""
    
    def __init__(self):
        self.execution_log = []
        self.start_time = None
        self.end_time = None
    
    def start_monitoring(self):
        """开始监控"""
        from datetime import datetime
        self.start_time = datetime.now()
        logger.info("开始任务执行监控")
    
    def log_task_result(self, task_id: str, success: bool, error_msg: str = None):
        """记录任务结果"""
        from datetime import datetime
        
        self.execution_log.append({
            'task_id': task_id,
            'success': success,
            'error_msg': error_msg,
            'timestamp': datetime.now()
        })
    
    def end_monitoring(self):
        """结束监控"""
        from datetime import datetime
        self.end_time = datetime.now()
        
        total_tasks = len(self.execution_log)
        successful_tasks = sum(1 for log in self.execution_log if log['success'])
        failed_tasks = total_tasks - successful_tasks
        
        duration = self.end_time - self.start_time if self.start_time else None
        
        logger.info("=== 任务执行监控报告 ===")
        logger.info(f"总任务数: {total_tasks}")
        logger.info(f"成功任务: {successful_tasks}")
        logger.info(f"失败任务: {failed_tasks}")
        if duration:
            logger.info(f"执行时间: {duration}")
        
        if failed_tasks > 0:
            logger.warning("失败任务详情:")
            for log in self.execution_log:
                if not log['success']:
                    logger.warning(f"  - {log['task_id']}: {log['error_msg']}")
        
        return {
            'total': total_tasks,
            'successful': successful_tasks,
            'failed': failed_tasks,
            'duration': duration,
            'logs': self.execution_log
        }