"""
🆕 增强天気数据分割器 - 智能浏览器数量计算
修复问题：根据数据量动态计算最优浏览器数量，避免空闲浏览器误操作

修复日期：2025-08-01
修复内容：
- 智能浏览器数量计算算法
- 数据量评估机制
- 动态分割策略
- 负载均衡优化
"""

import math
from typing import List, Dict, Any, Tuple
from logger_config import logger


class EnhancedTennkiDataSplitter:
    """🆕 增强数据分割器 - 智能浏览器管理"""

    def __init__(self, max_records_per_batch: int = 30, max_users_per_batch: int = 10):
        self.max_records_per_batch = max_records_per_batch
        self.max_users_per_batch = max_users_per_batch
        
        # 🆕 智能分割配置
        self.performance_thresholds = {
            'light_load': {'records': 50, 'users': 15, 'browsers': 1},
            'medium_load': {'records': 150, 'users': 30, 'browsers': 2},
            'heavy_load': {'records': 300, 'users': 50, 'browsers': 3},
            'max_load': {'records': 500, 'users': 80, 'browsers': 4}
        }
        
        # 🆕 负载均衡参数
        self.load_balance_factor = 0.8  # 负载均衡因子，避免某个浏览器过载
        self.min_records_per_browser = 10  # 每个浏览器最少处理记录数
        self.min_users_per_browser = 3    # 每个浏览器最少处理用户数

    def calculate_smart_browser_count(self, processed_data: List[Dict]) -> Tuple[int, str]:
        """🆕 智能计算最优浏览器数量"""
        total_records = sum(user.get('total_records', 0) for user in processed_data)
        total_users = len(processed_data)
        
        logger.info(f"📊 数据量分析: {total_records}条记录, {total_users}个用户")
        
        # 1. 基于数据量的初步评估
        load_category = self._assess_load_category(total_records, total_users)
        base_browser_count = self.performance_thresholds[load_category]['browsers']
        
        # 2. 🆕 精确计算：确保每个浏览器有足够的工作量
        records_based_count = max(1, math.ceil(total_records / (self.max_records_per_batch * 2)))
        users_based_count = max(1, math.ceil(total_users / self.min_users_per_browser))
        
        # 3. 🆕 负载均衡优化
        optimal_count = min(base_browser_count, records_based_count, users_based_count)
        
        # 4. 🆕 最终验证：确保不会有空闲浏览器
        if optimal_count > 1:
            avg_records_per_browser = total_records / optimal_count
            avg_users_per_browser = total_users / optimal_count
            
            if avg_records_per_browser < self.min_records_per_browser or avg_users_per_browser < self.min_users_per_browser:
                optimal_count = max(1, optimal_count - 1)
                logger.info(f"🔧 调整浏览器数量以避免空闲: {optimal_count}")
        
        reason = f"{load_category}负载，平均每浏览器{total_records//optimal_count}条记录"
        
        logger.info(f"✅ 智能浏览器数量计算结果: {optimal_count} ({reason})")
        return optimal_count, reason

    def _assess_load_category(self, total_records: int, total_users: int) -> str:
        """评估负载类别"""
        for category, thresholds in self.performance_thresholds.items():
            if total_records <= thresholds['records'] and total_users <= thresholds['users']:
                return category
        return 'max_load'

    def split_data_with_smart_allocation(self, processed_data: List[Dict], target_browser_count: int) -> List[List[Dict]]:
        """🆕 智能数据分割 - 确保负载均衡"""
        if not processed_data:
            return []
        
        if target_browser_count == 1:
            logger.info("📊 使用单浏览器模式，无需分割")
            return [processed_data]
        
        logger.info(f"🧠 开始智能数据分割: {len(processed_data)}个用户 → {target_browser_count}个批次")
        
        # 1. 按记录数排序，实现负载均衡
        sorted_data = sorted(processed_data, key=lambda x: x.get('total_records', 0), reverse=True)
        
        # 2. 🆕 使用贪心算法分配到各个批次
        batches = [[] for _ in range(target_browser_count)]
        batch_loads = [0] * target_browser_count  # 记录每个批次的负载
        
        for user_data in sorted_data:
            # 找到当前负载最小的批次
            min_load_idx = batch_loads.index(min(batch_loads))
            
            # 检查是否会超过批次限制
            current_batch = batches[min_load_idx]
            current_records = sum(u.get('total_records', 0) for u in current_batch)
            current_users = len(current_batch)
            
            user_records = user_data.get('total_records', 0)
            
            # 如果添加这个用户会超过限制，尝试其他批次
            if (current_records + user_records > self.max_records_per_batch or 
                current_users >= self.max_users_per_batch):
                
                # 寻找可以容纳的批次
                suitable_batch_idx = None
                for i, batch in enumerate(batches):
                    batch_records = sum(u.get('total_records', 0) for u in batch)
                    batch_users = len(batch)
                    
                    if (batch_records + user_records <= self.max_records_per_batch and 
                        batch_users < self.max_users_per_batch):
                        suitable_batch_idx = i
                        break
                
                if suitable_batch_idx is not None:
                    min_load_idx = suitable_batch_idx
                # 如果没有合适的批次，仍然使用负载最小的批次（可能会超过限制）
            
            # 分配用户到选定的批次
            batches[min_load_idx].append(user_data)
            batch_loads[min_load_idx] += user_records
        
        # 3. 🆕 移除空批次
        non_empty_batches = [batch for batch in batches if batch]
        
        # 4. 输出分割结果统计
        logger.info(f"📊 智能分割结果:")
        for i, batch in enumerate(non_empty_batches):
            batch_records = sum(u.get('total_records', 0) for u in batch)
            batch_users = len(batch)
            logger.info(f"   批次 {i+1}: {batch_users}个用户, {batch_records}条记录")
        
        logger.info(f"✅ 数据分割完成: {len(non_empty_batches)} 个有效批次")
        return non_empty_batches

    def validate_split_integrity_enhanced(self, original_data: List[Dict], split_batches: List[List[Dict]]) -> bool:
        """🆕 增强分割完整性验证"""
        try:
            # 1. 基本数量验证
            original_users = len(original_data)
            split_users = sum(len(batch) for batch in split_batches)
            
            if original_users != split_users:
                logger.error(f"❌ 用户数量不匹配: 原始{original_users} vs 分割{split_users}")
                return False
            
            # 2. 记录数量验证
            original_records = sum(user.get('total_records', 0) for user in original_data)
            split_records = sum(
                sum(user.get('total_records', 0) for user in batch) 
                for batch in split_batches
            )
            
            if original_records != split_records:
                logger.error(f"❌ 记录数量不匹配: 原始{original_records} vs 分割{split_records}")
                return False
            
            # 3. 🆕 用户唯一性验证
            original_user_ids = set(user.get('user_id') or user.get('user_name') for user in original_data)
            split_user_ids = set()
            
            for batch in split_batches:
                for user in batch:
                    user_id = user.get('user_id') or user.get('user_name')
                    if user_id in split_user_ids:
                        logger.error(f"❌ 发现重复用户: {user_id}")
                        return False
                    split_user_ids.add(user_id)
            
            if original_user_ids != split_user_ids:
                logger.error(f"❌ 用户ID不匹配")
                return False
            
            # 4. 🆕 批次负载验证
            for i, batch in enumerate(split_batches):
                batch_records = sum(user.get('total_records', 0) for user in batch)
                batch_users = len(batch)
                
                if batch_records == 0:
                    logger.warning(f"⚠️ 批次 {i+1} 没有记录数据")
                
                if batch_users == 0:
                    logger.warning(f"⚠️ 批次 {i+1} 没有用户数据")
            
            logger.info("✅ 增强分割完整性验证通过")
            return True
            
        except Exception as e:
            logger.error(f"❌ 分割完整性验证失败: {e}")
            return False

    def optimize_batch_distribution(self, split_batches: List[List[Dict]]) -> List[List[Dict]]:
        """🆕 优化批次分布 - 进一步负载均衡"""
        if len(split_batches) <= 1:
            return split_batches
        
        logger.info("🔧 开始优化批次分布...")
        
        # 计算每个批次的负载
        batch_loads = []
        for i, batch in enumerate(split_batches):
            load = sum(user.get('total_records', 0) for user in batch)
            batch_loads.append({'index': i, 'load': load, 'users': len(batch)})
        
        # 按负载排序
        batch_loads.sort(key=lambda x: x['load'])
        
        # 🆕 负载均衡：从最重的批次移动用户到最轻的批次
        max_iterations = 5  # 避免无限循环
        iteration = 0
        
        while iteration < max_iterations:
            heaviest = batch_loads[-1]
            lightest = batch_loads[0]
            
            # 如果负载差异不大，停止优化
            load_diff = heaviest['load'] - lightest['load']
            if load_diff < self.min_records_per_browser:
                break
            
            # 尝试从最重批次移动一个用户到最轻批次
            heaviest_batch = split_batches[heaviest['index']]
            lightest_batch = split_batches[lightest['index']]
            
            # 找到最小的用户（记录数最少）
            if heaviest_batch:
                min_user = min(heaviest_batch, key=lambda x: x.get('total_records', 0))
                min_user_records = min_user.get('total_records', 0)
                
                # 检查移动后是否改善负载均衡
                if min_user_records < load_diff / 2:
                    heaviest_batch.remove(min_user)
                    lightest_batch.append(min_user)
                    
                    # 更新负载统计
                    heaviest['load'] -= min_user_records
                    lightest['load'] += min_user_records
                    heaviest['users'] -= 1
                    lightest['users'] += 1
                    
                    # 重新排序
                    batch_loads.sort(key=lambda x: x['load'])
                    
                    logger.debug(f"🔄 移动用户优化负载均衡: 批次{heaviest['index']+1} → 批次{lightest['index']+1}")
                else:
                    break
            else:
                break
            
            iteration += 1
        
        # 输出优化结果
        logger.info("📊 批次分布优化结果:")
        for i, batch in enumerate(split_batches):
            if batch:  # 只显示非空批次
                batch_records = sum(user.get('total_records', 0) for user in batch)
                batch_users = len(batch)
                logger.info(f"   批次 {i+1}: {batch_users}个用户, {batch_records}条记录")
        
        # 移除空批次
        optimized_batches = [batch for batch in split_batches if batch]
        
        logger.info(f"✅ 批次分布优化完成: {len(optimized_batches)} 个有效批次")
        return optimized_batches


def create_enhanced_batch_info_list(split_batches: List[List[Dict]]) -> List[Dict]:
    """🆕 创建增强批次信息列表"""
    batch_info_list = []
    
    for batch_index, batch_data in enumerate(split_batches):
        if not batch_data:  # 跳过空批次
            continue
            
        batch_records = sum(user.get('total_records', 0) for user in batch_data)
        batch_users = len(batch_data)
        
        # 🆕 计算批次优先级（记录数多的优先处理）
        priority = batch_records
        
        # 🆕 估算处理时间（基于记录数和用户数）
        estimated_time = batch_records * 2 + batch_users * 10  # 秒
        
        batch_info = {
            'batch_index': batch_index,
            'batch_data': batch_data,
            'total_records': batch_records,
            'total_users': batch_users,
            'priority': priority,
            'estimated_time': estimated_time,
            'status': 'pending'
        }
        
        batch_info_list.append(batch_info)
        
        logger.debug(f"📦 批次 {batch_index + 1}: {batch_users}用户, {batch_records}记录, 预计{estimated_time}秒")
    
    # 🆕 按优先级排序（记录数多的先处理）
    batch_info_list.sort(key=lambda x: x['priority'], reverse=True)
    
    logger.info(f"📋 创建了 {len(batch_info_list)} 个增强批次信息")
    return batch_info_list


# 🆕 便捷函数：一键智能分割
def smart_split_tennki_data(processed_data: List[Dict], 
                           max_records_per_batch: int = 30, 
                           max_users_per_batch: int = 10) -> Tuple[List[List[Dict]], int, str]:
    """
    🆕 一键智能分割天気数据
    
    Returns:
        Tuple[分割后的批次, 最优浏览器数量, 分割原因]
    """
    splitter = EnhancedTennkiDataSplitter(max_records_per_batch, max_users_per_batch)
    
    # 1. 计算最优浏览器数量
    optimal_browser_count, reason = splitter.calculate_smart_browser_count(processed_data)
    
    # 2. 智能分割数据
    split_batches = splitter.split_data_with_smart_allocation(processed_data, optimal_browser_count)
    
    # 3. 优化批次分布
    optimized_batches = splitter.optimize_batch_distribution(split_batches)
    
    # 4. 验证分割完整性
    if not splitter.validate_split_integrity_enhanced(processed_data, optimized_batches):
        logger.error("❌ 智能分割验证失败，回退到原始数据")
        return [processed_data], 1, "分割失败，使用单浏览器模式"
    
    return optimized_batches, optimal_browser_count, reason