"""
Tennki并行批次处理器
管理同一据点内多个数据批次的并行处理

核心功能：
1. 管理多个浏览器实例
2. 协调批次并行处理
3. 资源分配和负载均衡
4. 失败处理和重试机制

性能目标：4个批次并行处理，总时间减少75%
"""

import asyncio
import time
from typing import List, Dict, Any, Optional
from playwright.async_api import Browser, Page

from logger_config import logger
from core.selector_executor import SelectorExecutor
from core.rpa_tools.kaipoke_login_service import kaipoke_login_with_env
from core.rpa_tools.tennki_form_engine import TennkiFormEngine, TennkiFailedDataCollector
from core.rpa_tools.tennki_performance_monitor import TennkiPerformanceMonitor
from core.rpa_tools.tennki_facility_manager import TennkiFacilityManager
# 移除错误的导入，TennkiBatchInfo不存在，应该使用Dict类型
# from core.rpa_tools.tennki_data_splitter import TennkiBatchInfo


class TennkiBatchProcessor:
    """Tennki批次处理器 - 管理同一据点内的并行批次处理"""

    def __init__(self, facility_config: dict, workflow_config: dict, multi_browser_manager):
        self.facility_config = facility_config
        self.workflow_config = workflow_config
        self.multi_browser_manager = multi_browser_manager
        self.facility_name = facility_config.get('name')

        # 批次处理状态
        self.batch_processors = []
        self.processing_results = {}
        self.global_failed_collector = TennkiFailedDataCollector()

        # 并发控制
        self.max_concurrent_batches = 4
        self.semaphore = asyncio.Semaphore(self.max_concurrent_batches)

        # 🆕 登录锁机制 - 防止并发登录冲突
        self.login_lock = asyncio.Lock()
        
    async def process_batches_parallel(self, batch_info_list: List[Dict]) -> bool:
        """并行处理多个批次"""
        if not batch_info_list:
            logger.warning("⚠️ 没有批次需要处理")
            return True
            
        logger.info(f"🚀 开始并行处理 {len(batch_info_list)} 个批次")
        
        try:
            # 创建并行任务
            tasks = []
            for batch_info in batch_info_list:
                task = self._process_single_batch(batch_info)
                tasks.append(task)
            
            # 执行并行任务
            start_time = time.time()
            results = await asyncio.gather(*tasks, return_exceptions=True)
            processing_time = time.time() - start_time
            
            # 统计结果
            success_count = sum(1 for r in results if r is True)
            error_count = len(results) - success_count
            
            logger.info(f"📊 并行批次处理完成:")
            logger.info(f"   总时间: {processing_time:.2f} 秒")
            logger.info(f"   成功批次: {success_count}/{len(batch_info_list)}")
            logger.info(f"   失败批次: {error_count}")
            
            # 记录失败的批次
            for i, result in enumerate(results):
                if isinstance(result, Exception) or result is False:
                    # 🆕 修复：batch_info_list中的元素是字典，不是对象
                    batch_info = batch_info_list[i]
                    batch_id = batch_info.get('batch_id', f'batch_{i}')
                    logger.error(f"❌ 批次 {batch_id} 处理失败: {result}")
            
            # 收集所有失败数据
            await self._collect_all_failed_data()
            
            return success_count == len(batch_info_list)
            
        except Exception as e:
            logger.error(f"❌ 并行批次处理异常: {e}", exc_info=True)
            return False
    
    async def _process_single_batch(self, batch_info: Dict) -> bool:
        """处理单个批次"""
        batch_id = batch_info.get('batch_index', 0)
        
        # 使用信号量控制并发数量
        async with self.semaphore:
            logger.info(f"🔄 开始处理批次 {batch_id}: {batch_info.get('total_users', 0)}用户, {batch_info.get('total_records', 0)}记录")
            
            batch_processor = None
            try:
                # 创建批次处理器
                batch_processor = SingleBatchProcessor(
                    batch_id,
                    batch_info.get('batch_data', []),
                    self.facility_config,
                    self.workflow_config,
                    self.multi_browser_manager,
                    self.login_lock  # 🆕 传递登录锁
                )
                
                # 添加到处理器列表
                self.batch_processors.append(batch_processor)
                
                # 初始化并处理
                await batch_processor.initialize()
                result = await batch_processor.process_batch_data()
                
                if result:
                    logger.info(f"✅ 批次 {batch_id} 处理成功")
                else:
                    logger.error(f"❌ 批次 {batch_id} 处理失败")
                
                self.processing_results[batch_id] = result
                return result
                
            except Exception as e:
                logger.error(f"❌ 批次 {batch_id} 处理异常: {e}", exc_info=True)
                self.processing_results[batch_id] = False
                return False
            finally:
                # 清理批次处理器资源
                if batch_processor:
                    await batch_processor.cleanup()
    
    async def _collect_all_failed_data(self):
        """收集所有批次的失败数据"""
        try:
            logger.info("📊 收集所有批次的失败数据...")
            
            for processor in self.batch_processors:
                if hasattr(processor, 'failed_data_collector'):
                    # 合并失败记录
                    self.global_failed_collector.failed_records.extend(
                        processor.failed_data_collector.failed_records
                    )
                    # 合并失败用户
                    self.global_failed_collector.failed_users.extend(
                        processor.failed_data_collector.failed_users
                    )
            
            total_failed = self.global_failed_collector.get_failed_count()
            logger.info(f"📋 批次失败数据收集完成: {total_failed} 条失败记录")
            
        except Exception as e:
            logger.warning(f"⚠️ 批次失败数据收集过程中出错: {e}")
    
    def get_failed_data_collector(self) -> TennkiFailedDataCollector:
        """获取全局失败数据收集器"""
        return self.global_failed_collector


class SingleBatchProcessor:
    """单批次处理器 - 处理一个数据批次"""
    
    def __init__(self, batch_id: int, user_data_list: List[Dict],
                 facility_config: dict, workflow_config: dict, multi_browser_manager, login_lock: asyncio.Lock):
        self.batch_id = batch_id
        self.user_data_list = user_data_list
        self.facility_config = facility_config
        self.workflow_config = workflow_config
        self.multi_browser_manager = multi_browser_manager
        self.login_lock = login_lock  # 🆕 登录锁
        
        # 浏览器实例
        self.browser = None
        self.page = None
        self.selector_executor = None
        
        # 处理组件
        self.facility_manager = None
        self.form_engine = None
        self.failed_data_collector = TennkiFailedDataCollector()
        
        # 批次标识符（用于区分不同批次的浏览器实例）
        self.batch_browser_name = f"{facility_config.get('name')}_batch_{batch_id}"
    
    async def initialize(self):
        """初始化批次处理器"""
        logger.info(f"🔧 初始化批次处理器 {self.batch_id}")
        
        try:
            # 1. 创建独立的浏览器实例
            self.browser, self.page = await self.multi_browser_manager.create_browser_for_facility(
                self.batch_browser_name, headless=False
            )
            
            # 2. 初始化选择器执行器
            self.selector_executor = SelectorExecutor(self.page)
            await self.selector_executor.initialize_mcp_fallback()

            # 🆕 2.1 设置页面保护机制
            await self._setup_page_protection()
            
            # 3. 🆕 增强版登录锁机制，防止并发登录冲突
            async with self.login_lock:
                logger.info(f"🔐 批次 {self.batch_id} 等待登录锁...")

                # 🆕 登录前状态检查
                await self._pre_login_health_check()

                login_success = await kaipoke_login_with_env(
                    self.page,
                    self.workflow_config.get('corporation_id_env', 'KAIPOKE_CORPORATION_ID'),
                    self.workflow_config.get('login_id_env', 'KAIPOKE_MEMBER_LOGIN_ID'),
                    self.workflow_config.get('password_env', 'KAIPOKE_PASSWORD'),
                    self.workflow_config.get('login_url')
                )

                if not login_success:
                    raise Exception(f"批次 {self.batch_id} Kaipoke登录失败")

                # 🆕 登录后状态验证
                login_verified = await self._verify_login_state()
                if not login_verified:
                    logger.warning(f"⚠️ 批次 {self.batch_id} 登录状态验证失败，尝试重新登录...")
                    # 重试一次登录
                    login_success = await kaipoke_login_with_env(
                        self.page,
                        self.workflow_config.get('corporation_id_env', 'KAIPOKE_CORPORATION_ID'),
                        self.workflow_config.get('login_id_env', 'KAIPOKE_MEMBER_LOGIN_ID'),
                        self.workflow_config.get('password_env', 'KAIPOKE_PASSWORD'),
                        self.workflow_config.get('login_url')
                    )
                    if not login_success:
                        raise Exception(f"批次 {self.batch_id} 重试登录也失败")

                logger.info(f"🔓 批次 {self.batch_id} 登录完成，释放登录锁")

                # 🆕 登录成功后等待一段时间，确保会话稳定
                await asyncio.sleep(3)  # 增加等待时间确保稳定性

            # 4. 初始化组件
            self.facility_manager = TennkiFacilityManager(self.selector_executor)
            # 🆕 创建批次专用的性能监控器
            batch_performance_monitor = TennkiPerformanceMonitor()
            self.form_engine = TennkiFormEngine(
                self.selector_executor,
                batch_performance_monitor,  # 使用批次专用的性能监控器
                self.failed_data_collector
            )

            # 🆕 预先同步表单验证状态，防止VAL_0001错误
            logger.debug(f"🔧 批次 {self.batch_id} 预先同步表单验证状态...")
            try:
                await self.form_engine._pre_sync_form_validation_state(self.page)
                logger.debug(f"✅ 批次 {self.batch_id} 表单验证状态同步完成")
            except Exception as e:
                logger.warning(f"⚠️ 批次 {self.batch_id} 表单验证状态同步失败: {e}")

            # 🆕 4.5. 点击レセプト主菜单（修复缺失步骤）
            await self._navigate_to_receipt_menu()

            # 5. 导航到据点
            facility_name = self.facility_config.get('name')
            element_text = self.facility_config.get('element_text')
            await self.facility_manager.navigate_to_facility(element_text, facility_name)

            # 6. 导航到訪問看護页面
            await self.facility_manager.navigate_to_nursing_page()

            # 🆕 7. 验证页面状态，确保在正确的页面
            await self._verify_page_state()

            logger.info(f"✅ 批次处理器 {self.batch_id} 初始化完成")
            
        except Exception as e:
            logger.error(f"❌ 批次处理器 {self.batch_id} 初始化失败: {e}")
            raise

    async def _verify_page_state(self):
        """🆕 验证页面状态，防止意外跳转"""
        try:
            current_url = self.page.url
            logger.info(f"🔍 批次 {self.batch_id} 当前页面URL: {current_url}")

            # 检查是否在主页面（biztop）
            if "biztop" in current_url:
                logger.warning(f"⚠️ 批次 {self.batch_id} 页面意外跳转到主页，尝试恢复...")
                # 🆕 检查facility_manager是否可用
                if self.facility_manager is not None:
                    await self._recover_to_nursing_page()
                else:
                    logger.warning(f"⚠️ 批次 {self.batch_id} facility_manager未初始化，跳过页面恢复")

            # 检查是否有必要的表单元素
            await self._check_essential_elements()

        except Exception as e:
            logger.warning(f"⚠️ 批次 {self.batch_id} 页面状态验证失败: {e}")

    async def _navigate_to_receipt_menu(self):
        """🆕 导航到レセプト菜单（参考RPA代码实现）"""
        try:
            logger.info(f"📋 批次 {self.batch_id} 点击レセプト主菜单...")

            # 等待页面完全加载
            await self.page.wait_for_load_state('networkidle', timeout=30000)

            # 🆕 多重策略点击主菜单（参考RPA代码中的 .mainCtg li:nth-of-type(1) a）
            main_menu_clicked = False

            # 策略1：使用RPA代码中的精确选择器
            try:
                await self.page.wait_for_selector('.mainCtg li:nth-of-type(1) a', timeout=10000)
                await self.page.click('.mainCtg li:nth-of-type(1) a')
                main_menu_clicked = True
                logger.info(f"✅ 批次 {self.batch_id} 使用RPA精确选择器成功点击主菜单")
            except Exception as e:
                logger.debug(f"⚠️ 批次 {self.batch_id} RPA精确选择器失败: {e}")

            # 策略2：使用智能选择器
            if not main_menu_clicked:
                try:
                    success = await self.selector_executor.smart_click(
                        workflow="kaipoke_tennki",
                        category="navigation",
                        element="main_menu",
                        target_text="レセプト"
                    )
                    if success:
                        main_menu_clicked = True
                        logger.info(f"✅ 批次 {self.batch_id} 智能选择器成功点击主菜单")
                except Exception as e:
                    logger.debug(f"⚠️ 批次 {self.batch_id} 智能选择器失败: {e}")

            # 策略3：文本匹配
            if not main_menu_clicked:
                try:
                    await self.page.wait_for_selector("text=レセプト", timeout=10000)
                    await self.page.click("text=レセプト")
                    main_menu_clicked = True
                    logger.info(f"✅ 批次 {self.batch_id} 文本匹配成功点击主菜单")
                except Exception as e:
                    logger.debug(f"⚠️ 批次 {self.batch_id} 文本匹配失败: {e}")

            if not main_menu_clicked:
                raise Exception(f"批次 {self.batch_id} 所有策略都无法点击レセプト主菜单")

            # 等待页面加载
            await self.page.wait_for_load_state('networkidle', timeout=30000)
            logger.info(f"✅ 批次 {self.batch_id} レセプト主菜单点击完成，页面已加载")

        except Exception as e:
            logger.error(f"❌ 批次 {self.batch_id} 导航到レセプト菜单失败: {e}")
            raise

    async def _recover_to_nursing_page(self):
        """🆕 恢复到訪問看護页面"""
        try:
            logger.info(f"🔄 批次 {self.batch_id} 开始恢复到訪問看護页面...")

            # 🆕 检查facility_manager是否已初始化
            if self.facility_manager is None:
                logger.warning(f"⚠️ 批次 {self.batch_id} facility_manager未初始化，无法恢复页面")
                # 尝试重新初始化facility_manager
                if self.selector_executor is not None:
                    logger.info(f"🔧 批次 {self.batch_id} 尝试重新初始化facility_manager...")
                    self.facility_manager = TennkiFacilityManager(self.selector_executor)
                else:
                    logger.error(f"❌ 批次 {self.batch_id} selector_executor也未初始化，无法恢复")
                    raise Exception("无法恢复页面：关键组件未初始化")

            # 重新导航到据点
            facility_name = self.facility_config.get('name')
            element_text = self.facility_config.get('element_text')
            await self.facility_manager.navigate_to_facility(element_text, facility_name)

            # 重新导航到訪問看護页面
            await self.facility_manager.navigate_to_nursing_page()

            logger.info(f"✅ 批次 {self.batch_id} 页面恢复成功")

        except Exception as e:
            logger.error(f"❌ 批次 {self.batch_id} 页面恢复失败: {e}")
            raise

    async def _check_essential_elements(self):
        """🆕 检查必要的页面元素"""
        try:
            # 检查是否有数据录入相关的元素
            essential_selectors = [
                "#selectServiceOfferYm",  # 服务提供年月选择
                ".table-linkuser",        # 用户列表
            ]

            for selector in essential_selectors:
                try:
                    await self.page.wait_for_selector(selector, timeout=5000)
                    logger.debug(f"✅ 批次 {self.batch_id} 找到必要元素: {selector}")
                    return  # 找到一个就足够了
                except:
                    continue

            logger.warning(f"⚠️ 批次 {self.batch_id} 未找到必要的页面元素，可能需要重新导航")

        except Exception as e:
            logger.warning(f"⚠️ 批次 {self.batch_id} 页面元素检查失败: {e}")

    async def _setup_page_protection(self):
        """🆕 增强版页面保护机制，防止误点击广告和页面跳转"""
        try:
            # 增强版页面保护脚本
            await self.page.evaluate("""
                // 阻止新窗口打开
                window.open = function() {
                    console.log('🛡️ 阻止新窗口打开');
                    return null;
                };

                // 增强版广告检测和阻止
                document.addEventListener('click', function(e) {
                    const target = e.target;
                    const href = target.href || target.closest('a')?.href;
                    const targetText = target.textContent || '';
                    const targetClass = target.className || '';
                    const targetId = target.id || '';

                    // 🆕 更全面的广告检测规则
                    const isAd = href && (
                        // URL关键词检测
                        href.includes('ad') ||
                        href.includes('banner') ||
                        href.includes('promo') ||
                        href.includes('campaign') ||
                        href.includes('affiliate') ||
                        href.includes('sponsor') ||
                        href.includes('marketing') ||
                        href.includes('popup') ||
                        // 外部域名检测（非kaipoke域名）
                        (!href.includes('kaipoke.biz') && (href.startsWith('http') || href.startsWith('//'))) ||
                        // CSS类名检测
                        targetClass.includes('ad') ||
                        targetClass.includes('banner') ||
                        targetClass.includes('promo') ||
                        targetClass.includes('popup') ||
                        // ID检测
                        targetId.includes('ad') ||
                        targetId.includes('banner') ||
                        // 文本内容检测
                        targetText.includes('広告') ||
                        targetText.includes('PR') ||
                        targetText.includes('スポンサー')
                    );

                    if (isAd) {
                        console.log('🛡️ 阻止广告点击:', href || targetText);
                        e.preventDefault();
                        e.stopPropagation();
                        return false;
                    }

                    // 🆕 保护重要表单元素，防止意外点击
                    const isImportantForm = target.closest('#registModal') ||
                                          target.closest('.table-linkuser') ||
                                          target.closest('#selectServiceOfferYm');

                    if (!isImportantForm && href && !href.includes('kaipoke.biz')) {
                        console.log('🛡️ 阻止可疑外部链接点击:', href);
                        e.preventDefault();
                        e.stopPropagation();
                        return false;
                    }
                }, true);

                // 🆕 监控页面跳转
                let originalPushState = history.pushState;
                let originalReplaceState = history.replaceState;

                history.pushState = function() {
                    console.log('🔍 检测到页面状态变化 (pushState):', arguments.length > 2 ? arguments[2] : 'undefined');
                    return originalPushState.apply(history, arguments);
                };

                history.replaceState = function() {
                    console.log('🔍 检测到页面状态变化 (replaceState):', arguments.length > 2 ? arguments[2] : 'undefined');
                    return originalReplaceState.apply(history, arguments);
                };

                // 🆕 阻止意外的页面刷新
                window.addEventListener('beforeunload', function(e) {
                    console.log('🔍 检测到页面即将卸载');
                    // 在自动化过程中，通常不希望页面意外刷新
                    // 但不阻止正常的导航操作
                });

                console.log('🛡️ 增强版页面保护机制已激活');
            """)

            # 🆕 启动页面状态监控
            await self._start_page_monitoring()

            logger.debug(f"✅ 批次 {self.batch_id} 增强版页面保护机制设置完成")

        except Exception as e:
            logger.warning(f"⚠️ 批次 {self.batch_id} 页面保护机制设置失败: {e}")

    async def _start_page_monitoring(self):
        """🆕 启动页面状态监控"""
        try:
            logger.debug(f"🔍 批次 {self.batch_id} 启动页面状态监控...")

            # 设置页面监控间隔（每30秒检查一次）
            asyncio.create_task(self._monitor_page_state_loop())

        except Exception as e:
            logger.warning(f"⚠️ 批次 {self.batch_id} 页面状态监控启动失败: {e}")

    async def _monitor_page_state_loop(self):
        """🆕 页面状态监控循环"""
        try:
            while True:
                await asyncio.sleep(30)  # 每30秒检查一次

                # 检查页面是否还在正确的位置
                current_url = self.page.url

                if "biztop" in current_url:
                    logger.warning(f"⚠️ 批次 {self.batch_id} 检测到页面意外跳转到主页，尝试恢复...")
                    # 🆕 检查facility_manager是否可用
                    if self.facility_manager is not None:
                        await self._recover_to_nursing_page()
                    else:
                        logger.warning(f"⚠️ 批次 {self.batch_id} facility_manager未初始化，跳过页面恢复")
                elif not any(keyword in current_url for keyword in ["kaipoke.biz", "localhost"]):
                    logger.warning(f"⚠️ 批次 {self.batch_id} 检测到页面跳转到外部站点: {current_url}")
                    # 🆕 检查facility_manager是否可用
                    if self.facility_manager is not None:
                        await self._recover_to_nursing_page()
                    else:
                        logger.warning(f"⚠️ 批次 {self.batch_id} facility_manager未初始化，跳过页面恢复")

        except asyncio.CancelledError:
            logger.debug(f"🔍 批次 {self.batch_id} 页面状态监控已停止")
        except Exception as e:
            logger.warning(f"⚠️ 批次 {self.batch_id} 页面状态监控异常: {e}")

    async def _pre_login_health_check(self):
        """🆕 登录前健康检查（修复版）"""
        try:
            logger.debug(f"🔍 批次 {self.batch_id} 执行登录前健康检查...")

            # 🆕 简化的页面响应检查
            try:
                # 方法1：检查页面基本状态
                current_url = self.page.url
                logger.debug(f"🔍 批次 {self.batch_id} 当前URL: {current_url}")

                # 方法2：简单的JavaScript执行测试
                test_result = await self.page.evaluate("typeof document !== 'undefined'")
                if not test_result:
                    logger.warning(f"⚠️ 批次 {self.batch_id} 页面document对象不可用")
                    raise Exception("页面状态异常")

                logger.debug(f"✅ 批次 {self.batch_id} 页面基本响应正常")

            except Exception as e:
                logger.warning(f"⚠️ 批次 {self.batch_id} 页面响应检查异常: {e}")

                # 🆕 尝试恢复页面状态
                try:
                    if not current_url or current_url == "about:blank":
                        logger.debug(f"🔄 批次 {self.batch_id} 尝试导航到登录页...")
                        await self.page.goto(self.workflow_config.get('login_url'), timeout=30000)
                        await asyncio.sleep(2)  # 等待页面加载

                        # 重新检查
                        test_result = await self.page.evaluate("typeof document !== 'undefined'")
                        if test_result:
                            logger.debug(f"✅ 批次 {self.batch_id} 页面恢复成功")
                        else:
                            raise Exception("页面恢复失败")
                    else:
                        # 页面URL正常，但响应异常，可能是临时问题
                        logger.debug(f"🔄 批次 {self.batch_id} 页面URL正常，等待恢复...")
                        await asyncio.sleep(3)

                except Exception as recovery_error:
                    logger.error(f"❌ 批次 {self.batch_id} 页面恢复失败: {recovery_error}")
                    raise Exception("页面无响应且无法恢复，无法继续登录")

            logger.debug(f"✅ 批次 {self.batch_id} 登录前健康检查完成")

        except Exception as e:
            logger.error(f"❌ 批次 {self.batch_id} 登录前健康检查失败: {e}")
            raise

    async def _verify_login_state(self):
        """🆕 验证登录状态"""
        try:
            logger.debug(f"🔍 批次 {self.batch_id} 验证登录状态...")

            # 等待页面加载完成
            await asyncio.sleep(2)

            # 检查是否成功登录（查找登录后的特征元素）
            login_indicators = [
                "レセプト",  # 主菜单中的レセプト
                ".user-info",  # 用户信息区域
                "#logout",     # 登出按钮
                ".main-menu"   # 主菜单
            ]

            for indicator in login_indicators:
                try:
                    element = await self.page.wait_for_selector(indicator, timeout=3000)
                    if element:
                        logger.debug(f"✅ 批次 {self.batch_id} 找到登录指示器: {indicator}")
                        return True
                except:
                    continue

            # 检查当前URL是否包含登录后的特征
            current_url = self.page.url
            if "biztop" in current_url or "main" in current_url:
                logger.debug(f"✅ 批次 {self.batch_id} URL显示已登录: {current_url}")
                return True

            logger.warning(f"⚠️ 批次 {self.batch_id} 登录状态验证失败")
            return False

        except Exception as e:
            logger.warning(f"⚠️ 批次 {self.batch_id} 登录状态验证异常: {e}")
            return False

    async def _browser_health_check(self):
        """🆕 浏览器健康检查"""
        try:
            logger.debug(f"🔍 批次 {self.batch_id} 执行浏览器健康检查...")

            # 检查浏览器是否还在运行
            if not self.browser or self.browser.is_closed():
                logger.error(f"❌ 批次 {self.batch_id} 浏览器已关闭")
                return False

            # 检查页面是否还有效
            if not self.page or self.page.is_closed():
                logger.error(f"❌ 批次 {self.batch_id} 页面已关闭")
                return False

            # 检查页面是否响应
            try:
                # 🆕 修复：使用简单的evaluate，不使用timeout参数
                result = await self.page.evaluate("1+1")
                if result == 2:
                    logger.debug(f"✅ 批次 {self.batch_id} 浏览器健康状态良好")
                    return True
                else:
                    logger.warning(f"⚠️ 批次 {self.batch_id} 页面计算结果异常: {result}")
                    return False
            except Exception as e:
                logger.warning(f"⚠️ 批次 {self.batch_id} 页面无响应: {e}")
                return False

        except Exception as e:
            logger.error(f"❌ 批次 {self.batch_id} 浏览器健康检查失败: {e}")
            return False
    
    async def process_batch_data(self) -> bool:
        """处理批次数据"""
        try:
            logger.info(f"📝 批次 {self.batch_id} 开始处理数据: {len(self.user_data_list)} 个用户")

            # 🆕 处理前再次验证页面状态
            await self._verify_page_state()

            # 使用表单引擎处理数据
            await self.form_engine.process_batch_data_sequential(
                self.user_data_list,
                self.facility_config
            )
            
            # 更新失败记录的据点名称
            facility_name = self.facility_config.get('name')
            for record in self.failed_data_collector.failed_records:
                if not record['facility_name']:
                    record['facility_name'] = f"{facility_name}_批次{self.batch_id}"
            for user in self.failed_data_collector.failed_users:
                if not user['facility_name']:
                    user['facility_name'] = f"{facility_name}_批次{self.batch_id}"
            
            logger.info(f"✅ 批次 {self.batch_id} 数据处理完成")
            return True
            
        except Exception as e:
            logger.error(f"❌ 批次 {self.batch_id} 数据处理失败: {e}", exc_info=True)
            return False
    
    async def cleanup(self):
        """清理批次处理器资源"""
        try:
            await self.multi_browser_manager.close_facility_browser(self.batch_browser_name)
            logger.info(f"🔒 批次处理器 {self.batch_id} 资源清理完成")
        except Exception as e:
            logger.warning(f"⚠️ 批次处理器 {self.batch_id} 资源清理时出错: {e}")
