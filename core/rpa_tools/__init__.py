"""
RPA共通工具模块
提供统一的RPA操作接口，支持多据点配置
"""

from .kaipoke_common import KaipokeRPATools, KaipokeAccountManager
from .data_processor import DataProcessor, FileManager, ExcelProcessor
from .gsuite_integration import GoogleWorkspaceManager, DriveUploadManager, SheetsDataManager
from .workflow_manager import MultiSiteWorkflowManager, ConfigDrivenWorkflow, TaskExecutionMonitor
from .kaipoke_login_service import (
    KaipokeLoginService, 
    kaipoke_login_service,
    ensure_kaipoke_login,
    kaipoke_login_with_env,
    kaipoke_login_direct,
    get_kaipoke_current_account,
    get_kaipoke_available_accounts,
    kaipoke_logout
)

__all__ = [
    'KaipokeRPATools',
    'KaipokeAccountManager', 
    'DataProcessor',
    'FileManager',
    'ExcelProcessor',
    'GoogleWorkspaceManager',
    'DriveUploadManager',
    'SheetsDataManager',
    'MultiSiteWorkflowManager',
    'ConfigDrivenWorkflow',
    'TaskExecutionMonitor',
    'KaipokeLoginService',
    'kaipoke_login_service',
    'ensure_kaipoke_login',
    'kaipoke_login_with_env',
    'kaipoke_login_direct',
    'get_kaipoke_current_account',
    'get_kaipoke_available_accounts',
    'kaipoke_logout'
]