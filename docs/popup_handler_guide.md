# 企业级弹窗处理系统使用指南

## 🏗️ 架构概述

我们的企业级弹窗处理系统采用分层架构设计，提供了灵活、可扩展的弹窗处理解决方案：

```
┌─────────────────────────────────────────┐
│           应用层 (Application)           │
│  ┌─────────────────────────────────────┐ │
│  │        装饰器接口 (Decorators)       │ │
│  │  @handle_popups, @kaipoke_safe      │ │
│  └─────────────────────────────────────┘ │
├─────────────────────────────────────────┤
│            服务层 (Service)             │
│  ┌─────────────────────────────────────┐ │
│  │       弹窗处理引擎 (Engine)         │ │
│  │    规则匹配、动作执行、统计报告      │ │
│  └─────────────────────────────────────┘ │
│  ┌─────────────────────────────────────┐ │
│  │      智能检测器 (Detector)          │ │
│  │    机器学习、模式识别、自动建议      │ │
│  └─────────────────────────────────────┘ │
├─────────────────────────────────────────┤
│            配置层 (Configuration)        │
│  ┌─────────────────────────────────────┐ │
│  │        规则配置 (Rules)             │ │
│  │      YAML配置文件、动态规则         │ │
│  └─────────────────────────────────────┘ │
└─────────────────────────────────────────┘
```

## 🚀 快速开始

### 1. 基础使用 - 装饰器方式

```python
from core.popup_handler.popup_decorator import handle_popups, kaipoke_safe

# 通用弹窗处理
@handle_popups(context="my_operation")
async def my_page_operation(page):
    await page.click("button")
    # 自动处理可能出现的弹窗

# Kaipoke平台专用
@kaipoke_safe
async def kaipoke_operation(page):
    await page.click(".some-button")
    # 自动处理Karte组件和其他Kaipoke弹窗
```

### 2. 上下文管理器方式

```python
from core.popup_handler.popup_decorator import PopupContext

async def complex_operation(page):
    async with PopupContext(page, context="complex_op") as popup_ctx:
        await page.click("button1")
        
        # 手动触发弹窗处理
        await popup_ctx.handle_now()
        
        await page.click("button2")
        # 退出时自动处理弹窗
```

### 3. 直接调用方式

```python
from core.popup_handler.popup_decorator import quick_popup_handle

async def manual_operation(page):
    await page.click("button")
    
    # 手动处理弹窗
    handled = await quick_popup_handle(page, "manual_check")
    if handled:
        print("处理了弹窗")
```

## 📋 配置管理

### 规则配置文件 (configs/popup_rules.yaml)

```yaml
popup_rules:
  - name: "custom_modal"
    type: "modal"
    selectors:
      - ".my-modal .close"
      - "#close-button"
    action: "close"
    priority: 80
    timeout: 5000
    retry_count: 3
    description: "自定义模态框"
```

### 动态添加规则

```python
from core.popup_handler.popup_engine import popup_engine, PopupRule, PopupType, PopupAction

# 创建新规则
new_rule = PopupRule(
    name="dynamic_rule",
    popup_type=PopupType.NOTIFICATION,
    selectors=[".new-popup .close"],
    action=PopupAction.CLOSE,
    priority=90
)

# 动态添加
await popup_engine.add_dynamic_rule(new_rule)
```

## 🎯 高级功能

### 1. 自定义处理器

```python
from core.popup_handler.popup_engine import popup_engine

async def custom_handler(page, selectors):
    """自定义弹窗处理逻辑"""
    for selector in selectors:
        try:
            await page.evaluate(f"""
                document.querySelector('{selector}')?.remove();
            """)
        except:
            pass

# 注册自定义处理器
popup_engine.register_custom_handler("remove_element", custom_handler)
```

### 2. 智能检测和学习

```python
from core.popup_handler.popup_detector import popup_detector

async def analyze_and_learn(page):
    # 分析页面中的潜在弹窗
    potential_popups = await popup_detector.analyze_page(page)
    
    for popup in potential_popups:
        print(f"发现潜在弹窗: {popup['element']['selector']} (分数: {popup['score']})")
    
    # 从处理结果中学习
    await popup_detector.learn_from_success(".some-popup", "close", True)
    
    # 获取机器学习建议
    suggestions = await popup_detector.suggest_new_rules()
    for suggestion in suggestions:
        print(f"建议新规则: {suggestion['name']}")
```

### 3. 监控模式

```python
from core.popup_handler.popup_decorator import monitor_popups

@monitor_popups(interval=3000, max_duration=60000)
async def long_running_operation(page):
    # 在执行期间持续监控弹窗
    for i in range(10):
        await page.click(f"button-{i}")
        await asyncio.sleep(5)
```

## 📊 统计和监控

### 获取处理统计

```python
from core.popup_handler.popup_engine import popup_engine

# 获取统计信息
stats = popup_engine.get_stats()
print(f"总检测数: {stats['total_detected']}")
print(f"总处理数: {stats['total_handled']}")
print(f"按类型统计: {stats['by_type']}")
```

### 导出学习数据

```python
from core.popup_handler.popup_detector import popup_detector

# 导出学习数据
await popup_detector.export_learning_data("popup_learning_data.json")

# 导入学习数据
await popup_detector.import_learning_data("popup_learning_data.json")
```

## 🔧 最佳实践

### 1. 规则优先级设计

```
95-100: 紧急处理 (安全相关、阻塞性弹窗)
80-94:  平台专用 (Kaipoke、特定网站)
60-79:  通用规则 (模态框、通知)
40-59:  低优先级 (广告、可选弹窗)
10-39:  备用方案 (ESC键、点击外部)
```

### 2. 错误处理策略

```python
@handle_popups(
    context="critical_operation",
    retry_count=5,
    fallback_enabled=True
)
async def critical_operation(page):
    try:
        await page.click("important-button")
    except Exception as e:
        logger.error(f"关键操作失败: {e}")
        # 弹窗处理器会自动重试
        raise
```

### 3. 性能优化

```python
# 针对特定弹窗的高效处理
@popup_safe(
    selectors=[".specific-popup .close"],
    action="close",
    priority=95
)
async def optimized_operation(page):
    # 只处理特定弹窗，提高性能
    await page.click("button")
```

## 🚨 故障排除

### 常见问题

1. **弹窗未被检测到**
   - 检查选择器是否正确
   - 确认弹窗是否真的可见
   - 调整检测超时时间

2. **处理动作失败**
   - 验证动作类型是否合适
   - 检查元素是否可交互
   - 尝试自定义处理脚本

3. **性能影响**
   - 调整检测间隔
   - 使用更精确的选择器
   - 启用缓存机制

### 调试模式

```python
# 启用详细日志
import logging
logging.getLogger("popup_handler").setLevel(logging.DEBUG)

# 在配置文件中启用调试
advanced_settings:
  debug_mode: true
  log_all_attempts: true
```

## 🔮 未来扩展

### 计划功能

1. **AI驱动的弹窗识别**
   - 使用计算机视觉识别弹窗
   - 自然语言处理理解弹窗内容

2. **跨浏览器兼容性**
   - 支持不同浏览器的特殊弹窗
   - 统一的处理接口

3. **云端规则同步**
   - 规则云端存储和同步
   - 社区共享弹窗规则

4. **实时性能监控**
   - 弹窗处理性能仪表板
   - 异常告警机制

---

这个企业级弹窗处理系统为您提供了强大、灵活的弹窗处理能力。通过合理配置和使用，可以显著提高自动化脚本的稳定性和成功率。
