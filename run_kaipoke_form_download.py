#!/usr/bin/env python3
"""
运行脚本：启动kaipoke_form_download工作流
基于三种标准操作模板（A、B、C）完成37个账单预览文件的下载
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config_loader import load_workflow_config
from workflows.kaipoke_form_download import run
from logger_config import logger

def main():
    """主函数"""
    print("🚀 启动Kaipoke Form Download工作流 (模板化版本)")
    print("=" * 60)
    print("📋 任务概览:")
    print("   - 总任务数: 37个")
    print("   - 模板A (国保連請求管理): 18个任务")
    print("   - 模板B (医療請求): 6个任务") 
    print("   - 模板C (介護請求): 11个任务")
    print("   - 模板B+C (组合流程): 2个任务")
    print("=" * 60)
    
    # 检查环境变量
    required_env_vars = [
        'KAIPOKE_CORPORATION_ID',
        'KAIPOKE_MEMBER_LOGIN_ID', 
        'KAIPOKE_PASSWORD'
    ]
    
    missing_vars = []
    for var in required_env_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print("❌ 缺少必需的环境变量:")
        for var in missing_vars:
            print(f"   - {var}")
        print("\n请设置这些环境变量后重新运行。")
        return 1
    
    print("✅ 环境变量检查通过")
    
    try:
        # 加载工作流配置
        print("📖 加载工作流配置...")
        config = load_workflow_config('kaipoke_form_download')
        
        if not config:
            print("❌ 无法加载工作流配置")
            return 1
        
        print("✅ 工作流配置加载成功")
        
        # 显示配置信息
        common_config = config.get('config', {})
        tasks = config.get('tasks', [])
        
        print(f"📁 下载路径: {common_config.get('download_path', 'N/A')}")
        print(f"☁️ Google Drive文件夹: {common_config.get('gdrive_folder_id', 'N/A')}")
        print(f"🖥️ 无头模式: {common_config.get('headless', False)}")
        print(f"📋 任务数量: {len(tasks)}")
        
        # 确认运行
        print("\n" + "=" * 60)
        response = input("🤔 确认开始运行工作流？(y/N): ").strip().lower()
        
        if response not in ['y', 'yes']:
            print("❌ 用户取消运行")
            return 0
        
        print("\n🚀 开始执行工作流...")
        print("=" * 60)
        
        # 运行工作流
        run(config)
        
        print("=" * 60)
        print("🎉 工作流执行完成！")
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断执行")
        return 1
    except Exception as e:
        logger.error(f"❌ 工作流执行失败: {e}", exc_info=True)
        print(f"❌ 工作流执行失败: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
