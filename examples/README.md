# Aozora自动化工作流示例

本文件夹包含项目中关键模式和最佳实践的示例，用于Context Engineering参考。

## 工作流模式

### 基本工作流结构
```python
def run(config):
    """工作流入口点
    
    Args:
        config (dict): 来自workflows.yaml的配置
    """
    # 1. 初始化和验证
    # 2. 执行主要逻辑
    # 3. 错误处理和清理
    # 4. 返回结果
```

### 配置模式
- `configs/workflows.yaml` - 工作流配置
- `configs/selectors.yaml` - 选择器配置
- `.env` - 环境变量

### 代理模式
- 使用CrewAI进行多代理协作
- MCP工具集成用于浏览器自动化
- 结构化的工具组织

### 数据处理模式
- Pandas用于CSV操作
- Google Drive集成用于文件上传
- 数据验证和清理

## 关键文件参考

1. `workflows/kaipoke_performance_report.py` - 完整工作流实现
2. `agents/web_operator_agent.py` - Web操作代理
3. `core/rpa_tools/` - RPA工具和服务
4. `logger_config.py` - 日志配置

## 最佳实践

1. **错误处理**: 使用try-catch和重试逻辑
2. **日志记录**: 使用logger而不是print
3. **配置管理**: 外部化所有配置
4. **模块化**: 保持文件小于500行
5. **测试**: 为所有新功能编写测试