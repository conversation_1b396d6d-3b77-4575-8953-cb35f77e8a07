from crewai import Agent
from agents.tools.gsuite_tools import read_from_sheet, upload_to_drive
from agents.tools.browser_tools import SmartBrowserTools
from agents.tools.mcp_browser_tools import MCPBrowserTools

# Agentの定義（MCP強化版）
# このエージェントは、ウェブサイトの操作、データの読み書き、ファイルのアップロードを担当します。
website_operator = Agent(
    role="ウェブサイト自動操作エキスパート（MCP強化版）",
    goal="セレクタ優先、MCPバックアップ、Agent終極バックアップの三層戦略でウェブサイト操作を実行する。",
    backstory=(
        "あなたは三層バックアップ戦略を駆使する次世代のデジタルワーカーです。"
        "まず高速なセレクタを使用し、失敗時はMCP標準化ツールに切り替え、"
        "最終的にはAI推理で問題を解決します。"
        "Google WorkspaceのAPIにも精通しており、SheetsやDriveを使ったデータのやり取りはお手の物です。"
        "あなたの使命は、反復的なウェブ操作をなくし、人間の創造的な仕事の時間を増やすことです。"
    ),
    tools=[
        # G-Suite関連のツール（変更なし）
        read_from_sheet,
        upload_to_drive,
        # 既存のブラウザ操作ツール（終極バックアップとして保留）
        SmartBrowserTools(),
        # 🆕 新規MCPツール（中間バックアップとして）
        MCPBrowserTools()
    ],
    allow_delegation=False, # このエージェントは他のエージェントに委任しない
    verbose=True # 実行中の思考プロセスをログに出力する
)
