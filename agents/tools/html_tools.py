import asyncio
from crewai.tools import BaseTool
from logger_config import logger
from typing import Type
from pydantic import BaseModel, ConfigDict
from core.browser.browser_manager import browser_manager

class EmptyArgsSchema(BaseModel):
    model_config = ConfigDict(extra="forbid")

class ReadPageHTMLTool(BaseTool):
    name: str = "Read Full Page HTML"
    description: str = "Reads and returns the full HTML content of the current web page for analysis."
    args_schema: Type[BaseModel] = EmptyArgsSchema

    def _run(self) -> str:
        logger.info("Reading full page HTML for analysis...")
        try:
            page = asyncio.run(browser_manager.get_page())
            content = asyncio.run(page.content())
            logger.info("Successfully read page HTML.")
            # HTMLが長すぎる場合、LLMのコンテキストウィンドウを考慮して一部を返すか、
            # 主要な部分（例：body）のみを返すなどの調整が必要になる可能性がある。
            return content
        except Exception as e:
            logger.error(f"Failed to read page HTML: {e}", exc_info=True)
            return f"Error: Could not read page HTML. Reason: {e}"
