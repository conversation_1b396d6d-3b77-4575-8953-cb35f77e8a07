import os
import csv
import glob
from datetime import datetime
from logger_config import logger

def process_downloaded_csv(download_path: str, file_name_template: str, original_encoding: str = 'Shift_JIS', target_encoding: str = 'UTF-8') -> str:
    """指定されたダウンロードパスから最新のCSVファイルを見つけ、文字コードを変換し、
    テンプレートに基づいて新しいファイル名を生成し、その内容を返すツール。

    Args:
        download_path (str): ダウンロードされたファイルが保存されているディレクトリ。
        file_name_template (str): 新しいファイル名のテンプレート。例: 'ファイル_{cell_B2}_{datetime}.csv'
        original_encoding (str): 元のファイルの文字コード。デフォルトは'Shift_JIS'。
        target_encoding (str): 変換後の文字コード。デフォルトは'UTF-8'。

    Returns:
        str: 処理後のファイルパスと、その内容（CSV形式の文字列）を結合した文字列。例: '/path/to/new_file.csv|header1,header2\nvalue1,value2'
    """
    try:
        logger.info(f"CSV処理を開始します。ダウンロードパス: {download_path}")

        # 1. 最新のCSVファイルを見つける
        list_of_files = glob.glob(f'{download_path}/*.csv')
        if not list_of_files:
            raise FileNotFoundError(f"ダウンロードパスにCSVファイルが見つかりません: {download_path}")
        latest_file = max(list_of_files, key=os.path.getctime)
        logger.info(f"最新のファイルを発見: {latest_file}")

        # 2. 文字コードを変換して内容を読み込む
        with open(latest_file, 'r', encoding='shift_jis', errors='replace') as f:
            content = f.read()
        
        # UTF-8で一時ファイルに書き出すことで変換を確実にする
        temp_utf8_path = f"{latest_file}.utf8"
        with open(temp_utf8_path, 'w', encoding=target_encoding) as f:
            f.write(content)
        logger.info(f"文字コードを {target_encoding} に変換しました。")

        # 3. CSVを読み込み、テンプレート変数を解決する
        with open(temp_utf8_path, 'r', encoding=target_encoding) as f:
            reader = csv.reader(f)
            data = list(reader)
        
        if not data:
            raise ValueError("CSVファイルが空です。")

        template_vars = {
            'datetime': datetime.now().strftime('%Y-%m-%d_%H%M'),
            'year': datetime.now().year,
            'month': datetime.now().month
        }
        # セル指定子（例: {cell_B2}）を解決
        for i, row in enumerate(data):
            for j, cell in enumerate(row):
                col_letter = chr(ord('A') + j)
                template_vars[f'cell_{col_letter}{i+1}'] = cell
        
        # テンプレートで要求される可能性のあるすべてのセル参照をダミー値で初期化
        import re
        placeholders = re.findall(r'\{(.+?)\}', file_name_template)
        for placeholder in placeholders:
            if placeholder.startswith('cell_') and placeholder not in template_vars:
                template_vars[placeholder] = ''

        # 4. 新しいファイル名を生成
        new_filename = file_name_template.format(**template_vars)
        new_filepath = os.path.join(download_path, new_filename)
        logger.info(f"新しいファイル名を生成: {new_filename}")

        # 5. ファイルをリネーム
        os.rename(temp_utf8_path, new_filepath)
        # 元のShift-JISファイルは削除
        os.remove(latest_file)
        logger.info(f"ファイルをリネームしました: {new_filepath}")

        # 6. 処理後のファイルパスと内容を返す
        # 内容をCSV文字列として再結合
        csv_content = "\n".join([",".join(row) for row in data])
        return f"{new_filepath}|{csv_content}"

    except Exception as e:
        logger.error(f"CSV処理中にエラーが発生しました: {e}", exc_info=True)
        return f"エラー: CSVファイルの処理に失敗しました。理由: {e}"