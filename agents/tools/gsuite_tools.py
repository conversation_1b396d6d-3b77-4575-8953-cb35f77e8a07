from crewai.tools import tool
from core.gsuite.sheets_client import SheetsClient
from core.gsuite.drive_client import DriveClient
from logger_config import logger

@tool("Google Sheets Reader Tool")
def read_from_sheet(spreadsheet_id: str, range_name: str) -> list:
    """Googleスプレッドシートからデータを読み取るために使用します。
    
    Args:
        spreadsheet_id (str): 読み取るスプレッドシートのID。
        range_name (str): 読み取る範囲 (例: 'シート1!A1:B10')。
    
    Returns:
        list: 読み取ったデータの2次元リスト。エラーの場合はNone。
    """
    try:
        client = SheetsClient()
        return client.read_sheet(spreadsheet_id, range_name)
    except Exception as e:
        logger.error(f"read_from_sheetツール内でエラー: {e}", exc_info=True)
        return None

@tool("Google Drive Uploader Tool")
def upload_to_drive(local_file_path: str, drive_folder_id: str) -> str:
    """ローカルファイルをGoogle Driveの指定したフォルダにアップロードします。

    Args:
        local_file_path (str): アップロードするローカルファイルのパス。
        drive_folder_id (str): アップロード先のGoogle DriveフォルダのID。

    Returns:
        str: アップロードされたファイルのGoogle Drive上でのID。エラーの場合はNone。
    """
    try:
        client = DriveClient()
        return client.upload_file(local_file_path, drive_folder_id)
    except Exception as e:
        logger.error(f"upload_to_driveツール内でエラー: {e}", exc_info=True)
        return None
