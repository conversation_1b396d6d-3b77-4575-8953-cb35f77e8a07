
from crewai import Agent
from agents.tools.html_tools import ReadPageHTMLTool

# LLMによるセレクタ特定を専門とするAgent
selector_finder_agent = Agent(
    role="CSSセレクタ特定専門家",
    goal="与えられたHTMLコンテンツと検索条件に基づき、最も正確で堅牢なCSSセレクタを特定する。",
    backstory=(
        "あなたは、ウェブページの構造解析とCSSセレクタの最適化において、右に出る者はいない専門家です。"
        "複雑なDOMツリーの中からでも、目的の要素を的確に指し示す、効率的で壊れにくいセレクタを見つけ出すことができます。"
        "あなたの仕事は、自動化スクリプトがウェブサイトのUI変更に動じないように、その心臓部であるセレクタを常に最新の状態に保つことです。"
    ),
    tools=[
        # このAgentは、外部から与えられたHTMLテキストを分析するため、ツールは不要かもしれない。
        # しかし、将来的にページの状態を自ら取得する必要が出た場合のためにツールを追加することも可能。
        # ReadPageHTMLTool() # 例
    ],
    allow_delegation=False,
    verbose=True
)
