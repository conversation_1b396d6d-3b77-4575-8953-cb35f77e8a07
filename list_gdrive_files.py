#!/usr/bin/env python3
"""
列出Google Drive服务账号的所有文件
帮助用户确认删除哪些文件来释放空间
"""

import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from core.gsuite.drive_client import DriveClient
    from logger_config import logger
except ImportError as e:
    print(f"导入模块失败: {e}")
    print("请确保已安装所需依赖包")
    sys.exit(1)

def format_file_size(size_bytes):
    """格式化文件大小"""
    if size_bytes is None:
        return "未知"
    
    for unit in ['B', 'KB', 'MB', 'GB']:
        if size_bytes < 1024.0:
            return f"{size_bytes:.1f} {unit}"
        size_bytes /= 1024.0
    return f"{size_bytes:.1f} TB"

def format_date(date_str):
    """格式化日期"""
    if not date_str:
        return "未知"
    try:
        dt = datetime.fromisoformat(date_str.replace('Z', '+00:00'))
        return dt.strftime('%Y-%m-%d %H:%M:%S')
    except:
        return date_str

def list_gdrive_files():
    """列出Google Drive中的所有文件"""
    try:
        print("🔍 正在连接Google Drive...")
        drive_client = DriveClient()
        
        print("📋 获取文件列表...")
        
        # 获取所有文件，包括详细信息
        results = drive_client.service.files().list(
            pageSize=1000,  # 最多获取1000个文件
            fields="nextPageToken, files(id, name, size, mimeType, createdTime, modifiedTime, parents, owners)",
            orderBy="modifiedTime desc"  # 按修改时间降序排列
        ).execute()
        
        files = results.get('files', [])
        
        if not files:
            print("📂 没有找到任何文件")
            return
        
        print(f"\n📊 找到 {len(files)} 个文件:")
        print("=" * 120)
        print(f"{'文件名':<40} {'类型':<25} {'大小':<12} {'创建时间':<20} {'修改时间':<20}")
        print("=" * 120)
        
        total_size = 0
        spreadsheet_count = 0
        folder_count = 0
        other_count = 0
        
        for file in files:
            name = file.get('name', '未知')[:38]  # 限制文件名长度
            mime_type = file.get('mimeType', '未知')
            size = file.get('size')
            created_time = format_date(file.get('createdTime'))[:19]
            modified_time = format_date(file.get('modifiedTime'))[:19]
            file_id = file.get('id', '')
            
            # 统计文件类型
            if 'spreadsheet' in mime_type:
                spreadsheet_count += 1
                type_display = "📊 Google Sheets"
            elif 'folder' in mime_type:
                folder_count += 1
                type_display = "📁 文件夹"
                size = None  # 文件夹没有大小
            else:
                other_count += 1
                type_display = mime_type.split('.')[-1] if '.' in mime_type else mime_type[:23]
            
            size_display = format_file_size(int(size)) if size else "N/A"
            
            # 累计总大小
            if size:
                total_size += int(size)
            
            print(f"{name:<40} {type_display:<25} {size_display:<12} {created_time:<20} {modified_time:<20}")
            
            # 如果是Google Sheets，显示文件ID（方便删除）
            if 'spreadsheet' in mime_type:
                print(f"    📋 ID: {file_id}")
        
        print("=" * 120)
        print(f"\n📈 统计信息:")
        print(f"  📊 Google Sheets: {spreadsheet_count} 个")
        print(f"  📁 文件夹: {folder_count} 个")
        print(f"  📄 其他文件: {other_count} 个")
        print(f"  💾 总大小: {format_file_size(total_size)}")
        
        # 显示最大的几个文件
        files_with_size = [f for f in files if f.get('size')]
        if files_with_size:
            files_with_size.sort(key=lambda x: int(x.get('size', 0)), reverse=True)
            print(f"\n🔝 最大的5个文件:")
            for i, file in enumerate(files_with_size[:5]):
                name = file.get('name', '未知')
                size = format_file_size(int(file.get('size', 0)))
                file_id = file.get('id', '')
                print(f"  {i+1}. {name} - {size}")
                print(f"     ID: {file_id}")
        
        # 显示删除建议
        print(f"\n💡 删除建议:")
        print(f"  1. 可以删除旧的测试文件或重复的Spreadsheet")
        print(f"  2. 使用以下命令删除文件:")
        print(f"     python3 delete_gdrive_file.py <文件ID>")
        
    except Exception as e:
        print(f"❌ 获取文件列表失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    list_gdrive_files()
