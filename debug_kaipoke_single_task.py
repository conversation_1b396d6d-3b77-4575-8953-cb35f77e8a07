#!/usr/bin/env python3
"""
调试脚本：只运行第一个任务来调试问题
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

import yaml
import asyncio
from workflows.kaipoke_form_download import async_run
from logger_config import logger

async def debug_single_task():
    """调试单个任务"""
    print("🔍 开始调试单个任务...")
    
    # 加载配置
    config_path = Path("configs/workflows.yaml")
    with open(config_path, 'r', encoding='utf-8') as f:
        all_configs = yaml.safe_load(f)

    config = all_configs.get('kaipoke_form_download')
    if not config:
        print("❌ 无法加载配置")
        return
    
    # 只保留第一个任务
    original_tasks = config.get('tasks', [])
    if not original_tasks:
        print("❌ 没有任务配置")
        return
    
    # 只运行第一个任务进行调试
    first_task = original_tasks[0]
    config['tasks'] = [first_task]
    
    print(f"🎯 调试任务: {first_task.get('task_id')} - {first_task.get('output_filename')}")
    print(f"📋 流程类型: {first_task.get('flow_type')}")
    print(f"🏢 据点: {first_task.get('element_text')}")
    print(f"🔧 模板参数: {first_task.get('template_param')}")
    
    # 运行工作流
    await async_run(config)

def main():
    """主函数"""
    try:
        asyncio.run(debug_single_task())
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断调试")
    except Exception as e:
        logger.error(f"❌ 调试失败: {e}", exc_info=True)
        print(f"❌ 调试失败: {e}")

if __name__ == "__main__":
    main()
