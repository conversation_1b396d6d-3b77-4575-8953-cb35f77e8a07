#!/usr/bin/env python3
"""
Kaipoke Tennki 通知窗口和字段激活问题修复验证脚本
解决两个核心问题：
1. 通知窗口无法正常关闭
2. 选择医疗后字段不可选择状态
"""

import asyncio
import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from logger_config import logger
from core.browser.browser_manager import browser_manager
from core.selector_executor import SelectorExecutor
from core.rpa_tools.kaipoke_login_service import kaipoke_login_with_env
from core.rpa_tools.tennki_form_engine import TennkiFormEngine


class KaipokeTennkiIssueFixer:
    """Kaipoke Tennki问题修复验证器"""
    
    def __init__(self):
        self.page = None
        self.selector_executor = None
        self.form_engine = None
        
    async def run_fix_verification(self):
        """运行修复验证"""
        logger.info("🔧 开始Kaipoke Tennki问题修复验证")
        
        try:
            # 1. 初始化组件
            await self._initialize_components()
            
            # 2. 登录Kaipoke
            await self._login_kaipoke()
            
            # 3. 导航到测试页面
            await self._navigate_to_test_page()
            
            # 4. 测试问题1：通知窗口关闭
            await self._test_notification_close()
            
            # 5. 测试问题2：字段激活
            await self._test_field_activation()
            
            # 6. 综合测试：完整流程
            await self._test_complete_workflow()
            
            logger.info("✅ 问题修复验证完成")
            
        except Exception as e:
            logger.error(f"❌ 问题修复验证失败: {e}", exc_info=True)
            raise
        finally:
            # 保持浏览器打开以便观察
            logger.info("🔍 浏览器将保持打开状态，请手动验证修复效果")
            await asyncio.sleep(30)  # 等待30秒观察
            await browser_manager.close()
    
    async def _initialize_components(self):
        """初始化组件"""
        logger.info("🔧 初始化测试组件...")
        
        # 启动浏览器
        await browser_manager.start_browser(headless=False)
        self.page = await browser_manager.get_page()
        self.selector_executor = SelectorExecutor(self.page)
        
        # 初始化MCP备份工具
        await self.selector_executor.initialize_mcp_fallback()
        
        # 创建表单引擎
        class SimplePerformanceMonitor:
            def record_processed(self): pass
            def record_failed(self): pass
        
        self.form_engine = TennkiFormEngine(
            self.selector_executor, 
            SimplePerformanceMonitor()
        )
        
        logger.info("✅ 组件初始化完成")
    
    async def _login_kaipoke(self):
        """登录Kaipoke"""
        logger.info("🔑 执行Kaipoke登录...")
        
        login_success = await kaipoke_login_with_env(
            self.page,
            'KAIPOKE_CORPORATION_ID',
            'KAIPOKE_MEMBER_LOGIN_ID', 
            'KAIPOKE_PASSWORD',
            'https://r.kaipoke.biz/kaipokebiz/login/COM020102.do'
        )
        
        if not login_success:
            raise Exception("Kaipoke登录失败")
            
        logger.info("✅ Kaipoke登录成功")
    
    async def _navigate_to_test_page(self):
        """导航到测试页面"""
        logger.info("🧭 导航到测试页面...")
        
        # 点击主菜单
        await self.selector_executor.smart_click(
            workflow="kaipoke_tennki",
            category="navigation",
            element="main_menu",
            target_text="レセプト"
        )
        
        await self.page.wait_for_load_state("load")
        
        # 选择据点（使用测试据点）
        await self.page.click('text="訪問看護/4660190861"')
        await self.page.wait_for_load_state("load")
        
        # 导航到訪問看護页面
        await self.page.evaluate("""
            () => {
                const menuItem = document.querySelector('.dropdown:nth-child(3) li:nth-of-type(2) a');
                if (menuItem) {
                    menuItem.click();
                    return true;
                }
                return false;
            }
        """)
        
        await self.page.wait_for_load_state("load")
        
        # 选择目标年月
        try:
            await self.page.select_option('#selectServiceOfferYm', label="令和7年8月")
        except Exception:
            logger.warning("⚠️ 月份选择失败，继续测试")
        
        await self.page.wait_for_load_state("load")
        logger.info("✅ 页面导航完成")
    
    async def _test_notification_close(self):
        """测试问题1：通知窗口关闭"""
        logger.info("📢 测试问题1：通知窗口关闭功能...")
        
        try:
            # 检查是否有通知窗口
            notification_count = await self.page.locator('._icon-close__bF1y_').count()
            logger.info(f"   - 检测到通知窗口数量: {notification_count}")
            
            if notification_count > 0:
                # 测试通知窗口关闭
                await self.form_engine._close_oshirase_notification_only(self.page)
                
                # 验证关闭效果
                await asyncio.sleep(2)
                remaining_count = await self.page.locator('._icon-close__bF1y_').count()
                logger.info(f"   - 关闭后剩余通知窗口: {remaining_count}")
                
                if remaining_count < notification_count:
                    logger.info("✅ 问题1修复成功：通知窗口可以正常关闭")
                else:
                    logger.warning("⚠️ 问题1修复效果不明显：通知窗口仍然存在")
            else:
                logger.info("ℹ️ 当前页面没有通知窗口，无法测试关闭功能")
                
        except Exception as e:
            logger.error(f"❌ 通知窗口关闭测试失败: {e}")
    
    async def _test_field_activation(self):
        """测试问题2：字段激活功能"""
        logger.info("🔧 测试问题2：字段激活功能...")
        
        try:
            # 先关闭可能的通知窗口
            await self.form_engine._close_oshirase_notification_only(self.page)
            
            # 点击新規追加按钮
            logger.info("🔘 点击新規追加按钮...")
            success = await self.form_engine._click_add_button_with_retry(self.page)
            
            if success:
                logger.info("✅ 新規追加按钮点击成功")
                
                # 等待表单出现
                await self.page.wait_for_selector('#registModal', timeout=10000, state='visible')
                logger.info("✅ 数据登录表单已出现")
                
                # 等待并激活字段
                await asyncio.sleep(2)
                await self.form_engine._safe_activate_form_fields(self.page)
                
                # 验证字段激活状态
                await self._verify_field_activation_status()
                
            else:
                logger.error("❌ 新規追加按钮点击失败")
                
        except Exception as e:
            logger.error(f"❌ 字段激活测试失败: {e}")
    
    async def _verify_field_activation_status(self):
        """验证字段激活状态"""
        logger.info("🔍 验证字段激活状态...")
        
        try:
            # 检查保险选择器状态
            insurance_status = await self.page.evaluate("""
                () => {
                    const results = {};
                    
                    // 检查介護保险
                    const kaigo = document.querySelector('#inPopupInsuranceDivision01');
                    results.kaigo = {
                        exists: !!kaigo,
                        disabled: kaigo ? kaigo.disabled : true,
                        visible: kaigo ? kaigo.style.display !== 'none' : false
                    };
                    
                    // 检查医療保险
                    const iryou = document.querySelector('#inPopupInsuranceDivision02');
                    results.iryou = {
                        exists: !!iryou,
                        disabled: iryou ? iryou.disabled : true,
                        visible: iryou ? iryou.style.display !== 'none' : false
                    };
                    
                    // 检查自費保险
                    const jihi = document.querySelector('#inPopupInsuranceDivision03');
                    results.jihi = {
                        exists: !!jihi,
                        disabled: jihi ? jihi.disabled : true,
                        visible: jihi ? jihi.style.display !== 'none' : false
                    };
                    
                    return results;
                }
            """)
            
            logger.info("📊 保险选择器状态检查结果:")
            for insurance_type, status in insurance_status.items():
                enabled = status['exists'] and not status['disabled'] and status['visible']
                status_text = "✅ 可选择" if enabled else "❌ 不可选择"
                logger.info(f"   - {insurance_type}保险: {status_text}")
                logger.info(f"     存在: {status['exists']}, 禁用: {status['disabled']}, 可见: {status['visible']}")
            
            # 检查是否有可用的保险选择器
            available_count = sum(1 for status in insurance_status.values() 
                                if status['exists'] and not status['disabled'] and status['visible'])
            
            if available_count >= 2:
                logger.info("✅ 问题2修复成功：保险选择器已激活，可以正常选择")
                return True
            else:
                logger.warning("⚠️ 问题2修复效果不佳：部分保险选择器仍不可用")
                return False
                
        except Exception as e:
            logger.error(f"❌ 字段激活状态验证失败: {e}")
            return False
    
    async def _test_complete_workflow(self):
        """测试完整工作流程"""
        logger.info("🔄 测试完整工作流程...")
        
        try:
            # 如果表单还没打开，重新打开
            modal_visible = await self.page.locator('#registModal').is_visible()
            if not modal_visible:
                await self.form_engine._close_oshirase_notification_only(self.page)
                await self.form_engine._click_add_button_with_retry(self.page)
                await self.page.wait_for_selector('#registModal', timeout=10000, state='visible')
                await asyncio.sleep(2)
                await self.form_engine._safe_activate_form_fields(self.page)
            
            # 测试选择医療保险
            logger.info("🏥 测试选择医療保险...")
            try:
                await self.page.click('#inPopupInsuranceDivision02', timeout=5000)
                logger.info("✅ 医療保险选择成功")
                
                # 等待页面响应
                await asyncio.sleep(2)
                
                # 检查其他字段是否可用
                await self._check_other_fields_availability()
                
            except Exception as e:
                logger.warning(f"⚠️ 医療保险选择失败: {e}")
            
            # 测试表单持久性
            await asyncio.sleep(3)
            still_visible = await self.page.locator('#registModal').is_visible()
            if still_visible:
                logger.info("✅ 数据登录窗口保持稳定显示")
            else:
                logger.error("❌ 数据登录窗口被意外关闭")
                
        except Exception as e:
            logger.error(f"❌ 完整工作流程测试失败: {e}")
    
    async def _check_other_fields_availability(self):
        """检查其他字段可用性"""
        logger.info("🔍 检查其他字段可用性...")
        
        try:
            field_status = await self.page.evaluate("""
                () => {
                    const results = {
                        selects: 0,
                        inputs: 0,
                        total_selects: 0,
                        total_inputs: 0
                    };
                    
                    // 检查下拉选择器
                    const selects = document.querySelectorAll('#registModal select');
                    results.total_selects = selects.length;
                    selects.forEach(select => {
                        if (!select.disabled && select.style.pointerEvents !== 'none') {
                            results.selects++;
                        }
                    });
                    
                    // 检查输入框
                    const inputs = document.querySelectorAll('#registModal input[type="text"], #registModal input[type="number"]');
                    results.total_inputs = inputs.length;
                    inputs.forEach(input => {
                        if (!input.disabled && input.style.pointerEvents !== 'none') {
                            results.inputs++;
                        }
                    });
                    
                    return results;
                }
            """)
            
            logger.info(f"📊 其他字段状态:")
            logger.info(f"   - 可用下拉选择器: {field_status['selects']}/{field_status['total_selects']}")
            logger.info(f"   - 可用输入框: {field_status['inputs']}/{field_status['total_inputs']}")
            
            if field_status['selects'] > 0 and field_status['inputs'] > 0:
                logger.info("✅ 其他字段也已正常激活")
            else:
                logger.warning("⚠️ 部分字段可能仍需手动激活")
                
        except Exception as e:
            logger.error(f"❌ 其他字段检查失败: {e}")


async def main():
    """主函数"""
    fixer = KaipokeTennkiIssueFixer()
    await fixer.run_fix_verification()


if __name__ == "__main__":
    print("=" * 80)
    print("🔧 Kaipoke Tennki 问题修复验证")
    print("=" * 80)
    print("修复的问题：")
    print("1. ✅ 通知窗口无法正常关闭")
    print("2. ✅ 选择医疗后字段不可选择状态")
    print("=" * 80)
    print("验证流程：")
    print("1. 🔑 登录Kaipoke系统")
    print("2. 🧭 导航到测试页面")
    print("3. 📢 测试通知窗口关闭")
    print("4. 🔧 测试字段激活功能")
    print("5. 🔄 测试完整工作流程")
    print("=" * 80)
    
    try:
        asyncio.run(main())
        print("\n🎉 修复验证完成！请查看日志了解详细结果。")
    except KeyboardInterrupt:
        print("\n⚠️ 验证被用户中断")
    except Exception as e:
        print(f"\n❌ 修复验证失败: {e}")
        sys.exit(1)