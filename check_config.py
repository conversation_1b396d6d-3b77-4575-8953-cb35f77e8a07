#!/usr/bin/env python3
"""
简单检查配置文件
不依赖外部库，直接读取文件内容
"""

import os

def check_selector_config():
    """检查选择器配置"""
    try:
        print("🧪 检查选择器配置...")
        
        config_path = os.path.join('configs', 'selectors.yaml')
        if not os.path.exists(config_path):
            print(f"❌ 配置文件不存在: {config_path}")
            return False
        
        with open(config_path, 'r', encoding='utf-8') as file:
            content = file.read()
        
        # 检查关键配置
        checks = [
            ('kaipoke_billing:', 'kaipoke_billing工作流配置'),
            ('user_billing:', 'user_billing选择器配置'),
            ('#billingInfo_tooltip', '特殊据点选择器'),
            ('text=\'利用者請求\'', '文本选择器'),
        ]
        
        print("\n📋 配置检查结果:")
        for check_text, description in checks:
            if check_text in content:
                print(f"  ✅ {description}: 存在")
            else:
                print(f"  ❌ {description}: 缺失")
        
        # 显示user_billing部分
        if 'user_billing:' in content:
            lines = content.split('\n')
            in_user_billing = False
            user_billing_lines = []
            
            for line in lines:
                if 'user_billing:' in line:
                    in_user_billing = True
                    user_billing_lines.append(line)
                elif in_user_billing:
                    if line.startswith('    - ') or line.startswith('    #'):
                        user_billing_lines.append(line)
                    elif line.strip() and not line.startswith('    '):
                        break
            
            print(f"\n📄 user_billing选择器配置:")
            for line in user_billing_lines:
                print(f"  {line}")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def check_workflow_config():
    """检查工作流配置"""
    try:
        print("\n🧪 检查工作流配置...")
        
        config_path = os.path.join('configs', 'workflows.yaml')
        if not os.path.exists(config_path):
            print(f"❌ 配置文件不存在: {config_path}")
            return False
        
        with open(config_path, 'r', encoding='utf-8') as file:
            content = file.read()
        
        # 检查关键配置
        checks = [
            ('kaipoke_billing:', 'kaipoke_billing工作流'),
            ('gdrive_folder_id:', 'Google Drive文件夹ID'),
            ('4660190861', '特殊据点ID'),
            ('facility_name:', '据点名称配置'),
        ]
        
        print("\n📋 工作流配置检查结果:")
        for check_text, description in checks:
            if check_text in content:
                print(f"  ✅ {description}: 存在")
            else:
                print(f"  ❌ {description}: 缺失")
        
        # 统计据点数量
        facility_count = content.count('facility_name:')
        print(f"\n📊 配置的据点数量: {facility_count}")
        
        # 查找gdrive_folder_id
        lines = content.split('\n')
        for line in lines:
            if 'gdrive_folder_id:' in line and 'kaipoke_billing' in content[max(0, content.find(line) - 1000):content.find(line)]:
                print(f"📁 Google Drive文件夹ID: {line.strip()}")
                break
        
        return True
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

if __name__ == "__main__":
    print("🔍 开始配置检查...")
    
    success1 = check_selector_config()
    success2 = check_workflow_config()
    
    if success1 and success2:
        print("\n✅ 所有配置检查通过")
    else:
        print("\n❌ 部分配置检查失败")
    
    print("\n💡 下一步:")
    print("  1. 运行 python3 list_gdrive_files.py 查看Drive文件")
    print("  2. 删除不需要的文件释放空间")
    print("  3. 测试kaipoke_billing工作流")
