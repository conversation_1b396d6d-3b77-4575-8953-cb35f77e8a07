#!/usr/bin/env python3
"""
测试脚本：验证kaipoke_form_download工作流配置
"""

import yaml
import sys
from pathlib import Path

def test_workflow_config():
    """测试工作流配置"""
    print("🧪 测试kaipoke_form_download工作流配置...")
    
    # 加载配置文件
    config_path = Path("configs/workflows.yaml")
    if not config_path.exists():
        print("❌ 配置文件不存在")
        return False
    
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    # 检查kaipoke_form_download配置
    if 'kaipoke_form_download' not in config:
        print("❌ kaipoke_form_download配置不存在")
        return False
    
    workflow_config = config['kaipoke_form_download']
    
    # 检查基本配置
    required_config_keys = ['intent', 'config', 'tasks']
    for key in required_config_keys:
        if key not in workflow_config:
            print(f"❌ 缺少配置项: {key}")
            return False
    
    # 检查任务配置
    tasks = workflow_config['tasks']
    print(f"📋 发现 {len(tasks)} 个任务配置")
    
    # 验证任务配置
    flow_types = {'A': 0, 'B': 0, 'C': 0, 'B+C': 0}
    required_task_keys = ['task_id', 'output_filename', 'flow_type', 'element_text']
    
    for i, task in enumerate(tasks):
        # 检查必需字段
        for key in required_task_keys:
            if key not in task:
                print(f"❌ 任务 {i+1} 缺少字段: {key}")
                return False
        
        # 统计流程类型
        flow_type = task['flow_type']
        if flow_type in flow_types:
            flow_types[flow_type] += 1
        else:
            print(f"❌ 任务 {i+1} 未知流程类型: {flow_type}")
            return False
        
        # 检查模板参数
        if flow_type in ['A', 'B', 'B+C'] and 'template_param' not in task:
            print(f"❌ 任务 {i+1} ({flow_type}类型) 缺少template_param")
            return False
    
    print(f"✅ 任务配置验证通过")
    print(f"📊 流程类型统计:")
    for flow_type, count in flow_types.items():
        print(f"   - 模板{flow_type}: {count}个任务")
    
    # 检查Google Drive配置
    gdrive_folder_id = workflow_config['config'].get('gdrive_folder_id')
    if gdrive_folder_id == "1CYNt1ew-XOFuKWf960U-CxQGy4jCXEWL":
        print("✅ Google Drive文件夹ID配置正确")
    else:
        print(f"⚠️ Google Drive文件夹ID: {gdrive_folder_id}")
    
    return True

def test_selector_config():
    """测试选择器配置"""
    print("\n🧪 测试选择器配置...")
    
    # 加载选择器配置文件
    selector_path = Path("configs/selectors.yaml")
    if not selector_path.exists():
        print("❌ 选择器配置文件不存在")
        return False
    
    with open(selector_path, 'r', encoding='utf-8') as f:
        selectors = yaml.safe_load(f)
    
    # 检查kaipoke_form_download选择器
    if 'kaipoke_form_download' not in selectors:
        print("❌ kaipoke_form_download选择器配置不存在")
        return False
    
    kfd_selectors = selectors['kaipoke_form_download']
    
    # 检查三种模板的选择器
    required_templates = ['template_a', 'template_b', 'template_c']
    for template in required_templates:
        if template not in kfd_selectors:
            print(f"❌ 缺少{template}选择器配置")
            return False
        print(f"✅ {template}选择器配置存在")
    
    return True

def main():
    """主函数"""
    print("🚀 开始测试kaipoke_form_download工作流...")
    
    success = True
    
    # 测试工作流配置
    if not test_workflow_config():
        success = False
    
    # 测试选择器配置
    if not test_selector_config():
        success = False
    
    if success:
        print("\n🎉 所有测试通过！工作流配置正确。")
        print("\n📝 配置摘要:")
        print("   - 37个任务配置完成")
        print("   - 三种操作模板(A、B、C)配置完成")
        print("   - 选择器配置完成")
        print("   - Google Drive集成配置完成")
        print("\n🚀 可以开始运行工作流了！")
        return 0
    else:
        print("\n❌ 测试失败，请检查配置。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
