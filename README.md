# Aozora自動化ワークフローシステム

CrewAIとMCPをベースにしたインテリジェントな自動化ワークフローシステムです。データ収集、処理、レポート生成など、業務自動化を効率的に実現します。

## 主な特徴
- Webデータの自動取得・ダウンロード
- Google Driveやスプレッドシートとの連携
- RPAコードに基づく精密なセレクタ制御
- 多拠点・多アカウント対応
- エラー時の自動リカバリ・エージェントフォールバック

## 技術スタック
- Python 3
- Playwright（ブラウザ自動操作）
- CrewAI（エージェント協調）
- MCP（マルチセレクタ・バックアップ）
- Google API（Drive, Sheets 連携）

## 用途例
- 介護業界の月次・日次実績レポート自動化
- 複数システムのデータ横断取得
- 定型業務の完全自動化

---

ご質問・カスタマイズ要望は開発チームまでご連絡ください。