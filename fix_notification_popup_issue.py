#!/usr/bin/env python3
"""
修复通知弹窗误关闭数据登录表单的问题
验证修复后的工作流程稳定性
"""

import asyncio
import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from logger_config import logger
from core.browser.browser_manager import browser_manager
from core.selector_executor import SelectorExecutor
from core.rpa_tools.kaipoke_login_service import kaipoke_login_with_env
from core.rpa_tools.tennki_form_engine import TennkiFormEngine


class NotificationPopupIssueFixer:
    """通知弹窗问题修复验证器"""
    
    def __init__(self):
        self.page = None
        self.selector_executor = None
        self.form_engine = None

    async def run_fix_test(self):
        """运行修复测试"""
        try:
            logger.info("🚀 开始通知弹窗问题修复测试")
            
            # 1. 初始化浏览器
            await self._initialize_browser()
            
            # 2. 登录Kaipoke
            await self._login_kaipoke()
            
            # 3. 导航到目标页面
            await self._navigate_to_target_page()
            
            # 4. 测试修复后的新規追加流程
            await self._test_fixed_add_button_flow()
            
            # 5. 测试医疗保险选择
            await self._test_medical_insurance_selection()
            
            # 6. 等待通知弹窗出现并验证表单不被关闭
            await self._test_notification_popup_handling()
            
            logger.info("✅ 通知弹窗问题修复测试完成")
            
        except Exception as e:
            logger.error(f"❌ 修复测试失败: {e}", exc_info=True)
            raise
        finally:
            await self._cleanup()

    async def _initialize_browser(self):
        """初始化浏览器"""
        logger.info("🔧 初始化浏览器...")
        await browser_manager.start_browser(headless=False)
        self.page = await browser_manager.get_page()
        self.selector_executor = SelectorExecutor(self.page)
        await self.selector_executor.initialize_mcp_fallback()
        
        # 初始化表单引擎
        from core.rpa_tools.tennki_form_engine import TennkiPerformanceMonitor
        performance_monitor = TennkiPerformanceMonitor()
        self.form_engine = TennkiFormEngine(self.selector_executor, performance_monitor)
        
        logger.info("✅ 浏览器初始化完成")

    async def _login_kaipoke(self):
        """登录Kaipoke"""
        logger.info("🔐 登录Kaipoke...")
        
        login_success = await kaipoke_login_with_env(
            self.page,
            'KAIPOKE_CORPORATION_ID',
            'KAIPOKE_MEMBER_LOGIN_ID', 
            'KAIPOKE_PASSWORD',
            'https://r.kaipoke.biz/kaipokebiz/login/COM020102.do'
        )
        
        if not login_success:
            raise Exception("Kaipoke登录失败")
            
        logger.info("✅ Kaipoke登录成功")

    async def _navigate_to_target_page(self):
        """导航到目标页面"""
        logger.info("🧭 导航到目标页面...")
        
        # 点击主菜单
        await self.selector_executor.smart_click(
            workflow="kaipoke_tennki",
            category="navigation", 
            element="main_menu",
            target_text="レセプト"
        )
        
        await self.page.wait_for_load_state("load")
        
        # 选择据点
        await self.page.click('text="訪問看護/4660190861"')
        await self.page.wait_for_load_state("load")
        
        # 导航到訪問看護页面
        await self.page.evaluate("""
            () => {
                const menuItem = document.querySelector('.dropdown:nth-child(3) li:nth-of-type(2) a');
                if (menuItem) menuItem.click();
            }
        """)
        
        await self.page.wait_for_load_state("load")
        
        # 选择月份
        try:
            await self.page.select_option('#selectServiceOfferYm', index=1)
        except Exception as e:
            logger.warning(f"月份选择失败: {e}")
        
        await self.page.wait_for_load_state("load")
        logger.info("✅ 成功导航到目标页面")

    async def _test_fixed_add_button_flow(self):
        """测试修复后的新規追加按钮流程"""
        logger.info("🧪 测试修复后的新規追加按钮流程...")
        
        # 使用修复后的点击函数
        await self.form_engine._click_add_button()
        
        # 验证表单是否正确打开
        modal_visible = await self.page.locator('#registModal').is_visible()
        if not modal_visible:
            raise Exception("数据登录表单未正确打开")
        
        logger.info("✅ 新規追加按钮点击测试成功")

    async def _test_medical_insurance_selection(self):
        """测试医疗保险选择"""
        logger.info("🏥 测试医疗保险选择...")
        
        # 点击医疗保险选项
        await self.page.click('#inPopupInsuranceDivision02')
        await self.page.wait_for_timeout(2000)
        
        # 验证医疗保险字段是否正确显示
        estimate1_visible = await self.page.locator('#inPopupEstimate1').is_visible()
        estimate2_visible = await self.page.locator('#inPopupEstimate2').is_visible()
        
        if not (estimate1_visible and estimate2_visible):
            raise Exception("医疗保险字段未正确显示")
        
        logger.info("✅ 医疗保险选择测试成功")

    async def _test_notification_popup_handling(self):
        """测试通知弹窗处理（验证表单不被误关闭）"""
        logger.info("📢 测试通知弹窗处理...")
        
        # 等待可能的通知弹窗出现
        await self.page.wait_for_timeout(5000)
        
        # 检查是否有通知弹窗
        notification_count = await self.page.locator('.modal:visible').count()
        logger.info(f"检测到 {notification_count} 个可见模态框")
        
        # 验证数据登录表单仍然存在且可见
        form_still_visible = await self.page.locator('#registModal').is_visible()
        insurance_still_visible = await self.page.locator('#inPopupInsuranceDivision02').is_visible()
        
        if not form_still_visible:
            raise Exception("❌ 数据登录表单被误关闭！")
        
        if not insurance_still_visible:
            raise Exception("❌ 保险选择器被误关闭！")
        
        logger.info("✅ 通知弹窗处理测试通过 - 数据登录表单未被误关闭")

    async def _cleanup(self):
        """清理资源"""
        try:
            await browser_manager.close()
        except:
            pass


async def main():
    """主函数"""
    fixer = NotificationPopupIssueFixer()
    await fixer.run_fix_test()


if __name__ == '__main__':
    asyncio.run(main())
