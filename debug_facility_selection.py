#!/usr/bin/env python3
"""
调试据点选择问题
验证每个据点是否使用了正确的element_text
"""

import asyncio
import sys
import os
import yaml

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from logger_config import logger
from workflows.kaipoke_tennki_refactored import MultiBrowserManager, TennkiFacilityProcessor, TennkiWorkflowManager


class FacilitySelectionDebugger:
    """据点选择调试器"""
    
    def __init__(self):
        self.multi_browser_manager = MultiBrowserManager()
        
    async def debug_facility_configs(self):
        """调试据点配置"""
        logger.info("🔍 开始调试据点配置...")
        
        # 读取配置文件
        config_path = 'configs/workflows.yaml'
        with open(config_path, 'r', encoding='utf-8') as f:
            all_configs = yaml.safe_load(f)
        
        workflow_config = all_configs.get('kaipoke_tennki_refactored')
        if not workflow_config:
            logger.error("❌ 未找到kaipoke_tennki_refactored配置")
            return
        
        facilities = workflow_config.get('facilities', [])
        logger.info(f"📋 找到 {len(facilities)} 个据点配置")
        
        # 打印每个据点的配置
        for i, facility_config in enumerate(facilities, 1):
            facility_name = facility_config.get('name')
            element_text = facility_config.get('element_text')
            spreadsheet_id = facility_config.get('spreadsheet_id')
            
            logger.info(f"据点 {i}: {facility_name}")
            logger.info(f"  element_text: {element_text}")
            logger.info(f"  spreadsheet_id: {spreadsheet_id}")
            logger.info("-" * 50)
        
        return facilities, workflow_config
    
    async def debug_facility_processor_creation(self):
        """调试据点处理器创建过程"""
        logger.info("🔍 开始调试据点处理器创建过程...")
        
        facilities, workflow_config = await self.debug_facility_configs()
        
        # 初始化多浏览器管理器
        await self.multi_browser_manager.initialize()
        
        # 为每个据点创建处理器并验证配置传递
        processors = []
        for facility_config in facilities:
            facility_name = facility_config.get('name')
            element_text = facility_config.get('element_text')
            
            logger.info(f"🏗️ 创建据点处理器: {facility_name}")
            logger.info(f"   传入的element_text: {element_text}")
            
            processor = TennkiFacilityProcessor(
                facility_config, 
                workflow_config, 
                self.multi_browser_manager
            )
            
            # 验证处理器内部的配置
            logger.info(f"   处理器内的facility_name: {processor.facility_name}")
            logger.info(f"   处理器内的facility_config: {processor.facility_config}")
            
            processors.append(processor)
            logger.info("-" * 50)
        
        # 清理
        await self.multi_browser_manager.close_all_browsers()
        
        return processors
    
    async def debug_parallel_processing_simulation(self):
        """调试并行处理模拟"""
        logger.info("🔍 开始调试并行处理模拟...")
        
        facilities, workflow_config = await self.debug_facility_configs()
        
        # 模拟TennkiWorkflowManager的并行处理逻辑
        workflow_manager = TennkiWorkflowManager(workflow_config)
        
        # 初始化
        await workflow_manager.multi_browser_manager.initialize()
        
        # 模拟_process_facilities_parallel方法
        logger.info("🚀 模拟并行处理...")
        
        tasks = []
        for facility_config in facilities:
            facility_name = facility_config.get('name')
            element_text = facility_config.get('element_text')
            
            logger.info(f"📋 准备处理据点: {facility_name}")
            logger.info(f"   使用element_text: {element_text}")
            
            # 创建模拟任务
            task = self._simulate_facility_processing(
                facility_config, 
                workflow_config, 
                workflow_manager.multi_browser_manager
            )
            tasks.append(task)
        
        # 执行并行任务（模拟）
        logger.info("⚡ 开始并行执行...")
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 统计结果
        success_count = sum(1 for r in results if r is True)
        logger.info(f"📊 并行处理模拟完成: 成功 {success_count}/{len(results)} 个据点")
        
        # 清理
        await workflow_manager._cleanup_all_resources()
    
    async def _simulate_facility_processing(self, facility_config, workflow_config, multi_browser_manager):
        """模拟据点处理过程"""
        facility_name = facility_config.get('name')
        element_text = facility_config.get('element_text')
        
        try:
            logger.info(f"🏢 模拟处理据点: {facility_name}")
            logger.info(f"   实际使用的element_text: {element_text}")
            
            # 创建据点处理器
            processor = TennkiFacilityProcessor(
                facility_config, 
                workflow_config, 
                multi_browser_manager
            )
            
            # 验证处理器接收到的配置
            actual_element_text = processor.facility_config.get('element_text')
            logger.info(f"   处理器接收到的element_text: {actual_element_text}")
            
            if actual_element_text != element_text:
                logger.error(f"❌ 配置传递错误！期望: {element_text}, 实际: {actual_element_text}")
                return False
            else:
                logger.info(f"✅ 配置传递正确: {actual_element_text}")
                return True
                
        except Exception as e:
            logger.error(f"❌ 据点 {facility_name} 模拟处理失败: {e}")
            return False
    
    async def debug_navigate_to_facility_logic(self):
        """调试导航到据点的逻辑"""
        logger.info("🔍 开始调试导航到据点的逻辑...")
        
        facilities, workflow_config = await self.debug_facility_configs()
        
        # 为每个据点验证导航逻辑
        for facility_config in facilities:
            facility_name = facility_config.get('name')
            element_text = facility_config.get('element_text')
            
            logger.info(f"🧭 验证据点导航逻辑: {facility_name}")
            logger.info(f"   应该选择的element_text: {element_text}")
            
            # 模拟TennkiFacilityManager.navigate_to_facility调用
            logger.info(f"   模拟调用: navigate_to_facility('{element_text}', '{facility_name}')")
            logger.info(f"   选择器将查找: text=\"{element_text}\"")
            logger.info("-" * 50)


async def main():
    """主调试函数"""
    logger.info("🚀 开始Kaipoke Tennki据点选择调试")
    
    debugger = FacilitySelectionDebugger()
    
    try:
        # 调试1：据点配置
        logger.info("=" * 60)
        logger.info("调试1：据点配置验证")
        logger.info("=" * 60)
        await debugger.debug_facility_configs()
        
        # 调试2：据点处理器创建
        logger.info("=" * 60)
        logger.info("调试2：据点处理器创建过程")
        logger.info("=" * 60)
        await debugger.debug_facility_processor_creation()
        
        # 调试3：并行处理模拟
        logger.info("=" * 60)
        logger.info("调试3：并行处理模拟")
        logger.info("=" * 60)
        await debugger.debug_parallel_processing_simulation()
        
        # 调试4：导航逻辑验证
        logger.info("=" * 60)
        logger.info("调试4：导航逻辑验证")
        logger.info("=" * 60)
        await debugger.debug_navigate_to_facility_logic()
        
        logger.info("=" * 60)
        logger.info("调试总结")
        logger.info("=" * 60)
        logger.info("🎉 所有调试步骤完成！请检查上述日志以确认配置传递是否正确")
            
    except Exception as e:
        logger.error(f"❌ 调试过程中发生异常: {e}", exc_info=True)


if __name__ == "__main__":
    asyncio.run(main())
