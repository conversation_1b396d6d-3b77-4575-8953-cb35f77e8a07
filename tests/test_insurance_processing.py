"""
Kaipoke Tennki 保险处理功能测试脚本

测试三种保险类型的字段映射和处理逻辑：
- 介護保险 (Kaigo)
- 医疗保险 (Iryou) 
- 自费保险 (<PERSON><PERSON>)

创建时间: 2025-07-24
作者: Aozora自动化工作流
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.rpa_tools.insurance_config import (
    InsuranceType, 
    InsuranceConfigManager, 
    InsuranceFieldMappings
)
from logger_config import logger


class InsuranceProcessingTester:
    """保险处理功能测试器"""
    
    def __init__(self):
        self.insurance_manager = InsuranceConfigManager()
        self.test_results = []
    
    def test_insurance_config_loading(self):
        """测试保险配置加载"""
        logger.info("🧪 测试保险配置加载...")
        
        try:
            # 测试介護保险配置
            kaigo_config = self.insurance_manager.get_config(InsuranceType.KAIGO)
            assert kaigo_config is not None, "介護保险配置加载失败"
            assert kaigo_config.selector == "#inPopupInsuranceDivision01", "介護保险选择器错误"
            logger.info("✅ 介護保险配置加载成功")
            
            # 测试医疗保险配置
            iryou_config = self.insurance_manager.get_config(InsuranceType.IRYOU)
            assert iryou_config is not None, "医疗保险配置加载失败"
            assert iryou_config.selector == "#inPopupInsuranceDivision02", "医疗保险选择器错误"
            logger.info("✅ 医疗保险配置加载成功")
            
            # 测试自费保险配置
            jihi_config = self.insurance_manager.get_config(InsuranceType.JIHI)
            assert jihi_config is not None, "自费保险配置加载失败"
            assert jihi_config.selector == "#inPopupInsuranceDivision03", "自费保险选择器错误"
            logger.info("✅ 自费保险配置加载成功")
            
            self.test_results.append(("保险配置加载", True, "所有保险类型配置加载成功"))
            
        except Exception as e:
            logger.error(f"❌ 保险配置加载测试失败: {e}")
            self.test_results.append(("保险配置加载", False, str(e)))
    
    def test_field_mapping_accuracy(self):
        """测试字段映射准确性"""
        logger.info("🧪 测试字段映射准确性...")
        
        try:
            # 测试介護保险字段映射
            kaigo_service_kind = self.insurance_manager.get_field_selector(
                InsuranceType.KAIGO, "service_kind"
            )
            assert kaigo_service_kind == "#inPopupServiceKindId", "介護保险サービス種類字段映射错误"
            
            # 测试医疗保险字段映射
            iryou_service_division = self.insurance_manager.get_field_selector(
                InsuranceType.IRYOU, "service_division"
            )
            assert iryou_service_division == "#inPopupEstimate1", "医疗保险サービス区分字段映射错误"
            
            iryou_basic_fee = self.insurance_manager.get_field_selector(
                InsuranceType.IRYOU, "basic_medical_fee"
            )
            assert iryou_basic_fee == "#inPopupEstimate2", "医疗保险基本療養費字段映射错误"
            
            iryou_staff_qual = self.insurance_manager.get_field_selector(
                InsuranceType.IRYOU, "staff_qualification"
            )
            assert iryou_staff_qual == "#inPopupEstimate3", "医疗保险職員資格字段映射错误"
            
            # 测试自费保险字段映射
            jihi_category = self.insurance_manager.get_field_selector(
                InsuranceType.JIHI, "category"
            )
            assert jihi_category == "#inPopupInsuranceOtherCategoryName", "自费保险分類字段映射错误"
            
            jihi_amount = self.insurance_manager.get_field_selector(
                InsuranceType.JIHI, "amount"
            )
            assert jihi_amount == "#inPopupAmount", "自费保险金額字段映射错误"
            
            logger.info("✅ 所有字段映射验证通过")
            self.test_results.append(("字段映射准确性", True, "所有字段映射正确"))
            
        except Exception as e:
            logger.error(f"❌ 字段映射测试失败: {e}")
            self.test_results.append(("字段映射准确性", False, str(e)))
    
    def test_required_fields_validation(self):
        """测试必填字段验证"""
        logger.info("🧪 测试必填字段验证...")
        
        try:
            # 测试介護保险必填字段
            kaigo_required = self.insurance_manager.get_required_fields(InsuranceType.KAIGO)
            expected_kaigo = ["service_kind", "estimate1", "start_time", "end_time", "plan_achievement"]
            assert set(kaigo_required) == set(expected_kaigo), "介護保险必填字段不匹配"
            
            # 测试医疗保险必填字段
            iryou_required = self.insurance_manager.get_required_fields(InsuranceType.IRYOU)
            expected_iryou = ["service_division", "basic_medical_fee", "staff_qualification", "start_time", "end_time", "plan_achievement"]
            assert set(iryou_required) == set(expected_iryou), "医疗保险必填字段不匹配"
            
            # 测试自费保险必填字段
            jihi_required = self.insurance_manager.get_required_fields(InsuranceType.JIHI)
            expected_jihi = ["category", "service_content", "amount", "start_time", "end_time", "plan_achievement"]
            assert set(jihi_required) == set(expected_jihi), "自费保险必填字段不匹配"
            
            logger.info("✅ 所有必填字段验证通过")
            self.test_results.append(("必填字段验证", True, "所有必填字段配置正确"))
            
        except Exception as e:
            logger.error(f"❌ 必填字段验证失败: {e}")
            self.test_results.append(("必填字段验证", False, str(e)))
    
    def test_insurance_type_parsing(self):
        """测试保险类型解析"""
        logger.info("🧪 测试保险类型解析...")
        
        try:
            # 测试中文保险类型解析
            assert InsuranceConfigManager.parse_insurance_type("介護") == InsuranceType.KAIGO
            assert InsuranceConfigManager.parse_insurance_type("医療") == InsuranceType.IRYOU
            assert InsuranceConfigManager.parse_insurance_type("自費") == InsuranceType.JIHI
            
            # 测试英文保险类型解析
            assert InsuranceConfigManager.parse_insurance_type("kaigo") == InsuranceType.KAIGO
            assert InsuranceConfigManager.parse_insurance_type("iryou") == InsuranceType.IRYOU
            assert InsuranceConfigManager.parse_insurance_type("jihi") == InsuranceType.JIHI
            
            # 测试无效类型
            assert InsuranceConfigManager.parse_insurance_type("invalid") is None
            
            logger.info("✅ 保险类型解析验证通过")
            self.test_results.append(("保险类型解析", True, "保险类型解析功能正常"))
            
        except Exception as e:
            logger.error(f"❌ 保险类型解析测试失败: {e}")
            self.test_results.append(("保险类型解析", False, str(e)))
    
    def run_all_tests(self):
        """运行所有测试"""
        logger.info("🚀 开始运行保险处理功能测试...")
        
        # 运行各项测试
        self.test_insurance_config_loading()
        self.test_field_mapping_accuracy()
        self.test_required_fields_validation()
        self.test_insurance_type_parsing()
        
        # 输出测试结果
        self.print_test_results()
    
    def print_test_results(self):
        """打印测试结果"""
        logger.info("📊 测试结果汇总:")
        logger.info("=" * 60)
        
        passed = 0
        failed = 0
        
        for test_name, success, message in self.test_results:
            status = "✅ PASS" if success else "❌ FAIL"
            logger.info(f"{status} | {test_name:<20} | {message}")
            
            if success:
                passed += 1
            else:
                failed += 1
        
        logger.info("=" * 60)
        logger.info(f"总计: {len(self.test_results)} 项测试")
        logger.info(f"通过: {passed} 项")
        logger.info(f"失败: {failed} 项")
        
        if failed == 0:
            logger.info("🎉 所有测试通过！保险处理功能正常工作。")
        else:
            logger.warning(f"⚠️ 有 {failed} 项测试失败，请检查相关功能。")


def main():
    """主函数"""
    tester = InsuranceProcessingTester()
    tester.run_all_tests()


if __name__ == "__main__":
    main()
