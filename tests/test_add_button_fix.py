"""
Kaipoke Ten<PERSON><PERSON> 新規追加按钮修复测试脚本

测试新規追加按钮的窗口消失和disabled问题修复

创建时间: 2025-07-24
作者: Aozora自动化工作流
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from logger_config import logger


class AddButtonFixTester:
    """新規追加按钮修复测试器"""
    
    def __init__(self):
        self.test_results = []
    
    def test_function_existence(self):
        """测试新增函数是否存在"""
        logger.info("🧪 测试新增函数是否存在...")
        
        try:
            from core.rpa_tools.tennki_form_engine import TennkiFormEngine
            
            # 检查新增的方法是否存在
            required_methods = [
                '_check_existing_form_window',
                '_smart_popup_cleanup', 
                '_click_add_button_with_retry',
                '_wait_and_activate_form',
                '_force_activate_all_form_fields',
                '_verify_form_fields_ready',
                '_attempt_form_recovery'
            ]
            
            missing_methods = []
            for method_name in required_methods:
                if not hasattr(TennkiFormEngine, method_name):
                    missing_methods.append(method_name)
            
            if missing_methods:
                logger.error(f"❌ 缺少方法: {missing_methods}")
                self.test_results.append(("函数存在性", False, f"缺少方法: {missing_methods}"))
                return False
            
            logger.info("✅ 所有新增方法都存在")
            self.test_results.append(("函数存在性", True, "所有新增方法都存在"))
            return True
            
        except Exception as e:
            logger.error(f"❌ 函数存在性测试失败: {e}")
            self.test_results.append(("函数存在性", False, str(e)))
            return False
    
    def test_enhanced_click_logic(self):
        """测试增强的点击逻辑"""
        logger.info("🧪 测试增强的点击逻辑...")
        
        try:
            from core.rpa_tools.tennki_form_engine import TennkiFormEngine
            from core.selector_executor import SelectorExecutor
            
            # 创建模拟的SelectorExecutor
            class MockSelectorExecutor:
                def __init__(self):
                    self.page = None
            
            mock_executor = MockSelectorExecutor()
            engine = TennkiFormEngine(mock_executor)
            
            # 检查_simple_click_add_button方法是否被正确重构
            import inspect
            source = inspect.getsource(engine._simple_click_add_button)
            
            # 检查是否包含新的逻辑
            required_keywords = [
                '_check_existing_form_window',
                '_smart_popup_cleanup',
                '_click_add_button_with_retry',
                '_wait_and_activate_form',
                '_attempt_form_recovery'
            ]
            
            missing_keywords = []
            for keyword in required_keywords:
                if keyword not in source:
                    missing_keywords.append(keyword)
            
            if missing_keywords:
                logger.error(f"❌ 点击逻辑缺少关键组件: {missing_keywords}")
                self.test_results.append(("增强点击逻辑", False, f"缺少组件: {missing_keywords}"))
                return False
            
            logger.info("✅ 增强点击逻辑包含所有必要组件")
            self.test_results.append(("增强点击逻辑", True, "包含所有必要组件"))
            return True
            
        except Exception as e:
            logger.error(f"❌ 增强点击逻辑测试失败: {e}")
            self.test_results.append(("增强点击逻辑", False, str(e)))
            return False
    
    def test_form_activation_logic(self):
        """测试表单激活逻辑"""
        logger.info("🧪 测试表单激活逻辑...")
        
        try:
            from core.rpa_tools.tennki_form_engine import TennkiFormEngine
            from core.selector_executor import SelectorExecutor
            
            # 创建模拟的SelectorExecutor
            class MockSelectorExecutor:
                def __init__(self):
                    self.page = None
            
            mock_executor = MockSelectorExecutor()
            engine = TennkiFormEngine(mock_executor)
            
            # 检查表单激活方法
            import inspect
            
            # 检查_force_activate_all_form_fields方法
            activation_source = inspect.getsource(engine._force_activate_all_form_fields)
            
            # 检查是否包含关键的激活逻辑
            activation_keywords = [
                'removeAttribute',
                'disabled = false',
                'pointerEvents',
                'opacity',
                'backgroundColor'
            ]
            
            missing_activation = []
            for keyword in activation_keywords:
                if keyword not in activation_source:
                    missing_activation.append(keyword)
            
            if missing_activation:
                logger.warning(f"⚠️ 表单激活逻辑可能缺少: {missing_activation}")
            
            # 检查_verify_form_fields_ready方法
            verify_source = inspect.getsource(engine._verify_form_fields_ready)
            
            # 检查是否包含验证逻辑
            verify_keywords = [
                'insurance_radios',
                'selects_enabled', 
                'inputs_enabled',
                'disabled'
            ]
            
            missing_verify = []
            for keyword in verify_keywords:
                if keyword not in verify_source:
                    missing_verify.append(keyword)
            
            if missing_verify:
                logger.error(f"❌ 表单验证逻辑缺少: {missing_verify}")
                self.test_results.append(("表单激活逻辑", False, f"验证逻辑缺少: {missing_verify}"))
                return False
            
            logger.info("✅ 表单激活逻辑完整")
            self.test_results.append(("表单激活逻辑", True, "激活和验证逻辑完整"))
            return True
            
        except Exception as e:
            logger.error(f"❌ 表单激活逻辑测试失败: {e}")
            self.test_results.append(("表单激活逻辑", False, str(e)))
            return False
    
    def test_recovery_mechanism(self):
        """测试恢复机制"""
        logger.info("🧪 测试恢复机制...")
        
        try:
            from core.rpa_tools.tennki_form_engine import TennkiFormEngine
            from core.selector_executor import SelectorExecutor
            
            # 创建模拟的SelectorExecutor
            class MockSelectorExecutor:
                def __init__(self):
                    self.page = None
            
            mock_executor = MockSelectorExecutor()
            engine = TennkiFormEngine(mock_executor)
            
            # 检查恢复机制方法
            import inspect
            recovery_source = inspect.getsource(engine._attempt_form_recovery)
            
            # 检查是否包含恢复逻辑
            recovery_keywords = [
                'modal_exists',
                'style.display',
                '_click_add_button_with_retry',
                '_wait_and_activate_form'
            ]
            
            missing_recovery = []
            for keyword in recovery_keywords:
                if keyword not in recovery_source:
                    missing_recovery.append(keyword)
            
            if missing_recovery:
                logger.error(f"❌ 恢复机制缺少: {missing_recovery}")
                self.test_results.append(("恢复机制", False, f"缺少组件: {missing_recovery}"))
                return False
            
            logger.info("✅ 恢复机制完整")
            self.test_results.append(("恢复机制", True, "恢复逻辑完整"))
            return True
            
        except Exception as e:
            logger.error(f"❌ 恢复机制测试失败: {e}")
            self.test_results.append(("恢复机制", False, str(e)))
            return False
    
    def test_integration_with_existing_code(self):
        """测试与现有代码的集成"""
        logger.info("🧪 测试与现有代码的集成...")
        
        try:
            from core.rpa_tools.tennki_form_engine import TennkiFormEngine
            from core.selector_executor import SelectorExecutor
            
            # 创建模拟的SelectorExecutor
            class MockSelectorExecutor:
                def __init__(self):
                    self.page = None
            
            mock_executor = MockSelectorExecutor()
            engine = TennkiFormEngine(mock_executor)
            
            # 检查_wait_for_data_form_only是否使用新机制
            import inspect
            wait_source = inspect.getsource(engine._wait_for_data_form_only)
            
            if '_wait_and_activate_form' not in wait_source:
                logger.error("❌ _wait_for_data_form_only未使用新的激活机制")
                self.test_results.append(("代码集成", False, "未使用新的激活机制"))
                return False
            
            if '_attempt_form_recovery' not in wait_source:
                logger.error("❌ _wait_for_data_form_only未包含恢复机制")
                self.test_results.append(("代码集成", False, "未包含恢复机制"))
                return False
            
            logger.info("✅ 与现有代码集成完整")
            self.test_results.append(("代码集成", True, "与现有代码集成完整"))
            return True
            
        except Exception as e:
            logger.error(f"❌ 代码集成测试失败: {e}")
            self.test_results.append(("代码集成", False, str(e)))
            return False
    
    def run_all_tests(self):
        """运行所有测试"""
        logger.info("🚀 开始运行新規追加按钮修复测试...")
        
        # 运行各项测试
        tests = [
            ("函数存在性", self.test_function_existence),
            ("增强点击逻辑", self.test_enhanced_click_logic),
            ("表单激活逻辑", self.test_form_activation_logic),
            ("恢复机制", self.test_recovery_mechanism),
            ("代码集成", self.test_integration_with_existing_code)
        ]
        
        passed = 0
        failed = 0
        
        for test_name, test_func in tests:
            try:
                if test_func():
                    passed += 1
                else:
                    failed += 1
            except Exception as e:
                logger.error(f"❌ {test_name} 测试异常: {e}")
                failed += 1
        
        # 输出测试结果
        self.print_test_results(passed, failed)
    
    def print_test_results(self, passed, failed):
        """打印测试结果"""
        logger.info("📊 新規追加按钮修复测试结果:")
        logger.info("=" * 60)
        
        for test_name, success, message in self.test_results:
            status = "✅ PASS" if success else "❌ FAIL"
            logger.info(f"{status} | {test_name:<20} | {message}")
        
        logger.info("=" * 60)
        logger.info(f"总计: {len(self.test_results)} 项测试")
        logger.info(f"通过: {passed} 项")
        logger.info(f"失败: {failed} 项")
        
        if failed == 0:
            logger.info("🎉 所有测试通过！新規追加按钮修复功能正常。")
            logger.info("💡 修复内容:")
            logger.info("   - 智能窗口检测，避免重复点击")
            logger.info("   - 保护性弹窗清理，不误删数据窗口")
            logger.info("   - 多重策略点击，提高成功率")
            logger.info("   - 强制字段激活，解决disabled问题")
            logger.info("   - 智能恢复机制，处理异常情况")
        else:
            logger.warning(f"⚠️ 有 {failed} 项测试失败，请检查相关功能。")


def main():
    """主函数"""
    tester = AddButtonFixTester()
    tester.run_all_tests()


if __name__ == "__main__":
    main()
