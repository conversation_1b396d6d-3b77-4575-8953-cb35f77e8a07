"""
Kaipoke Tennki 保险处理集成测试脚本

测试实际的保险处理逻辑和字段映射功能

创建时间: 2025-07-24
作者: Aozora自动化工作流
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.rpa_tools.insurance_config import InsuranceType, InsuranceConfigManager
from logger_config import logger


class InsuranceIntegrationTester:
    """保险处理集成测试器"""
    
    def __init__(self):
        self.insurance_manager = InsuranceConfigManager()
    
    def test_kaigo_insurance_mapping(self):
        """测试介護保险字段映射"""
        logger.info("🧪 测试介護保险字段映射...")
        
        config = self.insurance_manager.get_config(InsuranceType.KAIGO)
        
        # 验证关键字段映射
        mappings = {
            "サービス種類": config.field_mappings["service_kind"].selector,
            "サービス内容": config.field_mappings["service_content"].selector,
            "単位数": config.field_mappings["unit_count"].selector,
            "開始時間": config.field_mappings["start_time"].selector,
            "終了時間": config.field_mappings["end_time"].selector,
            "予定・実績": config.field_mappings["plan_achievement"].selector,
            "登録ボタン": config.field_mappings["submit_button"].selector
        }
        
        expected_mappings = {
            "サービス種類": "#inPopupServiceKindId",
            "サービス内容": "#inPopupServiceContent_row > td:nth-child(2) > div",
            "単位数": "#inPopupUnit",
            "開始時間": "#inPopupStartHour",
            "終了時間": "#inPopupEndHour",
            "予定・実績": "#inPopupPlanAchievementsDivision02",
            "登録ボタン": "#btnRegisPop"
        }
        
        for field_name, actual_selector in mappings.items():
            expected_selector = expected_mappings[field_name]
            if actual_selector == expected_selector:
                logger.info(f"✅ 介護保险 {field_name}: {actual_selector}")
            else:
                logger.error(f"❌ 介護保险 {field_name}: 期望 {expected_selector}, 实际 {actual_selector}")
                return False
        
        return True
    
    def test_iryou_insurance_mapping(self):
        """测试医疗保险字段映射"""
        logger.info("🧪 测试医疗保险字段映射...")
        
        config = self.insurance_manager.get_config(InsuranceType.IRYOU)
        
        # 验证关键字段映射
        mappings = {
            "サービス区分": config.field_mappings["service_division"].selector,
            "基本療養費": config.field_mappings["basic_medical_fee"].selector,
            "職員資格": config.field_mappings["staff_qualification"].selector,
            "同一日訪問人数": config.field_mappings["same_day_visit_count"].selector,
            "開始時間": config.field_mappings["start_time"].selector,
            "終了時間": config.field_mappings["end_time"].selector,
            "予定・実績": config.field_mappings["plan_achievement"].selector,
            "登録ボタン": config.field_mappings["submit_button"].selector
        }
        
        expected_mappings = {
            "サービス区分": "#inPopupEstimate1",
            "基本療養費": "#inPopupEstimate2",
            "職員資格": "#inPopupEstimate3",
            "同一日訪問人数": "#inPopupEstimate4",
            "開始時間": "#inPopupStartHour",
            "終了時間": "#inPopupEndHour",
            "予定・実績": "#inPopupPlanAchievementsDivision02",
            "登録ボタン": "#btnRegisPop"
        }
        
        for field_name, actual_selector in mappings.items():
            expected_selector = expected_mappings[field_name]
            if actual_selector == expected_selector:
                logger.info(f"✅ 医疗保险 {field_name}: {actual_selector}")
            else:
                logger.error(f"❌ 医疗保险 {field_name}: 期望 {expected_selector}, 实际 {actual_selector}")
                return False
        
        return True
    
    def test_jihi_insurance_mapping(self):
        """测试自费保险字段映射"""
        logger.info("🧪 测试自费保险字段映射...")
        
        config = self.insurance_manager.get_config(InsuranceType.JIHI)
        
        # 验证关键字段映射
        mappings = {
            "分類": config.field_mappings["category"].selector,
            "サービス内容": config.field_mappings["service_content"].selector,
            "算定時間": config.field_mappings["estimation_time"].selector,
            "開始時間": config.field_mappings["start_time"].selector,
            "終了時間": config.field_mappings["end_time"].selector,
            "金額": config.field_mappings["amount"].selector,
            "予定・実績": config.field_mappings["plan_achievement"].selector,
            "登録ボタン": config.field_mappings["submit_button"].selector
        }
        
        expected_mappings = {
            "分類": "#inPopupInsuranceOtherCategoryName",
            "サービス内容": "#inPopupServiceContent_row > td:nth-child(2) > div",
            "算定時間": "#inPopupEstimationTime",
            "開始時間": "#inPopupStartHour",
            "終了時間": "#inPopupEndHour",
            "金額": "#inPopupAmount",
            "予定・実績": "#inPopupPlanAchievementsDivision02",
            "登録ボタン": "#btnRegisPop"
        }
        
        for field_name, actual_selector in mappings.items():
            expected_selector = expected_mappings[field_name]
            if actual_selector == expected_selector:
                logger.info(f"✅ 自费保险 {field_name}: {actual_selector}")
            else:
                logger.error(f"❌ 自费保险 {field_name}: 期望 {expected_selector}, 实际 {actual_selector}")
                return False
        
        return True
    
    def test_insurance_selector_mapping(self):
        """测试保险选择器映射"""
        logger.info("🧪 测试保险选择器映射...")
        
        # 测试保险选择器
        selectors = {
            "介護保险": self.insurance_manager.get_selector(InsuranceType.KAIGO),
            "医疗保险": self.insurance_manager.get_selector(InsuranceType.IRYOU),
            "自费保险": self.insurance_manager.get_selector(InsuranceType.JIHI)
        }
        
        expected_selectors = {
            "介護保险": "#inPopupInsuranceDivision01",
            "医疗保险": "#inPopupInsuranceDivision02",
            "自费保险": "#inPopupInsuranceDivision03"
        }
        
        for insurance_name, actual_selector in selectors.items():
            expected_selector = expected_selectors[insurance_name]
            if actual_selector == expected_selector:
                logger.info(f"✅ {insurance_name}选择器: {actual_selector}")
            else:
                logger.error(f"❌ {insurance_name}选择器: 期望 {expected_selector}, 实际 {actual_selector}")
                return False
        
        return True
    
    def test_processing_order(self):
        """测试处理顺序"""
        logger.info("🧪 测试字段处理顺序...")
        
        # 测试介護保险处理顺序
        kaigo_order = self.insurance_manager.get_processing_order(InsuranceType.KAIGO)
        expected_kaigo_order = ["service_kind", "service_content", "unit_count", "estimate1", "estimate2", "estimate3", 
                               "start_time", "end_time", "plan_achievement", "service_date"]
        
        if kaigo_order == expected_kaigo_order:
            logger.info("✅ 介護保险处理顺序正确")
        else:
            logger.error(f"❌ 介護保险处理顺序错误: {kaigo_order}")
            return False
        
        # 测试医疗保险处理顺序
        iryou_order = self.insurance_manager.get_processing_order(InsuranceType.IRYOU)
        expected_iryou_order = ["service_division", "basic_medical_fee", "staff_qualification", "same_day_visit_count",
                               "start_time", "end_time", "plan_achievement", "service_date"]
        
        if iryou_order == expected_iryou_order:
            logger.info("✅ 医疗保险处理顺序正确")
        else:
            logger.error(f"❌ 医疗保险处理顺序错误: {iryou_order}")
            return False
        
        # 测试自费保险处理顺序
        jihi_order = self.insurance_manager.get_processing_order(InsuranceType.JIHI)
        expected_jihi_order = ["category", "service_content", "estimation_time", "start_time", "end_time", 
                              "amount", "plan_achievement", "service_date"]
        
        if jihi_order == expected_jihi_order:
            logger.info("✅ 自费保险处理顺序正确")
        else:
            logger.error(f"❌ 自费保险处理顺序错误: {jihi_order}")
            return False
        
        return True
    
    def run_integration_tests(self):
        """运行集成测试"""
        logger.info("🚀 开始运行保险处理集成测试...")
        
        tests = [
            ("保险选择器映射", self.test_insurance_selector_mapping),
            ("介護保险字段映射", self.test_kaigo_insurance_mapping),
            ("医疗保险字段映射", self.test_iryou_insurance_mapping),
            ("自费保险字段映射", self.test_jihi_insurance_mapping),
            ("字段处理顺序", self.test_processing_order)
        ]
        
        passed = 0
        failed = 0
        
        for test_name, test_func in tests:
            try:
                if test_func():
                    logger.info(f"✅ {test_name} 测试通过")
                    passed += 1
                else:
                    logger.error(f"❌ {test_name} 测试失败")
                    failed += 1
            except Exception as e:
                logger.error(f"❌ {test_name} 测试异常: {e}")
                failed += 1
        
        # 输出测试结果
        logger.info("=" * 60)
        logger.info(f"集成测试结果: 总计 {len(tests)} 项")
        logger.info(f"通过: {passed} 项")
        logger.info(f"失败: {failed} 项")
        
        if failed == 0:
            logger.info("🎉 所有集成测试通过！保险处理功能完全正常。")
        else:
            logger.warning(f"⚠️ 有 {failed} 项集成测试失败，请检查相关功能。")


def main():
    """主函数"""
    tester = InsuranceIntegrationTester()
    tester.run_integration_tests()


if __name__ == "__main__":
    main()
