#!/usr/bin/env python3
"""
🧪 双浏览器并行测试启动脚本
快速启动针对103条记录的双浏览器并行测试

使用方法:
python run_dual_browser_test.py

测试配置:
- 浏览器数量: 2个
- 数据分配: 浏览器1(用户A-75条) + 浏览器2(用户B-28条)
- 处理方式: 真正的并行处理
- 监控: 实时性能和数据保护监控
"""

import asyncio
import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from logger_config import logger
from workflows.kaipoke_tennki_dual_browser_test import main as dual_browser_main


def print_test_info():
    """打印测试信息"""
    print("=" * 60)
    print("🧪 Kaipoke Tennki 双浏览器并行测试")
    print("=" * 60)
    print(f"📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("📊 测试配置:")
    print("   - 浏览器数量: 2个")
    print("   - 数据总量: 103条记录")
    print("   - 用户分布: 用户A(75条) + 用户B(28条)")
    print("   - 处理方式: 并行处理")
    print("   - 预期时间: 10-15分钟")
    print()
    print("🎯 测试目标:")
    print("   ✓ 验证双浏览器并行处理效果")
    print("   ✓ 测试数据分割和负载均衡")
    print("   ✓ 验证弹窗守护协调机制")
    print("   ✓ 检查数据保护在并行环境下的表现")
    print()
    print("📋 注意事项:")
    print("   - 请确保已配置正确的Spreadsheet ID")
    print("   - 测试过程中会打开2个浏览器窗口")
    print("   - 请不要手动操作浏览器窗口")
    print("   - 测试完成后会自动生成详细报告")
    print("=" * 60)
    print()


def check_prerequisites():
    """检查前置条件"""
    print("🔍 检查前置条件...")
    
    # 检查环境变量
    required_env_vars = [
        'KAIPOKE_CORPORATION_ID',
        'KAIPOKE_MEMBER_LOGIN_ID', 
        'KAIPOKE_PASSWORD'
    ]
    
    missing_vars = []
    for var in required_env_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"❌ 缺少环境变量: {', '.join(missing_vars)}")
        print("请在 .env 文件中配置这些变量")
        return False
    
    print("✅ 环境变量检查通过")
    
    # 检查关键文件
    key_files = [
        'configs/tennki_test_config.py',
        'workflows/kaipoke_tennki_dual_browser_test.py',
        'core/rpa_tools/enhanced_tennki_form_engine.py',
        'core/popup_handler/enhanced_popup_guardian.py'
    ]
    
    missing_files = []
    for file_path in key_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        print(f"❌ 缺少关键文件: {', '.join(missing_files)}")
        return False
    
    print("✅ 关键文件检查通过")
    return True


async def run_test():
    """运行测试"""
    try:
        print("🚀 启动双浏览器并行测试...")
        print("⏳ 请耐心等待，测试过程大约需要10-15分钟")
        print()
        
        # 运行双浏览器测试
        success = await dual_browser_main()
        
        print()
        print("=" * 60)
        if success:
            print("🎉 双浏览器并行测试完成！")
            print("📊 测试结果:")
            print("   ✅ 并行处理验证成功")
            print("   ✅ 数据保护机制正常")
            print("   ✅ 弹窗守护协调正常")
            print()
            print("📄 详细报告已生成，请查看:")
            print("   - dual_browser_test_report_*.md")
        else:
            print("⚠️ 双浏览器测试完成，但部分处理失败")
            print("📋 请检查日志了解详细信息")
        
        print("=" * 60)
        return success
        
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
        return False
    except Exception as e:
        print(f"\n❌ 测试执行失败: {e}")
        logger.error(f"测试执行失败: {e}", exc_info=True)
        return False


def main():
    """主函数"""
    print_test_info()
    
    # 检查前置条件
    if not check_prerequisites():
        print("❌ 前置条件检查失败，请解决上述问题后重试")
        return False
    
    print("✅ 前置条件检查通过，开始测试...")
    print()
    
    # 确认开始测试
    try:
        response = input("🤔 确定要开始双浏览器并行测试吗？(y/N): ").strip().lower()
        if response not in ['y', 'yes', '是']:
            print("❌ 测试已取消")
            return False
    except KeyboardInterrupt:
        print("\n❌ 测试已取消")
        return False
    
    # 运行测试
    success = asyncio.run(run_test())
    
    if success:
        print("\n🎯 测试建议:")
        print("   - 基于此次测试结果，双浏览器并行处理效果良好")
        print("   - 生产环境建议使用3-4个浏览器处理2000-4000条记录")
        print("   - 可以考虑部署到生产环境进行更大规模测试")
    else:
        print("\n🔧 故障排除建议:")
        print("   - 检查网络连接是否稳定")
        print("   - 确认Kaipoke账号权限正常")
        print("   - 查看详细日志分析具体错误原因")
        print("   - 如需帮助，请提供日志文件")
    
    return success


if __name__ == "__main__":
    try:
        success = main()
        exit_code = 0 if success else 1
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n👋 再见！")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 意外错误: {e}")
        sys.exit(1)