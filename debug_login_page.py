#!/usr/bin/env python3
"""
调试脚本：检查Kaipoke登录页面的实际结构
"""

import asyncio
from playwright.async_api import async_playwright
import os

async def debug_login_page():
    """检查登录页面的实际结构"""
    
    # 获取环境变量
    corporation_id = os.getenv('KAIPOKE_CORPORATION_ID')
    member_login_id = os.getenv('KAIPOKE_MEMBER_LOGIN_ID')
    password = os.getenv('KAIPOKE_PASSWORD')
    
    print(f"🔍 开始调试Kaipoke登录页面...")
    print(f"法人ID: {corporation_id}")
    print(f"メンバーログインID: {member_login_id}")
    
    async with async_playwright() as p:
        # 启动浏览器
        browser = await p.chromium.launch(headless=False)
        page = await browser.new_page()
        
        try:
            # 访问登录页面
            print("📄 访问登录页面...")
            await page.goto("https://r.kaipoke.biz/kaipokebiz/login/COM020102.do")
            await page.wait_for_load_state('networkidle')
            
            # 填充输入字段
            print("✏️ 填充输入字段...")
            await page.fill('#form\\:corporation_id', corporation_id)
            await page.fill('#form\\:member_login_id', member_login_id)
            await page.fill('#form\\:password', password)
            
            print("🔍 检查页面上的所有按钮和输入元素...")
            
            # 获取所有按钮
            buttons = await page.query_selector_all('button, input[type="button"], input[type="submit"]')
            print(f"\n📋 找到 {len(buttons)} 个按钮:")
            
            for i, button in enumerate(buttons):
                try:
                    tag_name = await button.evaluate('el => el.tagName')
                    button_type = await button.evaluate('el => el.type || "无类型"')
                    button_id = await button.evaluate('el => el.id || "无ID"')
                    button_class = await button.evaluate('el => el.className || "无类别"')
                    button_onclick = await button.evaluate('el => el.onclick ? el.onclick.toString() : "无onclick"')
                    button_text = await button.evaluate('el => el.textContent || el.value || "无文本"')
                    
                    print(f"  {i+1}. {tag_name}")
                    print(f"     类型: {button_type}")
                    print(f"     ID: {button_id}")
                    print(f"     类别: {button_class}")
                    print(f"     文本: {button_text}")
                    print(f"     onclick: {button_onclick[:100]}...")
                    print()
                    
                except Exception as e:
                    print(f"  {i+1}. 错误: {e}")
            
            # 检查特定的选择器
            print("🎯 检查特定选择器:")
            selectors_to_check = [
                '#form\\:logn_nochklogin',
                'input[type="submit"]',
                'button[type="submit"]',
                'button[onclick*="doLogin"]',
                'input[onclick*="doLogin"]',
                '[onclick*="doLogin"]'
            ]
            
            for selector in selectors_to_check:
                try:
                    element = await page.query_selector(selector)
                    if element:
                        tag_name = await element.evaluate('el => el.tagName')
                        element_id = await element.evaluate('el => el.id || "无ID"')
                        element_text = await element.evaluate('el => el.textContent || el.value || "无文本"')
                        print(f"  ✅ {selector} -> {tag_name} (ID: {element_id}, 文本: {element_text})")
                    else:
                        print(f"  ❌ {selector} -> 未找到")
                except Exception as e:
                    print(f"  ⚠️ {selector} -> 错误: {e}")
            
            # 获取整个表单的HTML
            print("\n📄 表单HTML结构:")
            form_html = await page.evaluate('''
                () => {
                    const form = document.querySelector('form');
                    return form ? form.outerHTML : "未找到表单";
                }
            ''')
            print(form_html[:1000] + "..." if len(form_html) > 1000 else form_html)
            
            print("\n⏸️ 按任意键继续...")
            input()
            
        except Exception as e:
            print(f"❌ 错误: {e}")
        
        finally:
            await browser.close()

if __name__ == "__main__":
    asyncio.run(debug_login_page())
