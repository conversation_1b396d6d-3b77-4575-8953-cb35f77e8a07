#!/usr/bin/env python3
"""
简化的修复验证脚本
验证Kaipoke Tennki表单状态检测修复是否正确实施
"""

import sys
import os

def check_file_modifications():
    """检查文件修改是否正确实施"""
    print("🔍 检查文件修改...")
    
    # 检查tennki_form_engine.py的修改
    engine_file = "core/rpa_tools/tennki_form_engine.py"
    
    if not os.path.exists(engine_file):
        print(f"❌ 文件不存在: {engine_file}")
        return False
    
    with open(engine_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查关键修改是否存在
    checks = [
        ("_is_data_entry_form_window_open", "新的状态检测函数"),
        ("检测到数据登录窗口已打开，跳过新規追加步骤", "智能跳过逻辑"),
        ("智能状态检测：检查是否已经在数据登录窗口内", "状态检测注释"),
        ("is_form_already_active = await self._is_data_entry_form_window_open", "状态检测调用")
    ]
    
    all_passed = True
    for check_text, description in checks:
        if check_text in content:
            print(f"✅ {description}: 已实施")
        else:
            print(f"❌ {description}: 未找到")
            all_passed = False
    
    return all_passed

def check_logic_flow():
    """检查逻辑流程是否正确"""
    print("\n🔄 检查逻辑流程...")
    
    engine_file = "core/rpa_tools/tennki_form_engine.py"
    
    with open(engine_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # 查找关键逻辑段落
    in_process_record = False
    found_state_check = False
    found_conditional_logic = False
    
    for i, line in enumerate(lines):
        if "_process_single_record" in line and "async def" in line:
            in_process_record = True
            continue
            
        if in_process_record:
            if "is_form_already_active = await self._is_data_entry_form_window_open" in line:
                found_state_check = True
                print(f"✅ 状态检测逻辑: 第{i+1}行")
                
            if "if not is_form_already_active:" in line:
                found_conditional_logic = True
                print(f"✅ 条件分支逻辑: 第{i+1}行")
                
            # 如果遇到下一个函数定义，停止检查
            if line.strip().startswith("async def") and "_process_single_record" not in line:
                break
    
    if found_state_check and found_conditional_logic:
        print("✅ 逻辑流程检查通过")
        return True
    else:
        print("❌ 逻辑流程检查失败")
        return False

def check_click_button_protection():
    """检查新規追加按钮点击保护"""
    print("\n🛡️ 检查新規追加按钮点击保护...")
    
    engine_file = "core/rpa_tools/tennki_form_engine.py"
    
    with open(engine_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 查找_click_add_button函数中的保护逻辑
    if "检测到数据登录窗口已打开，跳过新規追加按钮点击" in content:
        print("✅ 新規追加按钮点击保护: 已实施")
        return True
    else:
        print("❌ 新規追加按钮点击保护: 未找到")
        return False

def generate_summary():
    """生成修复总结"""
    print("\n📋 修复实施总结")
    print("=" * 50)
    
    file_check = check_file_modifications()
    logic_check = check_logic_flow()
    protection_check = check_click_button_protection()
    
    print(f"\n📊 检查结果:")
    print(f"   - 文件修改: {'✅ 通过' if file_check else '❌ 失败'}")
    print(f"   - 逻辑流程: {'✅ 通过' if logic_check else '❌ 失败'}")
    print(f"   - 点击保护: {'✅ 通过' if protection_check else '❌ 失败'}")
    
    if file_check and logic_check and protection_check:
        print("\n🎉 所有修复已正确实施！")
        print("\n📝 下一步建议:")
        print("   1. 运行实际的kaipoke_tennki工作流进行测试")
        print("   2. 监控日志输出确认问题解决")
        print("   3. 观察是否还有重复的新規追加点击")
        return True
    else:
        print("\n⚠️ 部分修复可能未正确实施，请检查代码")
        return False

def show_key_changes():
    """显示关键修改内容"""
    print("\n🔧 关键修改内容:")
    print("=" * 50)
    
    print("1. 新增状态检测函数:")
    print("   - _is_data_entry_form_window_open()")
    print("   - 精确检测数据登录模态窗口状态")
    
    print("\n2. 智能流程控制:")
    print("   - 在_process_single_record()中添加状态检测")
    print("   - 根据窗口状态决定是否执行新規追加")
    
    print("\n3. 点击保护机制:")
    print("   - 在_click_add_button()中添加保护逻辑")
    print("   - 避免在表单窗口内重复点击")
    
    print("\n4. 预期效果:")
    print("   - 消除数据登录窗口内的重复新規追加点击")
    print("   - 减少不必要的弹窗清除操作")
    print("   - 提高表单字段的稳定性")

if __name__ == "__main__":
    print("🧪 Kaipoke Tennki表单状态检测修复验证")
    print("=" * 60)
    
    # 显示关键修改
    show_key_changes()
    
    # 执行检查
    success = generate_summary()
    
    if success:
        print(f"\n✅ 修复验证完成 - 所有检查通过")
        sys.exit(0)
    else:
        print(f"\n❌ 修复验证失败 - 请检查实施")
        sys.exit(1)
