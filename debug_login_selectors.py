#!/usr/bin/env python3
"""
调试脚本：检查Kaipoke登录页面的实际HTML结构
找出为什么选择器失败
"""

import asyncio
import os
import sys
from pathlib import Path
from dotenv import load_dotenv

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 加载环境变量
load_dotenv()

from logger_config import logger
from core.browser.browser_manager import browser_manager

async def debug_login_page():
    """调试登录页面的HTML结构"""
    logger.info("🔍 开始调试Kaipoke登录页面...")
    
    try:
        # 启动浏览器
        await browser_manager.start_browser(headless=False)  # 使用可视化模式便于调试
        page = await browser_manager.get_page()
        
        # 访问登录页面
        login_url = "https://r.kaipoke.biz/kaipokebiz/login/COM020102.do"
        logger.info(f"访问登录页面: {login_url}")
        await page.goto(login_url, wait_until='networkidle', timeout=60000)
        
        # 等待页面完全加载
        await page.wait_for_timeout(3000)
        
        # 获取页面标题
        title = await page.title()
        logger.info(f"页面标题: {title}")
        
        # 获取页面URL
        current_url = page.url
        logger.info(f"当前URL: {current_url}")
        
        # 检查是否有表单元素
        logger.info("🔍 检查表单元素...")
        
        # 检查法人ID输入框
        corp_id_exists = await page.locator('#form\\:corporation_id').count()
        logger.info(f"法人ID输入框 (#form:corporation_id): {'✅ 存在' if corp_id_exists > 0 else '❌ 不存在'}")
        
        # 检查成员ID输入框
        member_id_exists = await page.locator('#form\\:member_login_id').count()
        logger.info(f"成员ID输入框 (#form:member_login_id): {'✅ 存在' if member_id_exists > 0 else '❌ 不存在'}")
        
        # 检查密码输入框
        password_exists = await page.locator('#form\\:password').count()
        logger.info(f"密码输入框 (#form:password): {'✅ 存在' if password_exists > 0 else '❌ 不存在'}")
        
        # 检查登录按钮
        login_btn_exists = await page.locator('#form\\:logn_nochklogin').count()
        logger.info(f"登录按钮 (#form:logn_nochklogin): {'✅ 存在' if login_btn_exists > 0 else '❌ 不存在'}")
        
        # 如果登录按钮不存在，查找所有可能的按钮
        if login_btn_exists == 0:
            logger.warning("❌ 主要登录按钮不存在，查找所有按钮...")
            
            # 查找所有按钮
            all_buttons = await page.locator('button, input[type="submit"], input[type="button"]').all()
            logger.info(f"页面上共找到 {len(all_buttons)} 个按钮/提交元素")
            
            for i, button in enumerate(all_buttons):
                try:
                    # 获取按钮的属性
                    tag_name = await button.evaluate('el => el.tagName')
                    button_id = await button.get_attribute('id') or 'N/A'
                    button_class = await button.get_attribute('class') or 'N/A'
                    button_type = await button.get_attribute('type') or 'N/A'
                    button_value = await button.get_attribute('value') or 'N/A'
                    button_text = await button.inner_text() or 'N/A'
                    onclick = await button.get_attribute('onclick') or 'N/A'
                    
                    logger.info(f"  按钮 {i+1}: {tag_name}")
                    logger.info(f"    ID: {button_id}")
                    logger.info(f"    Class: {button_class}")
                    logger.info(f"    Type: {button_type}")
                    logger.info(f"    Value: {button_value}")
                    logger.info(f"    Text: {button_text}")
                    logger.info(f"    OnClick: {onclick}")
                    logger.info("    ---")
                except Exception as e:
                    logger.warning(f"  按钮 {i+1}: 无法获取属性 - {e}")
        
        # 获取整个页面的HTML结构，查找所有相关元素
        logger.info("📄 获取页面HTML结构...")
        try:
            # 查找所有包含 "form:" 的元素
            form_elements = await page.locator('[id*="form:"]').all()
            logger.info(f"找到 {len(form_elements)} 个包含 'form:' 的元素:")

            for i, element in enumerate(form_elements):
                try:
                    element_id = await element.get_attribute('id')
                    element_tag = await element.evaluate('el => el.tagName')
                    element_type = await element.get_attribute('type') or 'N/A'
                    logger.info(f"  元素 {i+1}: <{element_tag}> id='{element_id}' type='{element_type}'")
                except Exception as e:
                    logger.warning(f"  元素 {i+1}: 无法获取属性 - {e}")

            # 查找整个页面的HTML
            page_html = await page.content()

            # 查找包含 "logn_nochklogin" 的部分
            if "logn_nochklogin" in page_html:
                logger.info("✅ 页面HTML中包含 'logn_nochklogin'")
                # 提取相关部分
                start_idx = page_html.find("logn_nochklogin") - 200
                end_idx = page_html.find("logn_nochklogin") + 200
                relevant_html = page_html[max(0, start_idx):end_idx]
                logger.info(f"相关HTML片段: {relevant_html}")
            else:
                logger.warning("❌ 页面HTML中不包含 'logn_nochklogin'")

            # 查找包含 "doLogin" 的部分
            if "doLogin" in page_html:
                logger.info("✅ 页面HTML中包含 'doLogin'")
                # 提取相关部分
                start_idx = page_html.find("doLogin") - 200
                end_idx = page_html.find("doLogin") + 200
                relevant_html = page_html[max(0, start_idx):end_idx]
                logger.info(f"doLogin相关HTML片段: {relevant_html}")
            else:
                logger.warning("❌ 页面HTML中不包含 'doLogin'")

        except Exception as e:
            logger.warning(f"无法获取页面HTML: {e}")
        
        # 保存页面截图用于调试
        screenshot_path = '/tmp/kaipoke_login_debug.png'
        await page.screenshot(path=screenshot_path, full_page=True)
        logger.info(f"📸 页面截图已保存: {screenshot_path}")
        
        # 保存页面HTML用于调试
        html_path = '/tmp/kaipoke_login_debug.html'
        html_content = await page.content()
        with open(html_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        logger.info(f"📄 页面HTML已保存: {html_path}")
        
        return True
        
    except Exception as e:
        logger.error(f"调试过程中发生错误: {e}", exc_info=True)
        return False
    finally:
        try:
            await browser_manager.close_browser()
        except:
            pass

async def main():
    """主函数"""
    success = await debug_login_page()
    if success:
        logger.info("🎉 调试完成！请检查日志和保存的文件")
    else:
        logger.error("❌ 调试失败")

if __name__ == "__main__":
    asyncio.run(main())
