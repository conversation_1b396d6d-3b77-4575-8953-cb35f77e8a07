#!/usr/bin/env python3
"""
监控kaipoke_tennki实施日选择问题
运行真实工作流并详细记录日期选择过程
"""

import asyncio
import logging
import sys
import os
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from main import main as run_main

# 配置详细日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(f'date_selection_monitor_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
    ]
)
logger = logging.getLogger(__name__)

async def monitor_date_selection():
    """监控实施日选择过程"""
    logger.info("🔍 开始监控kaipoke_tennki实施日选择...")
    
    try:
        # 模拟运行kaipoke_tennki工作流
        logger.info("▶️ 启动kaipoke_tennki工作流...")
        
        # 设置环境变量或参数来运行tennki工作流
        sys.argv = ['main.py', 'kaipoke_tennki']
        
        # 运行主程序
        await run_main()
        
    except KeyboardInterrupt:
        logger.info("⏹️ 用户中断监控")
    except Exception as e:
        logger.error(f"❌ 监控过程中出错: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")

def main():
    """主函数"""
    print("🎯 Kaipoke Tennki 实施日选择监控")
    print("📋 此脚本将运行真实的kaipoke_tennki工作流并监控实施日选择过程")
    print("⚠️  请确保:")
    print("   1. 已配置正确的登录凭据")
    print("   2. 有可用的测试数据")
    print("   3. 网络连接正常")
    
    confirm = input("\n是否继续? (y/N): ").strip().lower()
    if confirm != 'y':
        print("❌ 取消监控")
        return
    
    print("🚀 开始监控...")
    asyncio.run(monitor_date_selection())

if __name__ == "__main__":
    main()
