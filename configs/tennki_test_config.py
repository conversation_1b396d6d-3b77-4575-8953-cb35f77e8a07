"""
🧪 Kaipoke Tennki 测试环境配置
针对当前测试数据优化：103条记录，2个用户（75条+28条）

测试数据特征：
- 据点数量: 1个
- 总记录数: 103条
- 用户分布: 用户A(75条) + 用户B(28条)
- 预期处理时间: 约15-20分钟

生产环境预期：
- 据点数量: 多个
- 单据点记录数: 2000-4000条
- 用户数量: 50-100个
"""

from typing import Dict, Any


class TennkiTestConfig:
    """🧪 测试环境配置"""
    
    # 🧪 测试数据配置
    TEST_DATA_CONFIG = {
        'total_records': 103,
        'total_users': 2,
        'user_distribution': {
            'user_a': 75,
            'user_b': 28
        },
        'facility_count': 1
    }
    
    # 🧪 测试环境浏览器配置 - 修改为2个浏览器并行测试
    TEST_BROWSER_CONFIG = {
        'optimal_browser_count': 2,  # 使用2个浏览器测试并行效果
        'max_browsers': 2,           # 最大浏览器数限制
        'browser_allocation_strategy': 'parallel',  # 并行浏览器策略
        'headless': False,           # 测试时显示浏览器便于调试
        'timeout': 30000,            # 30秒超时
        'slow_mo': 100               # 放慢操作便于观察
    }
    
    # 🧪 测试数据分割配置 - 启用分割，每个浏览器处理一个用户
    TEST_DATA_SPLITTING = {
        'max_records_per_batch': 75,   # 最大批次记录数（用户A的75条）
        'max_users_per_batch': 1,      # 每个浏览器处理1个用户
        'enable_splitting': True,      # 启用数据分割
        'force_single_batch': False,   # 不强制单批次
        'split_by_user': True          # 按用户分割
    }
    
    # 🧪 测试弹窗配置
    TEST_POPUP_CONFIG = {
        'protection_mode': 'test',     # 测试模式
        'popup_check_interval': 1,     # 1秒检查间隔（更频繁）
        'idle_timeout': 60,            # 60秒空闲超时（更长）
        'debug_logging': True,         # 启用调试日志
        'screenshot_on_popup': True    # 弹窗时截图
    }
    
    # 🧪 测试数据保护配置
    TEST_DATA_PROTECTION = {
        'backup_interval': 3,          # 3秒备份间隔（更频繁）
        'validation_level': 'strict',  # 严格验证
        'auto_recovery': True,         # 启用自动恢复
        'field_monitoring': True,      # 启用字段监控
        'debug_mode': True             # 调试模式
    }


class TennkiProductionConfig:
    """🚀 生产环境配置（预设）"""
    
    # 🚀 生产数据配置
    PRODUCTION_DATA_CONFIG = {
        'expected_records_per_facility': 3000,  # 预期每据点记录数
        'expected_users_per_facility': 75,      # 预期每据点用户数
        'max_facilities': 10                    # 最大据点数
    }
    
    # 🚀 生产环境浏览器配置
    PRODUCTION_BROWSER_CONFIG = {
        'browser_allocation_thresholds': {
            'small_load': {'records': 500, 'users': 20, 'browsers': 2},
            'medium_load': {'records': 1500, 'users': 50, 'browsers': 3},
            'large_load': {'records': 3000, 'users': 75, 'browsers': 4},
            'xlarge_load': {'records': 5000, 'users': 100, 'browsers': 5}
        },
        'max_browsers_per_facility': 5,
        'headless': True,                # 生产环境无头模式
        'timeout': 60000,                # 60秒超时
        'slow_mo': 0                     # 生产环境全速运行
    }
    
    # 🚀 生产数据分割配置
    PRODUCTION_DATA_SPLITTING = {
        'max_records_per_batch': 800,    # 每批次最大记录数
        'max_users_per_batch': 20,       # 每批次最大用户数
        'enable_splitting': True,        # 启用数据分割
        'load_balance_factor': 0.85      # 负载均衡因子
    }
    
    # 🚀 生产弹窗配置
    PRODUCTION_POPUP_CONFIG = {
        'protection_mode': 'production',
        'popup_check_interval': 2,       # 2秒检查间隔
        'idle_timeout': 30,              # 30秒空闲超时
        'debug_logging': False,          # 关闭调试日志
        'screenshot_on_popup': False     # 关闭截图
    }
    
    # 🚀 生产数据保护配置
    PRODUCTION_DATA_PROTECTION = {
        'backup_interval': 5,            # 5秒备份间隔
        'validation_level': 'normal',    # 正常验证
        'auto_recovery': True,           # 启用自动恢复
        'field_monitoring': True,        # 启用字段监控
        'debug_mode': False              # 关闭调试模式
    }


class TennkiConfigManager:
    """🔧 配置管理器"""
    
    def __init__(self, environment: str = 'test'):
        self.environment = environment
        self.test_config = TennkiTestConfig()
        self.production_config = TennkiProductionConfig()
    
    def get_browser_config(self) -> Dict[str, Any]:
        """获取浏览器配置"""
        if self.environment == 'test':
            return self.test_config.TEST_BROWSER_CONFIG
        else:
            return self.production_config.PRODUCTION_BROWSER_CONFIG
    
    def get_data_splitting_config(self) -> Dict[str, Any]:
        """获取数据分割配置"""
        if self.environment == 'test':
            return self.test_config.TEST_DATA_SPLITTING
        else:
            return self.production_config.PRODUCTION_DATA_SPLITTING
    
    def get_popup_config(self) -> Dict[str, Any]:
        """获取弹窗配置"""
        if self.environment == 'test':
            return self.test_config.TEST_POPUP_CONFIG
        else:
            return self.production_config.PRODUCTION_POPUP_CONFIG
    
    def get_data_protection_config(self) -> Dict[str, Any]:
        """获取数据保护配置"""
        if self.environment == 'test':
            return self.test_config.TEST_DATA_PROTECTION
        else:
            return self.production_config.PRODUCTION_DATA_PROTECTION
    
    def calculate_optimal_browser_count(self, total_records: int, total_users: int) -> int:
        """🧠 智能计算最优浏览器数量"""
        if self.environment == 'test':
            # 🧪 测试环境：固定使用1个浏览器
            return 1
        else:
            # 🚀 生产环境：动态计算
            thresholds = self.production_config.PRODUCTION_BROWSER_CONFIG['browser_allocation_thresholds']
            
            for load_type, config in thresholds.items():
                if total_records <= config['records'] and total_users <= config['users']:
                    return config['browsers']
            
            # 超大数据量，使用最大浏览器数
            return self.production_config.PRODUCTION_BROWSER_CONFIG['max_browsers_per_facility']
    
    def get_workflow_config(self) -> Dict[str, Any]:
        """获取完整工作流配置"""
        return {
            'environment': self.environment,
            'browser': self.get_browser_config(),
            'data_splitting': self.get_data_splitting_config(),
            'popup': self.get_popup_config(),
            'data_protection': self.get_data_protection_config(),
            'optimal_browser_count': self.calculate_optimal_browser_count(
                self.test_config.TEST_DATA_CONFIG['total_records'] if self.environment == 'test' else 3000,
                self.test_config.TEST_DATA_CONFIG['total_users'] if self.environment == 'test' else 75
            )
        }


# 🧪 测试环境实例
test_config_manager = TennkiConfigManager('test')

# 🚀 生产环境实例
production_config_manager = TennkiConfigManager('production')


def get_config_for_environment(environment: str = 'test') -> TennkiConfigManager:
    """获取指定环境的配置管理器"""
    if environment == 'test':
        return test_config_manager
    else:
        return production_config_manager


# 🧪 当前测试数据的推荐配置
CURRENT_TEST_RECOMMENDATION = {
    'description': '针对103条记录、2个用户的测试数据优化配置',
    'browser_strategy': '单浏览器顺序处理',
    'expected_processing_time': '15-20分钟',
    'memory_usage': '低（单浏览器）',
    'success_rate_target': '95%+',
    'monitoring_level': '详细（便于调试）'
}

# 🚀 生产环境预期配置
PRODUCTION_RECOMMENDATION = {
    'description': '针对2000-4000条记录、50-100个用户的生产数据配置',
    'browser_strategy': '智能多浏览器并行处理',
    'expected_processing_time': '2-4小时',
    'memory_usage': '中等（3-5个浏览器）',
    'success_rate_target': '98%+',
    'monitoring_level': '标准（性能优先）'
}


if __name__ == "__main__":
    # 🧪 测试配置示例
    print("🧪 测试环境配置:")
    test_config = test_config_manager.get_workflow_config()
    for key, value in test_config.items():
        print(f"  {key}: {value}")
    
    print("\n🚀 生产环境配置:")
    prod_config = production_config_manager.get_workflow_config()
    for key, value in prod_config.items():
        print(f"  {key}: {value}")