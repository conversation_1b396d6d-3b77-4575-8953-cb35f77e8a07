"""
工作流配置加载器
从 workflows.yaml 文件加载工作流配置
"""

import os
import yaml
from typing import Dict, Any

def load_workflows() -> Dict[str, Any]:
    """加载工作流配置"""
    config_path = os.path.join(os.path.dirname(__file__), 'workflows.yaml')
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f) or {}
    except Exception as e:
        print(f"加载工作流配置失败: {e}")
        return {}

# 全局配置对象
WORKFLOWS = load_workflows()
