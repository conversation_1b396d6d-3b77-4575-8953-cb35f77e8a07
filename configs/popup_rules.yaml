# 企业级弹窗处理规则配置
# 支持多种网站和应用场景的弹窗处理

popup_rules:
  # Kaipoke平台专用规则

  # 🆕 最高优先级：aria-label关闭按钮（最高稳定性）
  - name: "kaipoke_aria_label_close_button"
    type: "aria_label"
    aria_labels:
      - "閉じる"
      - "Close"
      - "关闭"
      - "×"
    action: "aria_click"
    priority: 110
    timeout: 5000
    description: "基于aria-label的弹窗关闭机制，具有最高稳定性"
    context_safe: true

  # 🆕 最高优先级：保护数据登录表单窗口
  - name: "kaipoke_data_entry_form_protection"
    type: "protection"
    selectors:
      - "#registModal"
      - "#inPopupInsuranceDivision01"
      - "#inPopupInsuranceDivision02"
      - "#inPopupServiceKindId"
      - "#btnRegisPop"
    action: "protect"
    priority: 100
    timeout: 1000
    description: "保护Kaipoke数据登录表单窗口，防止被误清除"
    validation_selector: "#inPopupInsuranceDivision01, #inPopupInsuranceDivision02"

  - name: "kaipoke_karte_widget"
    type: "widget"
    selectors:
      - "#karte-c"
      - ".karte-widget__container"
      - ".karte-c"
      - "[id*='karte']"
    action: "hide"
    priority: 95
    timeout: 3000
    retry_count: 2
    description: "Kaipoke平台Karte客服组件"
    
  - name: "kaipoke_notification_modal"
    type: "modal"
    selectors:
      - ".modal .close"
      - ".modal-header .close"
      - ".modal-dialog .close"
    action: "close"
    priority: 90
    description: "Kaipoke通知模态框"
    
  # 🆕 保险类型处理专用规则
  - name: "kaipoke_insurance_form_protection"
    type: "protection"
    selectors:
      - ".modal:has(#inPopupInsuranceDivision01)"
      - ".modal:has(#inPopupInsuranceDivision02)"
      - ".modal-dialog:has(#inPopupServiceKindId)"
    action: "protect"
    priority: 98
    description: "保护包含保险类型选择的模态窗口"

  # 🆕 数据登录流程保护
  - name: "kaipoke_tennki_form_workflow"
    type: "protection"
    selectors:
      - "#inPopupEstimate1"
      - "#inPopupEstimate2"
      - "#inPopupEstimate3"
      - "#inPopupStartHour"
      - "#chargeStaff1JobDivision1"
    action: "protect"
    priority: 97
    description: "保护tennki数据登录工作流程中的表单元素"

  # 🆕 估算字段专用保护规则
  - name: "kaipoke_estimate_fields_protection"
    type: "protection"
    selectors:
      - "#inPopupEstimate2[disabled]"
      - "#inPopupEstimate3[disabled]"
      - "#inPopupEstimate4[disabled]"
      - "#inPopupEstimate5[disabled]"
    action: "enable_field"
    priority: 99
    timeout: 2000
    description: "专门保护和启用被禁用的估算字段"
    validation_selector: "#inPopupEstimate2:not([disabled])"

  # 🆕 新規追加按钮保护规则
  - name: "kaipoke_add_button_protection"
    type: "protection"
    selectors:
      - "#btn_area .cf:nth-child(1) :nth-child(1)"
      - "text='新規追加'"
      - "button:contains('新規追加')"
    action: "clear_blocking"
    priority: 96
    description: "保护新規追加按钮，清理遮挡元素"

  # 🆕 Kaipoke通知弹窗处理规则（用户指定关闭选择器）
  - name: "kaipoke_notification_popup"
    type: "notification"
    selectors:
      - ".modal:has-text('重要')"
      - ".modal:has-text('お知らせ')"
      - ".modal:has-text('訪問看護出張所')"
      - ".modal:has-text('料金体系')"
      - "div:has-text('お知らせはこちら')"
    close_selectors:
      - "._icon-close__bF1y_"  # 🆕 用户指定的关闭选择器
      - ".modal .close"
      - ".modal button:has-text('×')"
      - ".modal .btn-close"
      - "button:has-text('お知らせはこちら')"
    action: "close"
    priority: 98
    timeout: 3000
    description: "处理Kaipoke系统通知弹窗（用户指定关闭选择器）"
    validation_selector: ".modal:not(:visible)"

  - name: "kaipoke_important_notice"
    type: "notification"
    selectors:
      - "button:has-text('お知らせはこちら')"
      - "a:has-text('×')"
      - ".notification-close"
    action: "close"
    priority: 85
    description: "Kaipoke重要通知弹窗"

  # 通用Web应用规则
  - name: "generic_modal_close"
    type: "modal"
    selectors:
      - ".modal .btn-close"
      - ".modal .close"
      - "button[data-dismiss='modal']"
      - ".modal-close"
      - "[aria-label='Close']"
    action: "close"
    priority: 80
    description: "通用模态对话框关闭"
    
  - name: "cookie_consent_banner"
    type: "cookie_consent"
    selectors:
      - "button:has-text('同意')"
      - "button:has-text('Accept')"
      - "button:has-text('Accept All')"
      - ".cookie-accept"
      - "#cookie-accept"
      - "[data-cookie-accept]"
    action: "accept"
    priority: 75
    description: "Cookie同意横幅"
    
  - name: "notification_toast"
    type: "notification"
    selectors:
      - ".toast .close"
      - ".notification .close"
      - ".alert .close"
      - ".snackbar .close"
    action: "close"
    priority: 70
    description: "通知Toast消息"
    
  - name: "advertisement_popup"
    type: "advertisement"
    selectors:
      - ".ad-close"
      - ".advertisement .close"
      - ".popup-ad .close"
      - "button:has-text('×')"
      - ".close-ad"
    action: "close"
    priority: 65
    description: "广告弹窗"
    
  - name: "overlay_backdrop"
    type: "overlay"
    selectors:
      - ".overlay"
      - ".backdrop"
      - ".modal-backdrop"
    action: "click_outside"
    priority: 60
    description: "遮罩层背景"
    
  - name: "confirmation_dialog"
    type: "confirmation"
    selectors:
      - "button:has-text('确定')"
      - "button:has-text('OK')"
      - "button:has-text('Yes')"
      - ".confirm-btn"
    action: "accept"
    priority: 55
    description: "确认对话框"

  # 特定网站规则
  - name: "google_privacy_notice"
    type: "notification"
    selectors:
      - "button:has-text('I agree')"
      - "[aria-label='Accept all']"
    action: "accept"
    priority: 50
    description: "Google隐私通知"
    
  - name: "microsoft_cookie_banner"
    type: "cookie_consent"
    selectors:
      - "button:has-text('Accept')"
      - "#accept-cookie-notification"
    action: "accept"
    priority: 45
    description: "Microsoft Cookie横幅"

  # 紧急处理规则
  - name: "emergency_escape"
    type: "custom"
    selectors: ["body"]
    action: "press_esc"
    priority: 10
    custom_script: |
      // 紧急ESC键处理
      document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
          const modals = document.querySelectorAll('.modal, .popup, .overlay');
          modals.forEach(modal => modal.style.display = 'none');
        }
      });
    description: "紧急ESC键处理"

# 高级配置
advanced_settings:
  # 全局超时设置
  global_timeout: 10000
  
  # 重试配置
  max_retries: 3
  retry_delay: 1000
  
  # 检测间隔
  detection_interval: 2000
  
  # 统计报告
  enable_stats: true
  stats_report_interval: 300000  # 5分钟
  
  # 调试模式
  debug_mode: false
  log_all_attempts: false

# 🆕 弹窗守护配置
popup_guardian_settings:
  # 检测间隔（秒）
  check_interval: 2.0

  # 启用状态
  enabled: true

  # 上下文配置
  context_settings:
    general:
      enabled: true
      check_interval: 2.0
    tennki_form:
      enabled: true
      check_interval: 3.0  # 表单上下文检测间隔稍长
    data_entry:
      enabled: true
      check_interval: 2.5

  # 性能配置
  performance:
    max_concurrent_guardians: 10
    memory_limit_mb: 50
    cpu_usage_limit_percent: 5

  # 日志配置
  logging:
    log_guardian_start: true
    log_guardian_stop: true
    log_popup_detection: false  # 避免日志过多
    log_popup_handling: true
