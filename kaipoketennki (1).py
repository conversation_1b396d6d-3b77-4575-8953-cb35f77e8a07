import os
import time
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import InstalledAppFlow
from googleapiclient.discovery import build
from playwright.sync_api import sync_playwright

# --- 定数 ---
SCOPES = ['https://www.googleapis.com/auth/spreadsheets.readonly']
TOKEN_PATH = 'token3.json'               # トークン保存ファイル
CREDENTIALS_PATH = '/home/<USER>/gdrive_service_account.json' # OAuthクライアントIDのJSON
SPREADSHEET_ID = '1AwLxoHHt8YfM2n7hZ2mtgKlVP3sSUxfRvsFLaF_1eXQ'
SHEET_NAME = '看護記録'
TARGET_YEAR_MONTH = '202508'

def google_sheets_auth():
    creds = None
    if os.path.exists(TOKEN_PATH):
        creds = Credentials.from_authorized_user_file(TOKEN_PATH, SCOPES)
    if not creds or not creds.valid:
        flow = InstalledAppFlow.from_client_secrets_file(CREDENTIALS_PATH, SCOPES)
        creds = flow.run_local_server(port=0)
        with open(TOKEN_PATH, 'w') as token_file:
            token_file.write(creds.to_json())
    return creds

def get_sheet_data():
    creds = google_sheets_auth()
    service = build('sheets', 'v4', credentials=creds)
    sheet = service.spreadsheets()
    result = sheet.values().get(spreadsheetId=SPREADSHEET_ID, range=f'{SHEET_NAME}!A2:AK').execute()
    return result.get('values', [])

def run():
    rows = get_sheet_data()

    # --- 追加: スプレッドシートデータを表示 ---
    print("=== スプレッドシートから取得したデータ ===")
    for idx, row in enumerate(rows, start=1):
        print(f"{idx}: {row}")

    temp_value = None

    with sync_playwright() as p:
        browser = p.chromium.launch(headless=False)
        context = browser.new_context(accept_downloads=True)
        page = context.new_page()

        # --- ログイン ---
        page.goto("https://r.kaipoke.biz/kaipokebiz/login/COM020102.do")
        page.fill("#form\\:corporation_id", "113668")
        page.fill("#form\\:member_login_id", "0992143315")
        page.fill("#form\\:password", "o0318e0L")
        page.click("#form\\:logn_nochklogin")
        page.wait_for_load_state("load")

        # --- メニュー遷移 ---
        page.click(".mainCtg li:nth-of-type(1) a")
        page.wait_for_load_state("load")
        page.locator('xpath=//a[contains(text(), "訪問看護/4660190861")]').click()
        page.wait_for_load_state("load")


        page.hover('.dropdown:nth-child(3) .dropdown-toggle')
        page.click('.dropdown:nth-child(3) li:nth-of-type(2) a')
        page.wait_for_load_state("load")


        # 対象年月選択
        page.select_option('#selectServiceOfferYm', value=TARGET_YEAR_MONTH)

        page.wait_for_load_state("load")

        for idx, row in enumerate(rows, start=1):
            if len(row) <= 24:
                print(f"行 {idx} に十分なデータがありません。スキップします。")
                continue

            current_value = row[24]  # Z列（0-indexedで25番目）

            if current_value != temp_value:
                temp_value = current_value
                print(f"[行 {idx}] 利用者名選択: {current_value}")

                page.select_option('.pulldownUser .form-control', label=current_value)
                page.wait_for_timeout(2000)

            page.click('#btn_area .cf:nth-child(1) :nth-child(1)')
            page.wait_for_timeout(2000)
            if len(row) <= 34:
                print(f"[行 {idx}] データ不足（35列未満）: スキップ")

            # 保険種別に応じた処理
            if row[19] == "介護":
                page.click('#inPopupInsuranceDivision01')
                page.wait_for_timeout(3000)

                if row[26] == "":
                    page.select_option('#inPopupServiceKindId', value='4')  # 訪問看護
                    page.wait_for_timeout(200)
                    page.select_option('#inPopupEstimate1', label='通常の算定')
                    page.wait_for_timeout(200)

                    if row[27] == "正看護師":
                        page.select_option('#inPopupEstimate3', label='正看護師')
                    elif row[27] == "准看護師":
                        page.select_option('#inPopupEstimate3', label='准看護師')
                    elif row[27] in ["理学療法士", "言語聴覚士", "作業療法士"]:
                        page.select_option('#inPopupEstimate3', label='作業療法士・理学療法士・言語聴覚士')

                    page.select_option('#inPopupEstimate4', label=row[17])
                    page.select_option('#inPopupEstimate5', label=row[18])

                    if row[34] == "2":
                        page.click('#inPopupserviceContentId1')
                        page.wait_for_timeout(2000)

                    page.select_option('#inPopupStartHour', label=row[8])
                    page.select_option('#inPopupStartMinute1', label=row[9])
                    page.select_option('#inPopupStartMinute2', label=row[10])
                    page.select_option('#inPopupEndHour', label=row[12])
                    page.select_option('#inPopupEndMinute1', label=row[13])
                    page.select_option('#inPopupEndMinute2', label=row[14])

                    page.click('#inPopupPlanAchievementsDivision02')
                    page.wait_for_timeout(2000)
                    page.click(row[28])
                    page.wait_for_timeout(2000)
                    page.click('#input_staff_on .btn')
                    page.wait_for_timeout(2000)

                    if row[27] == "正看護師":
                        page.select_option('#chargeStaff1JobDivision1', label='看護師')
                    else:
                        page.select_option('#chargeStaff1JobDivision1', label=row[27])

                    page.click('#btnRegisPop')
                    page.wait_for_timeout(2000)

                else:
                    page.select_option('#inPopupServiceKindId', value='18')  # 介護予防あり

                    if row[27] == "正看護師":
                        page.select_option('#inPopupEstimate2', label='正看護師')
                    elif row[27] == "准看護師":
                        page.select_option('#inPopupEstimate2', label='准看護師')
                    elif row[27] in ["理学療法士", "言語聴覚士", "作業療法士"]:
                        page.select_option('#inPopupEstimate2', label='作業療法士・理学療法士・言語聴覚士')

                    page.select_option('#inPopupEstimate3', label=row[17])
                    page.select_option('#inPopupEstimate4', label=row[18])

                    if row[34] == "2":
                        page.click('#inPopupserviceContentId1')
                        page.wait_for_timeout(2000)

                    page.select_option('#inPopupStartHour', label=row[8])
                    page.select_option('#inPopupStartMinute1', label=row[9])
                    page.select_option('#inPopupStartMinute2', label=row[10])
                    page.select_option('#inPopupEndHour', label=row[12])
                    page.select_option('#inPopupEndMinute1', label=row[13])
                    page.select_option('#inPopupEndMinute2', label=row[14])

                    page.click('#inPopupPlanAchievementsDivision02')
                    page.wait_for_timeout(2000)
                    page.click(row[28])
                    page.wait_for_timeout(2000)
                    page.click('#input_staff_on .btn')
                    page.wait_for_timeout(2000)

                    if row[27] == "正看護師":
                        page.select_option('#chargeStaff1JobDivision1', label='看護師')
                    else:
                        page.select_option('#chargeStaff1JobDivision1', label=row[27])

                    page.click('#btnRegisPop')
                    page.wait_for_timeout(2000)

            elif row[19] == "医療":
                page.click('#inPopupInsuranceDivision02')
                page.wait_for_timeout(2000)
                page.select_option('#inPopupEstimate1', label='訪問看護')
                page.select_option('#inPopupEstimate2', label=row[32])

                if row[27] == "正看護師":
                    page.select_option('#inPopupEstimate3', label='看護師等')
                elif row[27] == "准看護師":
                    page.select_option('#inPopupEstimate3', label='准看護師')
                elif row[27] in ["理学療法士", "言語聴覚士", "作業療法士"]:
                    page.select_option('#inPopupEstimate3', label='理学療法士等')

                if row[32] == "Ⅱ":
                    page.select_option('#inPopupEstimate4', label=row[33])

                page.select_option('#inPopupStartHour', label=row[8])
                page.select_option('#inPopupStartMinute1', label=row[9])
                page.select_option('#inPopupStartMinute2', label=row[10])
                page.select_option('#inPopupEndHour', label=row[12])
                page.select_option('#inPopupEndMinute1', label=row[13])
                page.select_option('#inPopupEndMinute2', label=row[14])

                page.click('#inPopupPlanAchievementsDivision02')
                page.wait_for_timeout(2000)
                page.click(row[28])
                page.wait_for_timeout(2000)
                page.click('#input_staff_on .btn')
                page.wait_for_timeout(2000)

                if row[27] == "正看護師":
                    page.select_option('#chargeStaff1JobDivision1', label='看護師')
                else:
                    page.select_option('#chargeStaff1JobDivision1', label=row[27])

                page.click('#btnRegisPop')
                page.wait_for_timeout(2000)

            elif row[19] == "精神医療":
                page.click('#inPopupInsuranceDivision02')
                page.wait_for_timeout(3000)
                page.select_option('#inPopupEstimate1', label='精神科訪問看護')
                page.select_option('#inPopupEstimate2', label=row[32])

                if row[27] == "正看護師":
                    page.select_option('#inPopupEstimate3', label='看護師等')
                elif row[27] == "准看護師":
                    page.select_option('#inPopupEstimate3', label='准看護師')
                elif row[27] in ["理学療法士", "言語聴覚士", "作業療法士"]:
                    page.select_option('#inPopupEstimate3', label='作業療法士')

                if row[32] == "Ⅲ":
                    page.select_option('#inPopupEstimate4', label=row[33])

                page.select_option('#inPopupStartHour', label=row[8])
                page.select_option('#inPopupStartMinute1', label=row[9])
                page.select_option('#inPopupStartMinute2', label=row[10])
                page.select_option('#inPopupEndHour', label=row[12])
                page.select_option('#inPopupEndMinute1', label=row[13])
                page.select_option('#inPopupEndMinute2', label=row[14])

                page.click('#inPopupPlanAchievementsDivision02')
                page.wait_for_timeout(1000)
                page.click(row[28])
                page.wait_for_timeout(2000)
                page.click('#input_staff_on .btn')
                page.wait_for_timeout(2000)

                if row[27] == "正看護師":
                    page.select_option('#chargeStaff1JobDivision1', label='看護師')
                else:
                    page.select_option('#chargeStaff1JobDivision1', label=row[27])

                page.click('#btnRegisPop')
                page.wait_for_timeout(2000)

            
        browser.close()

if __name__ == '__main__':
    run()
